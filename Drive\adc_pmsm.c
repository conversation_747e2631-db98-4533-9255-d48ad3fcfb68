#include "adc_pmsm.h"
#include <string.h>
#include <math.h>

ADC_CurrentData_t g_adc_current_data = {0};
ADC_FilterManager_t g_adc_filter_manager = {0};  // 简化的滤波器管理器
uint8_t calibration_completed = 0;        // 校准完成标志

/********** 简化的滤波器管理器实现 **********/

/**
 * @brief 初始化滤波器管理器
 */
void ADC_FilterManager_Init(void)
{
    memset(&g_adc_filter_manager, 0, sizeof(ADC_FilterManager_t));

    // 初始化四路低通滤波器系数（母线+ABC三相）
    g_adc_filter_manager.phase_lpf.idc_lpf.alpha = LPF1_ALPHA;
    g_adc_filter_manager.phase_lpf.ia_lpf.alpha = LPF1_ALPHA;
    g_adc_filter_manager.phase_lpf.ib_lpf.alpha = LPF1_ALPHA;
    g_adc_filter_manager.phase_lpf.ic_lpf.alpha = LPF1_ALPHA;

    // 初始化四路整流滤波器系数
    g_adc_filter_manager.phase_rect.idc_rect.alpha = LPF_RECTIFIED_ALPHA;
    g_adc_filter_manager.phase_rect.ia_rect.alpha = LPF_RECTIFIED_ALPHA;
    g_adc_filter_manager.phase_rect.ib_rect.alpha = LPF_RECTIFIED_ALPHA;
    g_adc_filter_manager.phase_rect.ic_rect.alpha = LPF_RECTIFIED_ALPHA;

    // 初始化母线电流滑动平均滤波器
    g_adc_filter_manager.idc_mavg.index = 0;
    g_adc_filter_manager.idc_mavg.sum = 0.0f;

    g_adc_filter_manager.initialized = 1;
}

/********** 静态滤波器函数实现 **********/

/**
 * @brief 一阶低通滤波器处理
 * @param filter 滤波器结构体指针
 * @param input 输入值
 * @return 滤波后的输出值
 */
static float LPF_Process(LPF_t *filter, float input)
{
    float output = filter->alpha * input + (1.0f - filter->alpha) * filter->prev_output;
    filter->prev_output = output;
    return output;
}

/**
 * @brief 滑动平均滤波器处理
 * @param filter 滤波器结构体指针
 * @param input 输入值
 * @return 滤波后的输出值
 */
static float MovingAvg_Process(MovingAvg_t *filter, float input)
{
    filter->sum -= filter->buffer[filter->index];
    filter->buffer[filter->index] = input;
    filter->sum += input;

    filter->index = (filter->index + 1) % IDC_MOVING_AVG_WINDOW_SIZE;

    return filter->sum / IDC_MOVING_AVG_WINDOW_SIZE;
}

/**
 * @brief 重置滤波器管理器
 */
void ADC_FilterManager_Reset(void)
{
    if (!g_adc_filter_manager.initialized) {
        return;
    }

    // 重置三相低通滤波器
    g_adc_filter_manager.phase_lpf.ia_lpf.prev_output = 0.0f;
    g_adc_filter_manager.phase_lpf.ib_lpf.prev_output = 0.0f;
    g_adc_filter_manager.phase_lpf.ic_lpf.prev_output = 0.0f;

    // 重置三相整流滤波器
    g_adc_filter_manager.phase_rect.ia_rect.prev_output = 0.0f;
    g_adc_filter_manager.phase_rect.ib_rect.prev_output = 0.0f;
    g_adc_filter_manager.phase_rect.ic_rect.prev_output = 0.0f;

    // 重置母线电流滑动平均滤波器
    g_adc_filter_manager.idc_mavg.index = 0;
    g_adc_filter_manager.idc_mavg.sum = 0.0f;
    memset(g_adc_filter_manager.idc_mavg.buffer, 0, sizeof(g_adc_filter_manager.idc_mavg.buffer));
}

/**
 * @brief ADC电流采样系统初始化
 */
void ADC_PMSM_Init(void)
{
    // 初始化主数据结构
    memset(&g_adc_current_data, 0, sizeof(ADC_CurrentData_t));

    // 初始化计算完成标志
    g_adc_current_data.calculation_completed = 0;
    g_adc_current_data.calculation_tick = 0;

    // 初始化简化的滤波器管理器
    ADC_FilterManager_Init();

    // 执行零飘校准
    ADC_PMSM_CalibrateOffset();
}

/**
 * @brief 零飘校准函数
 */
void ADC_PMSM_CalibrateOffset(void)
{
    float offset_sum_idc = 0;
    float offset_sum[2] = {0};

    // 改为软件触发模式进行校准
    ADC_SET_SOFTWARE_TRIGGER();

    for (int sample = 0; sample < ADC_CALIBRATION_SAMPLES; sample++) {
        // 启动软件触发的ADC转换
        ADC_START_PREEMPT_CONV();

        // 等待转换完成
        while (!ADC_IS_PREEMPT_COMPLETE());

        // 清除转换完成标志
        ADC_CLEAR_PREEMPT_FLAG();

        offset_sum_idc += (float)ADC_READ_IDC_RAW();
        offset_sum[0] += (float)ADC_READ_IU_RAW();
        offset_sum[1] += (float)ADC_READ_IV_RAW();

        // 短暂延时
        for (volatile int i = 0; i < ADC_CALIBRATION_DELAY; i++);
    }

    // 恢复为硬件触发模式
    ADC_SET_SOFTWARE_TRIGGER_DISABLE();
    ADC_CLEAR_OCCS_FLAG();
    ADC_SET_HARDWARE_TRIGGER();

    // 母线电流零飘校准
    float avg_adc_idc = offset_sum_idc / ADC_CALIBRATION_SAMPLES;
    float calculated_offset_idc = ADC_TO_IDC_COEFF * avg_adc_idc + ADC_IDC_OFFSET_VOLTAGE;

    if (calculated_offset_idc > ADC_ZERO_DRIFT_THRESHOLD || calculated_offset_idc < -ADC_ZERO_DRIFT_THRESHOLD) {
        g_adc_current_data.current_offset.idc_offset = 0.0f;
    } else {
        g_adc_current_data.current_offset.idc_offset = calculated_offset_idc;
    }

    // A相电流零飘校准
    float avg_adc_ia = offset_sum[0] / ADC_CALIBRATION_SAMPLES;
    float calculated_offset_ia = ADC_TO_PHASE_COEFF * avg_adc_ia + ADC_PHASE_OFFSET_VOLTAGE;
    if (calculated_offset_ia > ADC_ZERO_DRIFT_THRESHOLD || calculated_offset_ia < -ADC_ZERO_DRIFT_THRESHOLD) {
        g_adc_current_data.current_offset.ia_offset = 0.0f;
    } else {
        g_adc_current_data.current_offset.ia_offset = calculated_offset_ia;
    }

    // B相电流零飘校准
    float avg_adc_ib = offset_sum[1] / ADC_CALIBRATION_SAMPLES;
    float calculated_offset_ib = ADC_TO_PHASE_COEFF * avg_adc_ib + ADC_PHASE_OFFSET_VOLTAGE;
    if (calculated_offset_ib > ADC_ZERO_DRIFT_THRESHOLD || calculated_offset_ib < -ADC_ZERO_DRIFT_THRESHOLD) {
        g_adc_current_data.current_offset.ib_offset = 0.0f;
    } else {
        g_adc_current_data.current_offset.ib_offset = calculated_offset_ib;
    }

    // C相电流零飘校准（预留，通常通过计算得出）
    g_adc_current_data.current_offset.ic_offset = 0.0f;

    // 标记校准完成
    calibration_completed = 1;
}

/**
 * @brief 获取校准完成状态
 * @return 1: 校准完成, 0: 校准未完成
 */
uint8_t ADC_PMSM_IsCalibrationCompleted(void)
{
    return calibration_completed;
}

/**
 * @brief 电流采样处理主函数（20kHz调用）
 * @param ia_filt_out A相滤波后电流输出指针
 * @param ib_filt_out B相滤波后电流输出指针
 * @param ic_filt_out C相滤波后电流输出指针
 * @note 在20kHz电流环中调用，采样母线电流、A、B相电流，C相通过计算得出
 */
void ADC_PMSM_ProcessCurrents(float *ia_filt_out, float *ib_filt_out, float *ic_filt_out)
{
    // 清除计算完成标志
    g_adc_current_data.calculation_completed = 0;

    // 读取ADC2抢占通道数据（母线+ABC三相）
    g_adc_current_data.adc_raw.idc_adc = ADC_READ_IDC_RAW();       // 母线电流ADC值
    g_adc_current_data.adc_raw.ia_adc = ADC_READ_IU_RAW();         // A相电流ADC值
    g_adc_current_data.adc_raw.ib_adc = ADC_READ_IV_RAW();         // B相电流ADC值
    //g_adc_current_data.adc_raw.ic_adc = 0;                         // C相未使用，设为0

    // 母线电流处理（ADC转换+零飘补偿）
    float idc_actual_temp = ADC_TO_IDC_COEFF * (float)g_adc_current_data.adc_raw.idc_adc
                          + ADC_IDC_OFFSET_VOLTAGE
                          - g_adc_current_data.current_offset.idc_offset;

    // 母线电流原始值和滤波
    g_adc_current_data.current_raw.idc_raw = idc_actual_temp;
    g_adc_current_data.current_filt.idc_filt = LPF_Process(&g_adc_filter_manager.phase_lpf.idc_lpf, idc_actual_temp);

    // ADC转电流值并进行零飘补偿
    g_adc_current_data.current_raw.ia_raw = ADC_TO_PHASE_COEFF * (float)g_adc_current_data.adc_raw.ia_adc
                                          + ADC_PHASE_OFFSET_VOLTAGE
                                          - g_adc_current_data.current_offset.ia_offset;

    g_adc_current_data.current_raw.ib_raw = ADC_TO_PHASE_COEFF * (float)g_adc_current_data.adc_raw.ib_adc
                                          + ADC_PHASE_OFFSET_VOLTAGE
                                          - g_adc_current_data.current_offset.ib_offset;

    // 计算第三相
    g_adc_current_data.current_raw.ic_raw = -(g_adc_current_data.current_raw.ia_raw + g_adc_current_data.current_raw.ib_raw);

    // ABC三相电流滤波
    g_adc_current_data.current_filt.ia_filt = LPF_Process(&g_adc_filter_manager.phase_lpf.ia_lpf, g_adc_current_data.current_raw.ia_raw);
    g_adc_current_data.current_filt.ib_filt = LPF_Process(&g_adc_filter_manager.phase_lpf.ib_lpf, g_adc_current_data.current_raw.ib_raw);
    g_adc_current_data.current_filt.ic_filt = LPF_Process(&g_adc_filter_manager.phase_lpf.ic_lpf, g_adc_current_data.current_raw.ic_raw);

    // 输出滤波后的电流值
    if (ia_filt_out != NULL) *ia_filt_out = g_adc_current_data.current_filt.ia_filt;
    if (ib_filt_out != NULL) *ib_filt_out = g_adc_current_data.current_filt.ib_filt;
    if (ic_filt_out != NULL) *ic_filt_out = g_adc_current_data.current_filt.ic_filt;

    // 更新计算完成状态
    g_adc_current_data.calculation_tick++;

    // 设置计算完成标志
    g_adc_current_data.calculation_completed = 1;
}

/**
 * @brief 处理四路整流平均值计算（2kHz调用）
 * @note 在2kHz低频任务中调用，计算母线+三相电流的整流平均值
 */
void ADC_PMSM_ProcessRectifiedAverage(void)
{
    // 计算ABC三相整流平均值
    g_adc_current_data.current_rectified.ia_rectified_avg = LPF_Process(&g_adc_filter_manager.phase_rect.ia_rect, fabsf(g_adc_current_data.current_filt.ia_filt));
    g_adc_current_data.current_rectified.ib_rectified_avg = LPF_Process(&g_adc_filter_manager.phase_rect.ib_rect, fabsf(g_adc_current_data.current_filt.ib_filt));
    g_adc_current_data.current_rectified.ic_rectified_avg = LPF_Process(&g_adc_filter_manager.phase_rect.ic_rect, fabsf(g_adc_current_data.current_filt.ic_filt));
}

