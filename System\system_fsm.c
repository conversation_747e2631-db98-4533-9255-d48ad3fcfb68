/**
 * @file system_fsm.c
 * @brief 高可靠性驱动器系统状态机实现
 * @version 1.0
 * @date 2025-08-21
 */

#include "system_fsm.h"
#include <string.h>

/********** 私有变量定义 **********/
sys_fsm_control_t g_sys_fsm = {0};

/********** 私有函数声明 **********/
static void fsm_state_power_on_init(void);
static void fsm_state_self_check(void);
static void fsm_state_idle(void);
static void fsm_state_running(void);
static void fsm_state_stopping(void);
static void fsm_state_fault(void);

static void fsm_change_state(sys_fsm_state_t new_state);
static void fsm_update_statistics(void);
static uint8_t fsm_check_timeout(uint32_t timeout_ms);
static void fsm_handle_global_fault_detection(void);

/********** 状态处理函数指针表 **********/
static void (*const state_handlers[SYS_FSM_STATE_COUNT])(void) = {
    fsm_state_power_on_init,    // SYS_FSM_STATE_POWER_ON_INIT
    fsm_state_self_check,       // SYS_FSM_STATE_SELF_CHECK
    fsm_state_idle,             // SYS_FSM_STATE_IDLE
    fsm_state_running,          // SYS_FSM_STATE_RUNNING
    fsm_state_stopping,         // SYS_FSM_STATE_STOPPING
    fsm_state_fault             // SYS_FSM_STATE_FAULT
};



/********** 公开API函数实现 **********/

/**
 * @brief 初始化系统状态机
 */
sys_status_t SYS_FSM_Init(void)
{
    // 清零控制块
    memset(&g_sys_fsm, 0, sizeof(sys_fsm_control_t));
    
    // 设置初始状态
    g_sys_fsm.current_state = SYS_FSM_STATE_POWER_ON_INIT;
    g_sys_fsm.previous_state = SYS_FSM_STATE_POWER_ON_INIT;
    g_sys_fsm.state_enter_time_ms = 0;
    g_sys_fsm.system_time_ms = 0;
    
    // 初始化命令和事件
    g_sys_fsm.pending_cmd = SYS_FSM_CMD_NONE;
    g_sys_fsm.pending_event = SYS_FSM_EVENT_NONE;
    
    // 初始化故障信息
    g_sys_fsm.last_fault_code = SYS_STATUS_OK;
    g_sys_fsm.fault_time_ms = 0;
    
    // 标记初始化完成
    g_sys_fsm.initialized = 1;
    
    return SYS_STATUS_OK;
}

/**
 * @brief 状态机主处理函数
 */
void SYS_FSM_Process(void)
{
    if (!g_sys_fsm.initialized) {
        return;
    }
    
    // 更新系统时间
    g_sys_fsm.system_time_ms++;
    g_sys_fsm.state_duration_ms = g_sys_fsm.system_time_ms - g_sys_fsm.state_enter_time_ms;
    
    // 全局故障检测
    fsm_handle_global_fault_detection();
    
    // 执行当前状态处理函数
    if (g_sys_fsm.current_state < SYS_FSM_STATE_COUNT) {
        state_handlers[g_sys_fsm.current_state]();
    }
    
    // 更新统计信息
    fsm_update_statistics();
    
    // 清除已处理的命令和事件
    g_sys_fsm.pending_cmd = SYS_FSM_CMD_NONE;
    g_sys_fsm.pending_event = SYS_FSM_EVENT_NONE;
}

/**
 * @brief 发送命令到状态机
 */
sys_status_t SYS_FSM_SendCommand(sys_fsm_cmd_t cmd)
{
    if (cmd >= SYS_FSM_CMD_COUNT) {
        return SYS_STATUS_INVALID_PARAMETER;
    }
    
    g_sys_fsm.pending_cmd = cmd;
    g_sys_fsm.statistics.cmd_received_count[cmd]++;
    
    return SYS_STATUS_OK;
}

/**
 * @brief 触发内部事件
 */
sys_status_t SYS_FSM_TriggerEvent(sys_fsm_event_t event)
{
    if (event >= SYS_FSM_EVENT_COUNT) {
        return SYS_STATUS_INVALID_PARAMETER;
    }
    
    g_sys_fsm.pending_event = event;
    g_sys_fsm.statistics.event_triggered_count[event]++;
    
    return SYS_STATUS_OK;
}

/**
 * @brief 获取当前状态
 */
sys_fsm_state_t SYS_FSM_GetCurrentState(void)
{
    return g_sys_fsm.current_state;
}



/**
 * @brief 检查状态机是否处于运行状态
 */
uint8_t SYS_FSM_IsRunning(void)
{
    return (g_sys_fsm.current_state == SYS_FSM_STATE_RUNNING) ? 1 : 0;
}

/**
 * @brief 检查状态机是否处于故障状态
 */
uint8_t SYS_FSM_IsFaulted(void)
{
    return (g_sys_fsm.current_state == SYS_FSM_STATE_FAULT) ? 1 : 0;
}

/**
 * @brief 获取最后一次故障代码
 */
sys_status_t SYS_FSM_GetLastFaultCode(void)
{
    return g_sys_fsm.last_fault_code;
}

/**
 * @brief 获取状态机统计信息
 */
const sys_fsm_statistics_t* SYS_FSM_GetStatistics(void)
{
    return &g_sys_fsm.statistics;
}

/**
 * @brief 复位状态机统计信息
 */
void SYS_FSM_ResetStatistics(void)
{
    memset(&g_sys_fsm.statistics, 0, sizeof(sys_fsm_statistics_t));
}

/********** 私有函数实现 **********/

/**
 * @brief 状态切换函数
 */
static void fsm_change_state(sys_fsm_state_t new_state)
{
    if (new_state != g_sys_fsm.current_state) {
        g_sys_fsm.previous_state = g_sys_fsm.current_state;
        g_sys_fsm.current_state = new_state;
        g_sys_fsm.state_enter_time_ms = g_sys_fsm.system_time_ms;
        g_sys_fsm.state_duration_ms = 0;

        // 更新统计信息
        g_sys_fsm.statistics.state_enter_count[new_state]++;
    }
}

/**
 * @brief 更新统计信息
 */
static void fsm_update_statistics(void)
{
    // 更新当前状态持续时间
    g_sys_fsm.statistics.state_duration_ms[g_sys_fsm.current_state] = g_sys_fsm.state_duration_ms;

    // 更新总运行时间
    g_sys_fsm.statistics.total_runtime_ms = g_sys_fsm.system_time_ms;
}

/**
 * @brief 检查超时
 */
static uint8_t fsm_check_timeout(uint32_t timeout_ms)
{
    return (g_sys_fsm.state_duration_ms >= timeout_ms) ? 1 : 0;
}

/**
 * @brief 全局故障检测处理
 */
static void fsm_handle_global_fault_detection(void)
{
    // 检查是否有故障事件或紧急停止命令
    if (g_sys_fsm.pending_event == SYS_FSM_EVENT_FAULT_DETECTED ||
        g_sys_fsm.pending_cmd == SYS_FSM_CMD_EMERGENCY_STOP) {

        // 记录故障信息
        if (g_sys_fsm.pending_event == SYS_FSM_EVENT_FAULT_DETECTED) {
            g_sys_fsm.last_fault_code = SYS_STATUS_SYSTEM_ERROR; // 可以根据具体故障设置
            g_sys_fsm.statistics.fault_count++;
        }

        g_sys_fsm.fault_time_ms = g_sys_fsm.system_time_ms;

        // 切换到故障状态（除非已经在故障状态）
        if (g_sys_fsm.current_state != SYS_FSM_STATE_FAULT) {
            fsm_change_state(SYS_FSM_STATE_FAULT);
        }
    }
}

/********** 状态处理函数实现 **********/

/**
 * @brief 上电初始化状态处理
 */
static void fsm_state_power_on_init(void)
{
    // TODO: 在这里实现上电初始化逻辑
    // 例如：
    // - 硬件初始化
    // - 外设初始化
    // - 内存初始化
    // - 配置参数加载

    // 检查初始化超时
    if (fsm_check_timeout(SYS_FSM_INIT_TIMEOUT)) {
        SYS_FSM_TriggerEvent(SYS_FSM_EVENT_INIT_FAILED);
        return;
    }

    // 处理初始化完成事件
    if (g_sys_fsm.pending_event == SYS_FSM_EVENT_INIT_COMPLETE) {
        fsm_change_state(SYS_FSM_STATE_SELF_CHECK);
    }
    // 处理初始化失败事件
    else if (g_sys_fsm.pending_event == SYS_FSM_EVENT_INIT_FAILED) {
        g_sys_fsm.last_fault_code = SYS_STATUS_INIT_ERROR;
        fsm_change_state(SYS_FSM_STATE_FAULT);
    }

    // 模拟初始化完成（实际应用中应该根据真实初始化状态判断）
    if (g_sys_fsm.state_duration_ms >= 100) { // 100ms后认为初始化完成
        SYS_FSM_TriggerEvent(SYS_FSM_EVENT_INIT_COMPLETE);
    }
}

/**
 * @brief 自检状态处理
 */
static void fsm_state_self_check(void)
{
    // TODO: 在这里实现自检逻辑
    // 例如：
    // - 功率级自检
    // - 传感器自检
    // - 通信自检
    // - 安全系统自检

    // 检查自检超时
    if (fsm_check_timeout(SYS_FSM_SELF_CHECK_TIMEOUT)) {
        SYS_FSM_TriggerEvent(SYS_FSM_EVENT_SELF_CHECK_FAIL);
        return;
    }

    // 处理自检通过事件
    if (g_sys_fsm.pending_event == SYS_FSM_EVENT_SELF_CHECK_PASS) {
        fsm_change_state(SYS_FSM_STATE_IDLE);
    }
    // 处理自检失败事件
    else if (g_sys_fsm.pending_event == SYS_FSM_EVENT_SELF_CHECK_FAIL) {
        g_sys_fsm.last_fault_code = SYS_STATUS_SELF_CHECK_ERROR;
        fsm_change_state(SYS_FSM_STATE_FAULT);
    }

    // 模拟自检完成（实际应用中应该调用真实的自检函数）
    if (g_sys_fsm.state_duration_ms >= 500) { // 500ms后认为自检完成
        SYS_FSM_TriggerEvent(SYS_FSM_EVENT_SELF_CHECK_PASS);
    }
}

/**
 * @brief 空闲状态处理
 */
static void fsm_state_idle(void)
{
    // TODO: 在这里实现空闲状态逻辑
    // 例如：
    // - 监控系统状态
    // - 处理低优先级任务
    // - 节能管理
    // - 等待启动命令

    // 处理启动命令
    if (g_sys_fsm.pending_cmd == SYS_FSM_CMD_START) {
        fsm_change_state(SYS_FSM_STATE_RUNNING);
    }
}

/**
 * @brief 运行状态处理
 */
static void fsm_state_running(void)
{
    // TODO: 在这里实现运行状态逻辑
    // 例如：
    // - 电机控制
    // - 实时监控
    // - 性能优化
    // - 安全检查

    // 处理停止命令
    if (g_sys_fsm.pending_cmd == SYS_FSM_CMD_STOP) {
        fsm_change_state(SYS_FSM_STATE_STOPPING);
    }
}

/**
 * @brief 停止状态处理
 */
static void fsm_state_stopping(void)
{
    // TODO: 在这里实现停止状态逻辑
    // 例如：
    // - 逐步减速
    // - 安全停止
    // - 状态保存
    // - 资源释放

    // 检查停止超时
    if (fsm_check_timeout(SYS_FSM_STOPPING_TIMEOUT)) {
        // 强制停止
        SYS_FSM_TriggerEvent(SYS_FSM_EVENT_MOTOR_STOPPED);
    }

    // 处理电机完全停止事件
    if (g_sys_fsm.pending_event == SYS_FSM_EVENT_MOTOR_STOPPED) {
        fsm_change_state(SYS_FSM_STATE_IDLE);
    }

    // 模拟电机停止（实际应用中应该检查真实的电机状态）
    if (g_sys_fsm.state_duration_ms >= 1000) { // 1000ms后认为电机停止
        SYS_FSM_TriggerEvent(SYS_FSM_EVENT_MOTOR_STOPPED);
    }
}

/**
 * @brief 故障状态处理
 */
static void fsm_state_fault(void)
{
    // TODO: 在这里实现故障状态逻辑
    // 例如：
    // - 故障诊断
    // - 安全保护
    // - 故障记录
    // - 等待复位命令

    // 处理故障复位命令
    if (g_sys_fsm.pending_cmd == SYS_FSM_CMD_FAULT_RESET) {
        // 检查是否满足复位条件
        if (g_sys_fsm.state_duration_ms >= SYS_FSM_FAULT_RESET_DELAY) {
            // 清除故障信息
            g_sys_fsm.last_fault_code = SYS_STATUS_OK;
            g_sys_fsm.fault_time_ms = 0;

            // 返回自检状态
            fsm_change_state(SYS_FSM_STATE_SELF_CHECK);
        }
    }
}
