/* Includes ------------------------------------------------------------------*/
#include "at32a403a_wk_config.h"
#include "usb_app.h"
#include "wk_system.h"
#include "HWInterface.h"
#include "AnoPTv8Run.h"
#include "sensor_proc.h"
#include "ad2s1212_spi.h"
#include "Sys_TimerEvent.h"
#include "SysFSM.h"
#include "adc_pmsm.h"
/* ADC1 DMA缓冲区定义 */
uint16_t adc1_dma_buffer[SENSOR_COUNT] = {0};


/**
  * @brief main function.
  * @param  none
  * @retval none
  */
int main(void)
{

/**************系统初始化 Start**************/
  /* system clock config. */
  wk_system_clock_config();

  /* config periph clock. */
  wk_periph_clock_config();

  /* init debug function. */
  wk_debug_config();

  /* nvic config. */
  wk_nvic_config();

  /* timebase config. */
  wk_timebase_init();

  /* init gpio function. */
  wk_gpio_config();

  /* init adc2 function. */
  wk_adc2_init();

  /* init adc1 function. */
  wk_adc1_init();

  /* init dma1 channel1 */
  wk_dma1_channel1_init();
  /* config dma channel transfer parameter */
  wk_dma_channel_config(DMA1_CHANNEL1,
                        (uint32_t)&ADC1->odt,
                        (uint32_t)&adc1_dma_buffer[0],
                        SENSOR_COUNT);

  /* 启用DMA传输完成中断 */
  dma_interrupt_enable(DMA1_CHANNEL1, DMA_FDT_INT, TRUE);

  dma_channel_enable(DMA1_CHANNEL1, TRUE);

  /* init spi3 function. */
  wk_spi3_init();

  /* init tmr1 function. */
  wk_tmr1_init();
  tmr_counter_enable(TMR1, FALSE);
  /* init tmr3 function. */
  wk_tmr3_init();

  /* init tmr6 function. */
  wk_tmr6_init();

  /* init usbfs function. */
  wk_usbfs_init();

  /* init wdt function. */
  wk_wdt_init();

  /* init usb app function. */
  wk_usb_app_init();

  /* add user code begin 2 */

  /* add user code end 2 */

  wk_delay_ms(10);  //等待初始化稳定
  

/**************系统初始化 End**************/

/*************用户初始化 Start**************/
  AnoPTv8HwInit();//AnoPTv8协议初始化


	// 初始化传感器处理模块
	sensor_proc_init(adc1_dma_buffer);

  //系统时基初始化
  TimerEvent_Init();

	// 初始化AD2S1210模块
	RESET_H;
	wk_delay_ms(1);
	AD2S1210_Init();

  // 初始化ADC电流采样模块
  ADC_PMSM_Init();


/*************启动配置 Start**************/
tmr_counter_enable(TMR6, true);

/**********中断启用 Start**************/
tmr_interrupt_enable(TMR6, TMR_OVF_INT, true);
dma_interrupt_enable(DMA1_CHANNEL1, DMA_FDT_INT, true);



  while(1)
  {
    //usb接收任务
    wk_usb_app_task();
    //V8协议执行
    AnoPTv8HwTrigger1ms();
    // 传感器处理任务
    sensor_proc_task();

    Timer_Tasks_Execute();

  }
}

