--cpu=Cortex-M4.fp.sp
".\objects\main.o"
".\objects\sysfsm.o"
".\objects\at32a403a_adc.o"
".\objects\at32a403a_crm.o"
".\objects\at32a403a_debug.o"
".\objects\at32a403a_dma.o"
".\objects\at32a403a_exint.o"
".\objects\at32a403a_flash.o"
".\objects\at32a403a_gpio.o"
".\objects\at32a403a_misc.o"
".\objects\at32a403a_pwc.o"
".\objects\at32a403a_spi.o"
".\objects\at32a403a_tmr.o"
".\objects\at32a403a_usb.o"
".\objects\at32a403a_wdt.o"
".\objects\startup_at32a403a.o"
".\objects\system_at32a403a.o"
"..\..\middlewares\dsp\arm_cortexM4lf_math.lib"
".\objects\usbd_core.o"
".\objects\usbd_int.o"
".\objects\usbd_sdr.o"
".\objects\at32a403a_int.o"
".\objects\at32a403a_wk_config.o"
".\objects\cdc_class.o"
".\objects\cdc_desc.o"
".\objects\usb_app.o"
".\objects\wk_system.o"
".\objects\algorithm.o"
".\objects\delay.o"
".\objects\mathbasic.o"
".\objects\motor_vectorcontrol.o"
".\objects\sys_isr_controller.o"
".\objects\sysctl_analogprocess.o"
".\objects\sysctl_globalvar.o"
".\objects\sysctl_rotorget.o"
".\objects\sysctl_sysmoore.o"
".\objects\anoptv8cmd.o"
".\objects\anoptv8framefactory.o"
".\objects\anoptv8par.o"
".\objects\anoptv8run.o"
".\objects\hwinterface.o"
".\objects\motorcmd.o"
".\objects\motordata.o"
".\objects\motorparams.o"
".\objects\ad2s1212_spi.o"
".\objects\enc_speed.o"
".\objects\adc_pmsm.o"
".\objects\pt1000_table.o"
".\objects\sensor_proc.o"
".\objects\sys_timerevent.o"
".\objects\low_speed_alarm.o"
".\objects\protection_monitor.o"
--strict --scatter ".\objects\AT32A403ARGT7_154_33.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\listings\AT32A403ARGT7_154_33.map" -o .\objects\AT32A403ARGT7_154_33.axf