Dependencies for Project 'AT32A403ARGT7_154_33', Target 'AT32A403ARGT7_154_33': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (..\..\User\sysTypeDef.h)(0x6881F4F9)()
F (..\..\User\main.c)(0x68873F7F)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/main.o -MMD)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\User\src_inc\usb_app.h)(0x687D93D7)
I (..\..\User\src_inc\wk_system.h)(0x68786E1F)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\System\system_status.h)(0x688C1399)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\System\Sys_TimerEvent.h)(0x6881F513)
I (..\..\User\SysFSM.h)(0x68806FAC)
I (..\..\Drive\adc_pmsm.h)(0x688C16B0)
F (..\..\User\SysFSM.h)(0x68806FAC)()
F (..\..\User\SysFSM.c)(0x688C1640)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/sysfsm.o -MMD)
I (..\..\User\SysFSM.h)(0x68806FAC)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\Drive\adc_pmsm.h)(0x688C16B0)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\System\system_status.h)(0x688C1399)
I (..\..\User\src_inc\wk_system.h)(0x68786E1F)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\System\Sys_TimerEvent.h)(0x6881F513)
F (..\..\libraries\drivers\src\at32a403a_adc.c)(0x688742EC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_adc.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_crm.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_crm.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_debug.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_debug.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_dma.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_dma.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_exint.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_exint.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_flash.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_flash.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_gpio.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_gpio.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_misc.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_misc.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_pwc.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_pwc.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_spi.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_spi.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_tmr.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_tmr.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_usb.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_usb.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\libraries\drivers\src\at32a403a_wdt.c)(0x684505BC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_wdt.o -MMD)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (.\startup_at32a403a.s)(0x68786E1F)(--cpu Cortex-M4.fp.sp -g --diag_suppress=A1950W

-I"D:\Program Files\Keil5l\Arm\Packs\ArteryTek\AT32A403A_DFP\2.0.8\Device\Include"

--pd "__UVISION_VERSION SETA 540"

--pd "AT32A403ARGT7 SETA 1"

--list .\listings\startup_at32a403a.lst

--xref -o .\objects\startup_at32a403a.o

--depend .\objects\startup_at32a403a.d)
F (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.c)(0x684505BB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/system_at32a403a.o -MMD)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\middlewares\dsp\arm_cortexM4lf_math.lib)(0x67638300)()
F (../../middlewares/usbd_drivers/src/usbd_core.c)(0x684505BD)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/usbd_core.o -MMD)
I (..\..\middlewares\usbd_drivers\inc\usbd_core.h)(0x684505BD)
I (..\..\User\src_inc\usb_conf.h)(0x687F4321)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\middlewares\usbd_drivers\inc\usb_std.h)(0x684505BD)
I (..\..\middlewares\usbd_drivers\inc\usbd_sdr.h)(0x684505BD)
F (../../middlewares/usbd_drivers/src/usbd_int.c)(0x684505BD)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/usbd_int.o -MMD)
I (..\..\middlewares\usbd_drivers\inc\usbd_int.h)(0x684505BD)
I (..\..\middlewares\usbd_drivers\inc\usbd_core.h)(0x684505BD)
I (..\..\User\src_inc\usb_conf.h)(0x687F4321)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\middlewares\usbd_drivers\inc\usb_std.h)(0x684505BD)
F (../../middlewares/usbd_drivers/src/usbd_sdr.c)(0x684505BD)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/usbd_sdr.o -MMD)
I (..\..\middlewares\usbd_drivers\inc\usbd_sdr.h)(0x684505BD)
I (..\..\middlewares\usbd_drivers\inc\usbd_core.h)(0x684505BD)
I (..\..\User\src_inc\usb_conf.h)(0x687F4321)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\middlewares\usbd_drivers\inc\usb_std.h)(0x684505BD)
F (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)()
F (..\..\User\src_inc\at32a403a_int.c)(0x6882FA61)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_int.o -MMD)
I (..\..\User\src_inc\at32a403a_int.h)(0x6879C0A6)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\User\src_inc\usb_app.h)(0x687D93D7)
I (..\..\System\Sys_TimerEvent.h)(0x6881F513)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\System\system_status.h)(0x688C1399)
F (..\..\User\src_inc\at32a403a_int.h)(0x6879C0A6)()
F (..\..\User\src_inc\at32a403a_wk_config.c)(0x68873F3E)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/at32a403a_wk_config.o -MMD)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)()
F (..\..\User\src_inc\cdc_class.c)(0x684505BD)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/cdc_class.o -MMD)
I (..\..\middlewares\usbd_drivers\inc\usbd_core.h)(0x684505BD)
I (..\..\User\src_inc\usb_conf.h)(0x687F4321)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\middlewares\usbd_drivers\inc\usb_std.h)(0x684505BD)
I (..\..\User\src_inc\cdc_class.h)(0x684505BD)
I (..\..\User\src_inc\cdc_desc.h)(0x68786E1F)
F (..\..\User\src_inc\cdc_class.h)(0x684505BD)()
F (..\..\User\src_inc\cdc_desc.c)(0x684505BD)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/cdc_desc.o -MMD)
I (..\..\middlewares\usbd_drivers\inc\usb_std.h)(0x684505BD)
I (..\..\User\src_inc\usb_conf.h)(0x687F4321)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\middlewares\usbd_drivers\inc\usbd_sdr.h)(0x684505BD)
I (..\..\middlewares\usbd_drivers\inc\usbd_core.h)(0x684505BD)
I (..\..\User\src_inc\cdc_desc.h)(0x68786E1F)
I (..\..\User\src_inc\cdc_class.h)(0x684505BD)
F (..\..\User\src_inc\cdc_desc.h)(0x68786E1F)()
F (..\..\User\src_inc\usb_app.c)(0x68888730)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/usb_app.o -MMD)
I (..\..\User\src_inc\usb_app.h)(0x687D93D7)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\User\src_inc\usb_conf.h)(0x687F4321)
I (..\..\User\src_inc\wk_system.h)(0x68786E1F)
I (..\..\middlewares\usbd_drivers\inc\usbd_int.h)(0x684505BD)
I (..\..\middlewares\usbd_drivers\inc\usbd_core.h)(0x684505BD)
I (..\..\middlewares\usbd_drivers\inc\usb_std.h)(0x684505BD)
I (..\..\User\src_inc\cdc_class.h)(0x684505BD)
I (..\..\User\src_inc\cdc_desc.h)(0x68786E1F)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
F (..\..\User\src_inc\usb_app.h)(0x687D93D7)()
F (..\..\User\src_inc\usb_conf.h)(0x687F4321)()
F (..\..\User\src_inc\wk_system.c)(0x68786E1F)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/wk_system.o -MMD)
I (..\..\User\src_inc\wk_system.h)(0x68786E1F)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\User\src_inc\wk_system.h)(0x68786E1F)()
F (..\..\Motor\Algorithm.c)(0x67AAE528)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/algorithm.o -MMD)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
F (..\..\Motor\delay.c)(0x677CF8CA)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/delay.o -MMD)
I (..\..\Motor\delay.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\Motor\delay.h)(0x68787C1B)()
F (..\..\Motor\MathBasic.c)(0x684A3911)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/mathbasic.o -MMD)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
F (..\..\Motor\MathBasic.h)(0x68787C1B)()
F (..\..\Motor\Motor_VectorControl.c)(0x6879E48E)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/motor_vectorcontrol.o -MMD)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
F (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)()
F (..\..\Motor\Sys_Isr_Controller.c)(0x6889C114)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/sys_isr_controller.o -MMD)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\Drive\ENC_Speed.h)(0x68788BAD)
I (..\..\Drive\adc_pmsm.h)(0x688C16B0)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\System\system_status.h)(0x688C1399)
F (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)()
F (..\..\Motor\SysCtl_AnalogProcess.c)(0x68788B2E)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/sysctl_analogprocess.o -MMD)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
F (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)()
F (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)()
F (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)()
F (..\..\Motor\SysCtl_GlobalVar.c)(0x68787C1B)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/sysctl_globalvar.o -MMD)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
F (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)()
F (..\..\Motor\SysCtl_RotorGet.c)(0x68789C49)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/sysctl_rotorget.o -MMD)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
F (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)()
F (..\..\Motor\SysCtl_SysMoore.c)(0x68788B63)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/sysctl_sysmoore.o -MMD)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\User\SysFSM.h)(0x68806FAC)
F (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)()
F (..\..\Motor\SysVoltBase.h)(0x68787C1A)()
F (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)()
F (..\..\AnoPTv8\AnoPTv8Cmd.c)(0x687D8AE2)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/anoptv8cmd.o -MMD)
I (..\..\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)()
F (..\..\AnoPTv8\AnoPTv8FrameFactory.c)(0x687D98AB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/anoptv8framefactory.o -MMD)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\..\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\..\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\..\User\src_inc\usb_app.h)(0x687D93D7)
F (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)()
F (..\..\AnoPTv8\AnoPTv8Par.c)(0x687D8BB4)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/anoptv8par.o -MMD)
I (..\..\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\AnoPTv8\AnoPTv8Par.h)(0x686177E4)()
F (..\..\AnoPTv8\AnoPTv8Run.c)(0x6879BB60)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/anoptv8run.o -MMD)
I (..\..\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\..\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
F (..\..\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)()
F (..\..\AnoPTv8\HWInterface.c)(0x687D8CFC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/hwinterface.o -MMD)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\..\AnoPTv8\MotorCmd.h)(0x6822FFB1)
I (..\..\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\..\User\src_inc\usb_app.h)(0x687D93D7)
I (..\..\System\Sys_TimerEvent.h)(0x6881F513)
F (..\..\AnoPTv8\HWInterface.h)(0x68872861)()
F (..\..\AnoPTv8\MotorCmd.c)(0x68788AB7)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/motorcmd.o -MMD)
I (..\..\AnoPTv8\MotorCmd.h)(0x6822FFB1)
I (..\..\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\..\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\..\Motor\SysCtl_AllHeaders.h)(0x6822F190)
I (..\..\Motor\MathBasic.h)(0x68787C1B)
I (..\..\Motor\SysCtl_SysMoore.h)(0x68787C1A)
I (..\..\Motor\SysCtl_AnalogProcess.h)(0x68787C1B)
I (..\..\Motor\SysCtl_ConstDef.h)(0x67B43716)
I (..\..\Motor\SysCtl_CsvParamDef.h)(0x68787C1B)
I (..\..\Motor\Motor_VectorControl.h)(0x68787C1B)
I (..\..\Motor\SysCtl_RotorGet.h)(0x684CE6C1)
I (..\..\Motor\SysCtl_IoAd2s1210.h)(0x68787C1B)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\Motor\SysCtl_GlobalVar.h)(0x68787C1B)
I (..\..\Motor\SysVoltBase.h)(0x68787C1A)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\User\SysFSM.h)(0x68806FAC)
I (..\..\Motor\delay.h)(0x68787C1B)
F (..\..\AnoPTv8\MotorCmd.h)(0x6822FFB1)()
F (..\..\AnoPTv8\MotorData.c)(0x68882EC1)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/motordata.o -MMD)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\AnoPTv8\MotorCmd.h)(0x6822FFB1)
I (..\..\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\..\AnoPTv8\AnoPTv8Run.h)(0x687D96E7)
I (..\..\System\Sys_TimerEvent.h)(0x6881F513)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\System\system_status.h)(0x688C1399)
I (..\..\Drive\adc_pmsm.h)(0x688C16B0)
I (..\..\System\protection_monitor.h)(0x6881F014)
F (..\..\AnoPTv8\MotorData.h)(0x68882E98)()
F (..\..\AnoPTv8\MotorParams.c)(0x687F3816)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/motorparams.o -MMD)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\AnoPTv8\MotorParams.h)(0x6840519C)()
F (..\..\Drive\ad2s1212_spi.c)(0x68870710)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/ad2s1212_spi.o -MMD)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\Drive\ad2s1212_spi.h)(0x6883478D)()
F (..\..\Drive\ENC_Speed.c)(0x6858C8EB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/enc_speed.o -MMD)
I (..\..\Drive\ENC_Speed.h)(0x68788BAD)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Drive\ad2s1212_spi.h)(0x6883478D)
I (..\..\libraries\cmsis\cm4\core_support\arm_math.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_types.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\dsp\none.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\utils.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h)(0x66A6B132)
I (..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h)(0x66A6B132)
I (..\..\AnoPTv8\MotorCmd.h)(0x6822FFB1)
I (..\..\AnoPTv8\AnoPTv8Cmd.h)(0x68418FAE)
I (..\..\AnoPTv8\AnoPTv8.h)(0x687D970E)
I (..\..\AnoPTv8\AnoPTv8FrameFactory.h)(0x6854F777)
I (..\..\AnoPTv8\HWInterface.h)(0x68872861)
I (..\..\AnoPTv8\MotorParams.h)(0x6840519C)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
I (..\..\AnoPTv8\AnoPTv8Par.h)(0x686177E4)
F (..\..\Drive\ENC_Speed.h)(0x68788BAD)()
F (..\..\Drive\adc_pmsm.c)(0x688C1661)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/adc_pmsm.o -MMD)
I (..\..\Drive\adc_pmsm.h)(0x688C16B0)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\System\system_status.h)(0x688C1399)
F (..\..\Drive\adc_pmsm.h)(0x688C16B0)()
F (..\..\Drive\PT1000_table.c)(0x687E0BA8)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/pt1000_table.o -MMD)
I (..\..\Drive\PT1000_table.h)(0x6881EFDB)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\Drive\PT1000_table.h)(0x6881EFDB)()
F (..\..\Drive\sensor_proc.c)(0x6889E62B)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/sensor_proc.o -MMD)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\System\system_status.h)(0x688C1399)
I (..\..\Drive\PT1000_table.h)(0x6881EFDB)
I (..\..\AnoPTv8\MotorData.h)(0x68882E98)
I (..\..\User\sysTypeDef.h)(0x6881F4F9)
F (..\..\Drive\sensor_proc.h)(0x6889E620)()
F (..\..\System\Sys_TimerEvent.c)(0x68804D7B)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/sys_timerevent.o -MMD)
I (..\..\System\Sys_TimerEvent.h)(0x6881F513)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
F (..\..\System\Sys_TimerEvent.h)(0x6881F513)()
F (..\..\System\low_speed_alarm.c)(0x6881EA21)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/low_speed_alarm.o -MMD)
I (..\..\System\low_speed_alarm.h)(0x68883376)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\System\Sys_TimerEvent.h)(0x6881F513)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\System\system_status.h)(0x688C1399)
I (..\..\System\protection_monitor.h)(0x6881F014)
F (..\..\System\low_speed_alarm.h)(0x68883376)()
F (..\..\System\protection_monitor.c)(0x6889B129)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/protection_monitor.o -MMD)
I (..\..\System\protection_monitor.h)(0x6881F014)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\System\system_status.h)(0x688C1399)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\Drive\adc_pmsm.h)(0x688C16B0)
I (..\..\System\Sys_TimerEvent.h)(0x6881F513)
F (..\..\System\protection_monitor.h)(0x6881F014)()
F (..\..\System\system_status.h)(0x688C1399)()
F (..\..\System\power_stage_test.c)(0x688C1781)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../libraries/drivers/inc -I ../../libraries/cmsis/cm4/core_support -I ../../libraries/cmsis/cm4/device_support -I ../../middlewares/usbd_drivers/inc -I ../../User -I ../../User/src_inc -I ../../Motor -I ../../AnoPTv8 -I ../../Drive -I ../../System

-I"D:/Program Files/Keil5l/Arm/Packs/ArteryTek/AT32A403A_DFP/2.0.8/Device/Include"

-D__UVISION_VERSION="540" -DAT32A403ARGT7 -DAT32A403ARGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_A403A_V1

-o ./objects/power_stage_test.o -MMD)
I (..\..\System\power_stage_test.h)(0x688C13AE)
I (..\..\libraries\cmsis\cm4\device_support\at32a403a.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x684505BB)
I (..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h)(0x684505BB)
I (..\..\libraries\drivers\inc\at32a403a_def.h)(0x684505BC)
I (..\..\User\src_inc\at32a403a_conf.h)(0x68786E1F)
I (..\..\libraries\drivers\inc\at32a403a_adc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_crm.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_debug.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_dma.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_exint.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_flash.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_gpio.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_misc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_pwc.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_spi.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_tmr.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_usb.h)(0x684505BC)
I (..\..\libraries\drivers\inc\at32a403a_wdt.h)(0x684505BC)
I (..\..\System\system_status.h)(0x688C1399)
I (..\..\Drive\sensor_proc.h)(0x6889E620)
I (..\..\User\src_inc\at32a403a_wk_config.h)(0x68833514)
I (..\..\System\Sys_TimerEvent.h)(0x6881F513)
I (..\..\Drive\adc_pmsm.h)(0x688C16B0)
I (..\..\User\src_inc\wk_system.h)(0x68786E1F)
F (..\..\System\power_stage_test.h)(0x688C13AE)()
