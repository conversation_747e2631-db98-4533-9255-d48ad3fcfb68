#ifndef __ADC_PMSM_H
#define __ADC_PMSM_H

#include "at32a403a_wk_config.h"
#include "sensor_proc.h"

/********** ADC硬件抽象宏定义 **********/
// ADC硬件配置宏
// 移植说明：
// 1. ADC_INSTANCE: 根据目标平台选择合适的ADC实例
// 2. ADC_PREEMPT_COMPLETE_FLAG: 根据目标平台的ADC完成标志定义
// 3. ADC_SOFTWARE_TRIGGER: 软件触发源定义
// 4. ADC_HARDWARE_TRIGGER: 硬件触发源定义（定时器触发）
#define ADC_INSTANCE                ADC2                        // ADC实例
#define ADC_PREEMPT_COMPLETE_FLAG   ADC_PCCE_FLAG              // 抢占转换完成标志
#define ADC_SOFTWARE_TRIGGER        ADC12_PREEMPT_TRIG_SOFTWARE // 软件触发源
#define ADC_HARDWARE_TRIGGER        ADC12_PREEMPT_TRIG_TMR1CH4  // 硬件触发源（TMR1CH4）

#define ADC_READ_IDC_RAW() (uint16_t)(ADC_INSTANCE->pdt1_bit.pdt1) // 读取母线电流ADC原始值
#define ADC_READ_IU_RAW() (uint16_t)(ADC_INSTANCE->pdt2_bit.pdt2) // 读取U相电流ADC原始值
#define ADC_READ_IV_RAW() (uint16_t)(ADC_INSTANCE->pdt3_bit.pdt3) // 读取V相电流ADC原始值

// ADC转换触发和状态检查宏
#define ADC_START_PREEMPT_CONV()    adc_preempt_software_trigger_enable(ADC_INSTANCE, TRUE)
#define ADC_IS_PREEMPT_COMPLETE()   adc_flag_get(ADC_INSTANCE, ADC_PCCE_FLAG)
#define ADC_CLEAR_PREEMPT_FLAG()    adc_flag_clear(ADC_INSTANCE, ADC_PCCE_FLAG)
#define ADC_CLEAR_PCCS_FLAG()       adc_flag_clear(ADC_INSTANCE, ADC_PCCS_FLAG)
#define ADC_CLEAR_OCCS_FLAG()       adc_flag_clear(ADC_INSTANCE, ADC_OCCS_FLAG)


// ADC触发模式切换宏
#define ADC_SET_SOFTWARE_TRIGGER()  adc_preempt_conversion_trigger_set(ADC_INSTANCE, ADC_SOFTWARE_TRIGGER, TRUE)
#define ADC_SET_SOFTWARE_TRIGGER_DISABLE()  adc_preempt_conversion_trigger_set(ADC_INSTANCE, ADC_SOFTWARE_TRIGGER, FALSE)
#define ADC_SET_HARDWARE_TRIGGER()  adc_preempt_conversion_trigger_set(ADC_INSTANCE, ADC_HARDWARE_TRIGGER, TRUE)

// ADC校准配置宏
#define ADC_CALIBRATION_SAMPLES     1000    // 校准采样次数
#define ADC_ZERO_DRIFT_THRESHOLD    2.0f    // 零飘阈值
#define ADC_CALIBRATION_DELAY       1000    // 校准采样间隔延时循环

/********** ADC电流采样相关常量定义 **********/

// 母线电流转换参数
#define ADC_TO_IDC_COEFF        0.036360f    // 母线电流ADC计数转电流系数
#define ADC_IDC_OFFSET_VOLTAGE  -81.1688f        // 母线电流ADC偏置

// 相电流转换参数（A、B、C三相参数一致）
#define ADC_TO_PHASE_COEFF       0.036360f    // 相电流ADC计数转电流系数
#define ADC_PHASE_OFFSET_VOLTAGE -81.1688f       // 相电流ADC偏置

// 滤波相关常量
// 母线电流专用滑动平均窗口大小
#define IDC_MOVING_AVG_WINDOW_SIZE  3        // 母线电流滑动平均窗口大小
// 注意：MOVING_AVG_WINDOW_SIZE 已在 sensor_proc.h 中定义，用于其他模块

// 一阶低通滤波器参数
#define LPF1_ALPHA              0.7827f       // 一阶低通滤波器系数
                                              // 采样频率20kHz时不同截止频率对应的系数：
                                              // fc=1.5kHz: α=0.3203
                                              // fc=2.0kHz: α=0.3858
                                              // fc=2.5kHz: α=0.4414
                                              // fc=3.0kHz: α=0.4886
                                              // fc=3.5kHz: α=0.5294
                                              // fc=4.0kHz: α=0.5652
                                              // fc=4.5kHz: α=0.5978
                                              // fc=5.0kHz: α=0.6283
                                              // fc=5.5kHz: α=0.6569
                                              // fc=6.0kHz: α=0.6840
                                              // fc=6.5kHz: α=0.7100
                                              // fc=7.0kHz: α=0.7350
                                              // fc=7.5kHz: α=0.7592
                                              // fc=8.0kHz: α=0.7827
                                              // fc=8.5kHz: α=0.8056
                                              // fc=9.0kHz: α=0.8279
                                              // fc=9.5kHz: α=0.8497
                                              // fc=10.0kHz: α=0.8711
#define LPF_RECTIFIED_ALPHA     0.01f         // 整流平均值低通滤波器系数

/********** 滤波器设计 **********/

// 一阶低通滤波器
typedef struct {
    float alpha;        // 滤波系数
    float prev_output;  // 上次输出值
} LPF_t;

// 滑动平均滤波器
typedef struct {
    float buffer[IDC_MOVING_AVG_WINDOW_SIZE]; // 滑动窗口缓冲区
    uint8_t index;      // 当前索引
    float sum;          // 当前窗口和值
} MovingAvg_t;

// 三相滤波器组合
typedef struct {
    LPF_t ia_lpf;              // A相低通滤波器
    LPF_t ib_lpf;              // B相低通滤波器
    LPF_t ic_lpf;              // C相低通滤波器
} ThreePhase_LPF_t;

typedef struct {
    LPF_t ia_rect;             // A相整流滤波器
    LPF_t ib_rect;             // B相整流滤波器
    LPF_t ic_rect;             // C相整流滤波器
} ThreePhase_RectLPF_t;

/********** 数据结构定义 **********/

// 三相电流原始ADC值结构
typedef struct {
    uint16_t ia_adc;                   // A相ADC原始值
    uint16_t ib_adc;                   // B相ADC原始值
    uint16_t ic_adc;                   // C相ADC原始值（未启用）
} ThreePhase_ADC_t;

// 三相电流原始值结构（零飘补偿后）
typedef struct {
    float ia_raw;                      // A相原始电流值
    float ib_raw;                      // B相原始电流值
    float ic_raw;                      // C相原始电流值（计算得出）
} ThreePhase_Raw_t;

// 三相电流滤波后值结构
typedef struct {
    float ia_filt;                     // A相滤波后电流值
    float ib_filt;                     // B相滤波后电流值
    float ic_filt;                     // C相滤波后电流值
} ThreePhase_Filtered_t;

// 三相电流整流平均值结构
typedef struct {
    float ia_rectified_avg;            // A相整流平均值
    float ib_rectified_avg;            // B相整流平均值
    float ic_rectified_avg;            // C相整流平均值
} ThreePhase_RectifiedAvg_t;

// 三相零飘补偿值结构
typedef struct {
    float ia_offset;                   // A相零飘补偿值
    float ib_offset;                   // B相零飘补偿值
    float ic_offset;                   // C相零飘补偿值（预留，2相采样通过计算得出）
} ThreePhase_Offset_t;

/********** 滤波器管理结构 **********/
typedef struct {
    // 三相低通滤波器组
    ThreePhase_LPF_t phase_lpf;                  // 三相低通滤波器
    ThreePhase_RectLPF_t phase_rect;             // 三相整流滤波器

    // 母线电流滤波器
    MovingAvg_t idc_mavg;                 // 母线电流滑动平均滤波器

    // 初始化标志
    uint8_t initialized;                         // 初始化标志
} ADC_FilterManager_t;

/********** ADC电流采样数据结构 **********/
typedef struct {
    // 原始ADC值
    uint16_t idc_adc_raw;              // 母线电流ADC原始值
    ThreePhase_ADC_t phase_adc;        // 三相电流ADC原始值

    // 原始电流值（零飘补偿后）
    float idc_raw;                     // 母线电流原始值
    ThreePhase_Raw_t phase_raw;        // 三相电流原始值

    // 滤波后电流值
    ThreePhase_Filtered_t phase_filt;  // 三相电流滤波后值

    // 整流平均值
    ThreePhase_RectifiedAvg_t phase_rectified; // 三相电流整流平均值

    // 零飘补偿值
    float idc_offset;                  // 母线电流零飘补偿值
    ThreePhase_Offset_t phase_offset;  // 三相电流零飘补偿值

    // 计算完成标志
    volatile uint8_t calculation_completed;           // 计算完成标志：1=完成，0=进行中
    uint32_t calculation_tick;                        // 计算完成计次
} ADC_CurrentData_t;

extern ADC_CurrentData_t g_adc_current_data;
extern ADC_FilterManager_t g_adc_filter_manager;

/********** 数据访问静态内联函数 **********/

/**
 * @brief 获取A相滤波后电流值
 * @return A相滤波后电流值 (A)
 * @note 经过低通滤波处理
 */
static inline float ADC_GET_IA_FILTERED(void)
{
    return g_adc_current_data.phase_filt.ia_filt;
}

/**
 * @brief 获取B相滤波后电流值
 * @return B相滤波后电流值 (A)
 * @note 经过低通滤波处理
 */
static inline float ADC_GET_IB_FILTERED(void)
{
    return g_adc_current_data.phase_filt.ib_filt;
}

/**
 * @brief 获取C相滤波后电流值
 * @return C相滤波后电流值 (A)
 * @note 经过低通滤波处理
 */
static inline float ADC_GET_IC_FILTERED(void)
{
    return g_adc_current_data.phase_filt.ic_filt;
}

/**
 * @brief 获取A相整流平均值
 * @return A相整流平均值 (A)
 * @note 用于电机温升保护和功率计算
 */
static inline float ADC_GET_IA_RECTIFIED_AVG(void)
{
    return g_adc_current_data.phase_rectified.ia_rectified_avg;
}

/**
 * @brief 获取B相整流平均值
 * @return B相整流平均值 (A)
 * @note 用于电机温升保护和功率计算
 */
static inline float ADC_GET_IB_RECTIFIED_AVG(void)
{
    return g_adc_current_data.phase_rectified.ib_rectified_avg;
}

/**
 * @brief 获取C相整流平均值
 * @return C相整流平均值 (A)
 * @note 用于电机温升保护和功率计算
 */
static inline float ADC_GET_IC_RECTIFIED_AVG(void)
{
    return g_adc_current_data.phase_rectified.ic_rectified_avg;
}

/**
 * @brief 获取母线电流值
 * @return 母线电流值 (A)
 * @note 经过零飘补偿处理的母线电流
 */
static inline float ADC_GET_IDC(void)
{
    return g_adc_current_data.idc_raw;
}

/********** 兼容性静态内联函数 - 支持U/V/W命名约定 **********/

#define ADC_GET_IU_FILTERED() ADC_GET_IA_FILTERED()
#define ADC_GET_IV_FILTERED() ADC_GET_IB_FILTERED()
#define ADC_GET_IW_FILTERED() ADC_GET_IC_FILTERED()

/********** 计算完成标志相关静态内联函数 - 数据同步支持 **********/

/**
 * @brief 检查ADC计算是否完成
 * @return 1: 计算完成, 0: 计算进行中
 * @note 用于数据同步，确保读取到最新的计算结果
 */
static inline uint8_t ADC_IS_CALCULATION_COMPLETED(void)
{
    return g_adc_current_data.calculation_completed;
}

/**
 * @brief 获取ADC计算完成计次
 * @return 计算完成计次值
 */
static inline uint32_t ADC_GET_CALCULATION_TICK(void)
{
    return g_adc_current_data.calculation_tick;
}

/**
 * @brief 清除ADC计算完成标志
 * @note 通常在开始新的计算周期前调用
 */
static inline void ADC_CLEAR_CALCULATION_FLAG(void)
{
    g_adc_current_data.calculation_completed = 0;
}

/**
 * @brief 检查是否有新的计算结果
 * @param last_counter 上次读取的计数器值指针
 * @return 1: 有新结果, 0: 无新结果
 * @note 通过比较计数器值判断是否有新的计算结果
 */
static inline uint8_t ADC_HAS_NEW_CALCULATION(uint32_t *last_counter)
{
    uint32_t current_counter = g_adc_current_data.calculation_tick;
    if (current_counter != *last_counter) {
        *last_counter = current_counter;
        return 1;
    }
    return 0;
}

/********** 滤波器API函数声明 **********/

// 滤波器管理器函数
void ADC_FilterManager_Init(void);
void ADC_FilterManager_Reset(void);

/********** 公开API函数声明 **********/
void ADC_PMSM_Init(void);
void ADC_PMSM_ProcessCurrents(float *ia_filt_out, float *ib_filt_out, float *ic_filt_out);
void ADC_PMSM_ProcessRectifiedAverage(void);
void ADC_PMSM_CalibrateOffset(void);
uint8_t ADC_PMSM_IsCalibrationCompleted(void);

#endif /* __ADC_PMSM_H */
