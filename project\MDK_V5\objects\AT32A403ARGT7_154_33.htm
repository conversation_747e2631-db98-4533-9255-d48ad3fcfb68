<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\objects\AT32A403ARGT7_154_33.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\objects\AT32A403ARGT7_154_33.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6220000: Last Updated: Tue Aug 12 09:02:21 2025
<BR><P>
<H3>Maximum Stack Usage =        352 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; wk_usb_app_task &rArr; AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[8a]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[61]">ACC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[61]">ACC_IRQHandler</a><BR>
 <LI><a href="#[17]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[17]">BusFault_Handler</a><BR>
 <LI><a href="#[15]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[15]">HardFault_Handler</a><BR>
 <LI><a href="#[16]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[16]">MemManage_Handler</a><BR>
 <LI><a href="#[18]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[18]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[61]">ACC_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[2f]">ADC1_2_IRQHandler</a> from at32a403a_int.o(.text.ADC1_2_IRQHandler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[4c]">ADC3_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[6c]">AnoPTv8CmdFun_MotorClearFault</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorClearFault) referenced from motorcmd.o(.rodata._pCmdInfoMotorClearFault)
 <LI><a href="#[6e]">AnoPTv8CmdFun_MotorGetFault</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorGetFault) referenced from motorcmd.o(.rodata._pCmdInfoMotorGetFault)
 <LI><a href="#[6d]">AnoPTv8CmdFun_MotorReset</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorReset) referenced from motorcmd.o(.rodata._pCmdInfoMotorEStop)
 <LI><a href="#[6f]">AnoPTv8CmdFun_MotorSelfTest</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorSelfTest) referenced from motorcmd.o(.rodata._pCmdInfoMotorSelfTest)
 <LI><a href="#[70]">AnoPTv8CmdFun_MotorStart</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorStart) referenced from motorcmd.o(.rodata._pCmdInfoMotorStart)
 <LI><a href="#[71]">AnoPTv8CmdFun_MotorStop</a> from motorcmd.o(.text.AnoPTv8CmdFun_MotorStop) referenced from motorcmd.o(.rodata._pCmdInfoMotorStop)
 <LI><a href="#[72]">AnoPTv8CmdFun_WaveCtrl</a> from motorcmd.o(.text.AnoPTv8CmdFun_WaveCtrl) referenced from motorcmd.o(.rodata._pCmdInfoWaveCtrl)
 <LI><a href="#[73]">AnoPTv8CmdFun_WaveStopAll</a> from motorcmd.o(.text.AnoPTv8CmdFun_WaveStopAll) referenced from motorcmd.o(.rodata._pCmdInfoWaveStopAll)
 <LI><a href="#[17]">BusFault_Handler</a> from at32a403a_int.o(.text.BusFault_Handler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[32]">CAN1_RX1_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[33]">CAN1_SE_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[5e]">CAN2_RX0_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[5f]">CAN2_RX1_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[60]">CAN2_SE_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[5d]">CAN2_TX_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[22]">CRM_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[28]">DMA1_Channel1_IRQHandler</a> from at32a403a_int.o(.text.DMA1_Channel1_IRQHandler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[29]">DMA1_Channel2_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[2a]">DMA1_Channel3_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[2b]">DMA1_Channel4_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[2c]">DMA1_Channel5_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[2d]">DMA1_Channel6_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[2e]">DMA1_Channel7_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[55]">DMA2_Channel1_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[56]">DMA2_Channel2_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[57]">DMA2_Channel3_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[58]">DMA2_Channel4_5_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[64]">DMA2_Channel6_7_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[1a]">DebugMon_Handler</a> from at32a403a_int.o(.text.DebugMon_Handler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[68]">EMAC_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[69]">EMAC_WKUP_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[23]">EXINT0_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[45]">EXINT15_10_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[24]">EXINT1_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[25]">EXINT2_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[26]">EXINT3_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[27]">EXINT4_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[34]">EXINT9_5_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[21]">FLASH_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[15]">HardFault_Handler</a> from at32a403a_int.o(.text.HardFault_Handler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[3d]">I2C1_ERR_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[3c]">I2C1_EVT_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[3f]">I2C2_ERR_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[3e]">I2C2_EVT_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[5b]">I2C3_ERR_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[5a]">I2C3_EVT_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[16]">MemManage_Handler</a> from at32a403a_int.o(.text.MemManage_Handler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[14]">NMI_Handler</a> from at32a403a_int.o(.text.NMI_Handler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[1e]">PVM_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[1b]">PendSV_Handler</a> from at32a403a_int.o(.text.PendSV_Handler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[46]">RTCAlarm_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[20]">RTC_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[13]">Reset_Handler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[4e]">SDIO1_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[59]">SDIO2_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[40]">SPI1_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[41]">SPI2_I2S2EXT_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[50]">SPI3_I2S3EXT_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[5c]">SPI4_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[19]">SVC_Handler</a> from at32a403a_int.o(.text.SVC_Handler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[1c]">SysTick_Handler</a> from at32a403a_int.o(.text.SysTick_Handler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[6a]">SystemInit</a> from system_at32a403a.o(.text.SystemInit) referenced from startup_at32a403a.o(.text)
 <LI><a href="#[1f]">TAMPER_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[35]">TMR1_BRK_TMR9_IRQHandler</a> from at32a403a_int.o(.text.TMR1_BRK_TMR9_IRQHandler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[38]">TMR1_CH_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[36]">TMR1_OVF_TMR10_IRQHandler</a> from at32a403a_int.o(.text.TMR1_OVF_TMR10_IRQHandler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[37]">TMR1_TRG_HALL_TMR11_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[39]">TMR2_GLOBAL_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[3a]">TMR3_GLOBAL_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[3b]">TMR4_GLOBAL_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[4f]">TMR5_GLOBAL_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[53]">TMR6_GLOBAL_IRQHandler</a> from at32a403a_int.o(.text.TMR6_GLOBAL_IRQHandler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[54]">TMR7_GLOBAL_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[48]">TMR8_BRK_TMR12_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[4b]">TMR8_CH_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[49]">TMR8_OVF_TMR13_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[4a]">TMR8_TRG_HALL_TMR14_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[51]">UART4_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[52]">UART5_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[66]">UART7_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[67]">UART8_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[42]">USART1_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[43]">USART2_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[44]">USART3_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[65]">USART6_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[47]">USBFSWakeUp_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[30]">USBFS_H_CAN1_TX_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[31]">USBFS_L_CAN1_RX0_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[62]">USBFS_MAPH_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[63]">USBFS_MAPL_IRQHandler</a> from at32a403a_int.o(.text.USBFS_MAPL_IRQHandler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[18]">UsageFault_Handler</a> from at32a403a_int.o(.text.UsageFault_Handler) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[1d]">WWDT_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[4d]">XMC_IRQHandler</a> from startup_at32a403a.o(.text) referenced from startup_at32a403a.o(RESET)
 <LI><a href="#[6b]">__main</a> from __main.o(!!!main) referenced from startup_at32a403a.o(.text)
 <LI><a href="#[1]">class_clear_handler</a> from cdc_class.o(.text.class_clear_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[4]">class_ept0_rx_handler</a> from cdc_class.o(.text.class_ept0_rx_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[3]">class_ept0_tx_handler</a> from cdc_class.o(.text.class_ept0_tx_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[8]">class_event_handler</a> from cdc_class.o(.text.class_event_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[5]">class_in_handler</a> from cdc_class.o(.text.class_in_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[0]">class_init_handler</a> from cdc_class.o(.text.class_init_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[6]">class_out_handler</a> from cdc_class.o(.text.class_out_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[2]">class_setup_handler</a> from cdc_class.o(.text.class_setup_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[7]">class_sof_handler</a> from cdc_class.o(.text.class_sof_handler) referenced 2 times from cdc_class.o(.data.cdc_class_handler)
 <LI><a href="#[12]">get_device_config_string</a> from cdc_desc.o(.text.get_device_config_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[b]">get_device_configuration</a> from cdc_desc.o(.text.get_device_configuration) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[9]">get_device_descriptor</a> from cdc_desc.o(.text.get_device_descriptor) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[11]">get_device_interface_string</a> from cdc_desc.o(.text.get_device_interface_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[d]">get_device_lang_id</a> from cdc_desc.o(.text.get_device_lang_id) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[e]">get_device_manufacturer_string</a> from cdc_desc.o(.text.get_device_manufacturer_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[c]">get_device_other_speed</a> from cdc_desc.o(.text.get_device_other_speed) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[f]">get_device_product_string</a> from cdc_desc.o(.text.get_device_product_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[a]">get_device_qualifier</a> from cdc_desc.o(.text.get_device_qualifier) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
 <LI><a href="#[10]">get_device_serial_string</a> from cdc_desc.o(.text.get_device_serial_string) referenced 2 times from cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[6b]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(.text)
</UL>
<P><STRONG><a name="[74]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[76]"></a>__scatterload_rt2</STRONG> (Thumb, 84 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[154]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[155]"></a>__scatterload_loop</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[156]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[157]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[158]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, __scatter.o(!!handler_null), UNUSED)

<P><STRONG><a name="[159]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[15a]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))

<P><STRONG><a name="[77]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[15b]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[7e]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[79]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[15c]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[15d]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[15e]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[15f]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[160]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000034))

<P><STRONG><a name="[161]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[162]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[163]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[164]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[165]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[166]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[167]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[168]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[169]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[16a]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000006))

<P><STRONG><a name="[16b]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000010))

<P><STRONG><a name="[16c]"></a>__rt_lib_init_relocate_pie_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[16d]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000035))

<P><STRONG><a name="[16e]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[16f]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[170]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[83]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[171]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[172]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[173]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[174]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[175]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[176]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[177]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[75]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[178]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[7b]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[7d]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[179]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[7f]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 352 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; wk_usb_app_task &rArr; AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[17a]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[8b]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[82]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[17b]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[84]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[13]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>ACC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>CAN1_SE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>CAN2_SE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CRM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>DMA2_Channel6_7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>EMAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>EMAC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXINT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>EXINT15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXINT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXINT2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>EXINT3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>EXINT4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>EXINT9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>I2C1_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>I2C1_EVT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>I2C2_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>I2C2_EVT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>I2C3_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>I2C3_EVT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>PVM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>SDIO1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>SDIO2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>SPI2_I2S2EXT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>SPI3_I2S3EXT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TMR1_CH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TMR1_TRG_HALL_TMR11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TMR2_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TMR3_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TMR4_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>TMR5_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>TMR7_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>TMR8_BRK_TMR12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>TMR8_CH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TMR8_OVF_TMR13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>TMR8_TRG_HALL_TMR14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>USBFSWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USBFS_H_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USBFS_L_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>USBFS_MAPH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>WWDT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>XMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32a403a.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_at32a403a.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[78]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
</UL>

<P><STRONG><a name="[b4]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
</UL>

<P><STRONG><a name="[87]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[17c]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[88]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvBytes
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[17d]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[17e]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[17f]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[9d]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_Init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sensor_proc_init
</UL>

<P><STRONG><a name="[180]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[181]"></a>__rt_memclr_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[182]"></a>_memset_w</STRONG> (Thumb, 74 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[183]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[184]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[185]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[86]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[186]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[89]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[187]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[7c]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[81]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[85]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[188]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[189]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[18a]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[8c]"></a>AD2S1210_CommRead</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, ad2s1212_spi.o(.text.AD2S1210_CommRead))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD2S1210_CommRead
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Execute
</UL>

<P><STRONG><a name="[8f]"></a>AD2S1210_Init</STRONG> (Thumb, 366 bytes, Stack size 24 bytes, ad2s1212_spi.o(.text.AD2S1210_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AD2S1210_Init &rArr; AD2S1210_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[91]"></a>AD2S1210_READ</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, ad2s1212_spi.o(.text.AD2S1210_READ))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD2S1210_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
</UL>

<P><STRONG><a name="[90]"></a>AD2S1210_WRITE</STRONG> (Thumb, 300 bytes, Stack size 24 bytes, ad2s1212_spi.o(.text.AD2S1210_WRITE))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD2S1210_WRITE
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
</UL>

<P><STRONG><a name="[2f]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, at32a403a_int.o(.text.ADC1_2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ADC1_2_IRQHandler &rArr; ADC_PMSM_ProcessCurrents
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_ProcessCurrents
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_clear
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[98]"></a>ADC_PMSM_CalibrateOffset</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, adc_pmsm.o(.text.ADC_PMSM_CalibrateOffset))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ADC_PMSM_CalibrateOffset
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_clear
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_get
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_software_trigger_enable
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_conversion_trigger_set
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_Init
</UL>

<P><STRONG><a name="[9c]"></a>ADC_PMSM_Init</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, adc_pmsm.o(.text.ADC_PMSM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = ADC_PMSM_Init &rArr; ADC_PMSM_CalibrateOffset
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_CalibrateOffset
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[97]"></a>ADC_PMSM_ProcessCurrents</STRONG> (Thumb, 376 bytes, Stack size 16 bytes, adc_pmsm.o(.text.ADC_PMSM_ProcessCurrents))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_PMSM_ProcessCurrents
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>

<P><STRONG><a name="[c4]"></a>ADC_PMSM_ProcessRectifiedAverage</STRONG> (Thumb, 142 bytes, Stack size 0 bytes, adc_pmsm.o(.text.ADC_PMSM_ProcessRectifiedAverage))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Execute
</UL>

<P><STRONG><a name="[9e]"></a>AnoPTv8CmdFrameAnl</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, anoptv8cmd.o(.text.AnoPTv8CmdFrameAnl))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvBytes
</UL>

<P><STRONG><a name="[b5]"></a>AnoPTv8CmdGetCount</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, anoptv8cmd.o(.text.AnoPTv8CmdGetCount))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
</UL>

<P><STRONG><a name="[b6]"></a>AnoPTv8CmdGetInfo</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, anoptv8cmd.o(.text.AnoPTv8CmdGetInfo))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
</UL>

<P><STRONG><a name="[bd]"></a>AnoPTv8CmdRegister</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, anoptv8cmd.o(.text.AnoPTv8CmdRegister))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorCmdInit
</UL>

<P><STRONG><a name="[b9]"></a>AnoPTv8GetParamType</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, anoptv8par.o(.text.AnoPTv8GetParamType))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
</UL>

<P><STRONG><a name="[a3]"></a>AnoPTv8HwInit</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, hwinterface.o(.text.AnoPTv8HwInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AnoPTv8HwInit &rArr; MotorParamsInit
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorCmdInit
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorParamsInit
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b2]"></a>AnoPTv8HwParCmdRecvCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwParCmdRecvCallback))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[b1]"></a>AnoPTv8HwParCmdResetParameter</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwParCmdResetParameter))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[a6]"></a>AnoPTv8HwRecvBytes</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwRecvBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_task
</UL>

<P><STRONG><a name="[a8]"></a>AnoPTv8HwSendBytes</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwSendBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_send_data
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8TxLargeBufSend
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>

<P><STRONG><a name="[aa]"></a>AnoPTv8HwTrigger1ms</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, hwinterface.o(.text.AnoPTv8HwTrigger1ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AnoPTv8HwTrigger1ms &rArr; AnoPTv8TxLargeBufSend &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8TxLargeBufSend
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ac]"></a>AnoPTv8ParFrameAnl</STRONG> (Thumb, 392 bytes, Stack size 8 bytes, anoptv8par.o(.text.AnoPTv8ParFrameAnl))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwParCmdRecvCallback
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwParCmdResetParameter
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendDevInfo
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvBytes
</UL>

<P><STRONG><a name="[b7]"></a>AnoPTv8ParGetCount</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, anoptv8par.o(.text.AnoPTv8ParGetCount))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
</UL>

<P><STRONG><a name="[b8]"></a>AnoPTv8ParGetInfo</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, anoptv8par.o(.text.AnoPTv8ParGetInfo))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
</UL>

<P><STRONG><a name="[be]"></a>AnoPTv8ParRegister</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, anoptv8par.o(.text.AnoPTv8ParRegister))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorParamsInit
</UL>

<P><STRONG><a name="[a7]"></a>AnoPTv8RecvBytes</STRONG> (Thumb, 316 bytes, Stack size 40 bytes, anoptv8run.o(.text.AnoPTv8RecvBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwRecvBytes
</UL>

<P><STRONG><a name="[b3]"></a>AnoPTv8SendBuf</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, anoptv8framefactory.o(.text.AnoPTv8SendBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateF1FocData
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateF2SensorData
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendDevInfo
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
</UL>

<P><STRONG><a name="[9f]"></a>AnoPTv8SendCheck</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, anoptv8framefactory.o(.text.AnoPTv8SendCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AnoPTv8SendCheck &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[a1]"></a>AnoPTv8SendCmdInfo</STRONG> (Thumb, 160 bytes, Stack size 208 bytes, anoptv8framefactory.o(.text.AnoPTv8SendCmdInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = AnoPTv8SendCmdInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetInfo
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[a0]"></a>AnoPTv8SendCmdNum</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, anoptv8framefactory.o(.text.AnoPTv8SendCmdNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AnoPTv8SendCmdNum &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[ad]"></a>AnoPTv8SendDevInfo</STRONG> (Thumb, 94 bytes, Stack size 64 bytes, anoptv8framefactory.o(.text.AnoPTv8SendDevInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = AnoPTv8SendDevInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[b0]"></a>AnoPTv8SendParInfo</STRONG> (Thumb, 136 bytes, Stack size 232 bytes, anoptv8framefactory.o(.text.AnoPTv8SendParInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetInfo
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[af]"></a>AnoPTv8SendParNum</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, anoptv8framefactory.o(.text.AnoPTv8SendParNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AnoPTv8SendParNum &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[ae]"></a>AnoPTv8SendParVal</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, anoptv8framefactory.o(.text.AnoPTv8SendParVal))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = AnoPTv8SendParVal &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetInfo
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetParamType
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[a2]"></a>AnoPTv8SendStr</STRONG> (Thumb, 66 bytes, Stack size 112 bytes, anoptv8framefactory.o(.text.AnoPTv8SendStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = AnoPTv8SendStr &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFun_WaveStopAll
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFun_WaveCtrl
</UL>

<P><STRONG><a name="[ab]"></a>AnoPTv8TxLargeBufSend</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, anoptv8run.o(.text.AnoPTv8TxLargeBufSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AnoPTv8TxLargeBufSend &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwTrigger1ms
</UL>

<P><STRONG><a name="[17]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, at32a403a_int.o(.text.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sensor_set_dma_ready
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[a5]"></a>MotorCmdInit</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, motorcmd.o(.text.MotorCmdInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MotorCmdInit
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwInit
</UL>

<P><STRONG><a name="[a4]"></a>MotorParamsInit</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, motorparams.o(.text.MotorParamsInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = MotorParamsInit
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwInit
</UL>

<P><STRONG><a name="[14]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[f0]"></a>PT1000_ADC_to_Temperature</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, pt1000_table.o(.text.PT1000_ADC_to_Temperature))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PT1000_ADC_to_Temperature
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sensor_proc_task
</UL>

<P><STRONG><a name="[1b]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[c9]"></a>Protection_Monitor_GetValue</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, protection_monitor.o(.text.Protection_Monitor_GetValue))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateF2SensorData
</UL>

<P><STRONG><a name="[19]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.SysTick_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>SystemInit</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, system_at32a403a.o(.text.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(.text)
</UL>
<P><STRONG><a name="[35]"></a>TMR1_BRK_TMR9_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.TMR1_BRK_TMR9_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TMR1_OVF_TMR10_IRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, at32a403a_int.o(.text.TMR1_OVF_TMR10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TMR1_OVF_TMR10_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_flag_clear
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>TMR6_GLOBAL_IRQHandler</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, at32a403a_int.o(.text.TMR6_GLOBAL_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TMR6_GLOBAL_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEvent_Handler
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_flag_clear
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_flag_get
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_ordinary_software_trigger_enable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[c1]"></a>TimerEvent_Handler</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, sys_timerevent.o(.text.TimerEvent_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR6_GLOBAL_IRQHandler
</UL>

<P><STRONG><a name="[ea]"></a>TimerEvent_Init</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, sys_timerevent.o(.text.TimerEvent_Init))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c3]"></a>Timer_Tasks_Execute</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, sysfsm.o(.text.Timer_Tasks_Execute))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Timer_Tasks_Execute &rArr; UpdateF1FocData &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateF1FocData
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_ProcessRectifiedAverage
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_CommRead
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[63]"></a>USBFS_MAPL_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a403a_int.o(.text.USBFS_MAPL_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = USBFS_MAPL_IRQHandler &rArr; wk_usbfs_irq_handler &rArr; usbd_irq_handler &rArr; usbd_eptn_handler &rArr; usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usbfs_irq_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[c5]"></a>UpdateF1FocData</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, motordata.o(.text.UpdateF1FocData))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = UpdateF1FocData &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Execute
</UL>

<P><STRONG><a name="[c7]"></a>UpdateF2SensorData</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, motordata.o(.text.UpdateF2SensorData))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UpdateF2SensorData &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Protection_Monitor_GetValue
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sensor_get_value
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sensor_proc_task
</UL>

<P><STRONG><a name="[18]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32a403a_int.o(.text.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32a403a.o(RESET)
</UL>
<P><STRONG><a name="[118]"></a>adc_base_config</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_base_config))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[117]"></a>adc_base_default_para_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_base_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[11e]"></a>adc_calibration_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_calibration_init))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[11f]"></a>adc_calibration_init_status_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_calibration_init_status_get))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[120]"></a>adc_calibration_start</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_calibration_start))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[121]"></a>adc_calibration_status_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_calibration_status_get))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[116]"></a>adc_combine_mode_select</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_combine_mode_select))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[11c]"></a>adc_dma_mode_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_dma_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[11d]"></a>adc_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_enable))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[96]"></a>adc_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_CalibrateOffset
</UL>

<P><STRONG><a name="[9b]"></a>adc_flag_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_CalibrateOffset
</UL>

<P><STRONG><a name="[ed]"></a>adc_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[95]"></a>adc_interrupt_flag_get</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>

<P><STRONG><a name="[119]"></a>adc_ordinary_channel_set</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, at32a403a_adc.o(.text.adc_ordinary_channel_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = adc_ordinary_channel_set
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[11a]"></a>adc_ordinary_conversion_trigger_set</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_ordinary_conversion_trigger_set))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[11b]"></a>adc_ordinary_part_mode_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_ordinary_part_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
</UL>

<P><STRONG><a name="[c2]"></a>adc_ordinary_software_trigger_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_ordinary_software_trigger_enable))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR6_GLOBAL_IRQHandler
</UL>

<P><STRONG><a name="[122]"></a>adc_preempt_channel_length_set</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_preempt_channel_length_set))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[123]"></a>adc_preempt_channel_set</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, at32a403a_adc.o(.text.adc_preempt_channel_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = adc_preempt_channel_set
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[99]"></a>adc_preempt_conversion_trigger_set</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_preempt_conversion_trigger_set))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_CalibrateOffset
</UL>

<P><STRONG><a name="[124]"></a>adc_preempt_offset_value_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_preempt_offset_value_set))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[9a]"></a>adc_preempt_software_trigger_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_preempt_software_trigger_enable))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_CalibrateOffset
</UL>

<P><STRONG><a name="[ca]"></a>adc_reset</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, at32a403a_adc.o(.text.adc_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = adc_reset
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_periph_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[125]"></a>adc_voltage_monitor_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_voltage_monitor_enable))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[126]"></a>adc_voltage_monitor_threshold_value_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_adc.o(.text.adc_voltage_monitor_threshold_value_set))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[115]"></a>crm_adc_clock_div_set</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_adc_clock_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
</UL>

<P><STRONG><a name="[138]"></a>crm_ahb_div_set</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_ahb_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[13a]"></a>crm_apb1_div_set</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_apb1_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[139]"></a>crm_apb2_div_set</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_apb2_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[13b]"></a>crm_auto_step_mode_enable</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_auto_step_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[134]"></a>crm_clock_source_enable</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_clock_source_enable))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[13d]"></a>crm_clocks_freq_get</STRONG> (Thumb, 236 bytes, Stack size 16 bytes, at32a403a_crm.o(.text.crm_clocks_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = crm_clocks_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_timebase_init
</UL>

<P><STRONG><a name="[135]"></a>crm_flag_get</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[136]"></a>crm_hext_stable_wait</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_hext_stable_wait))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[12f]"></a>crm_periph_clock_enable</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_periph_clock_config
</UL>

<P><STRONG><a name="[cb]"></a>crm_periph_reset</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_periph_reset))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_reset
</UL>

<P><STRONG><a name="[137]"></a>crm_pll_config</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, at32a403a_crm.o(.text.crm_pll_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = crm_pll_config
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[133]"></a>crm_reset</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_reset))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[13c]"></a>crm_sysclk_switch</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_sysclk_switch))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[f2]"></a>crm_sysclk_switch_status_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_sysclk_switch_status_get))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_core_clock_update
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[14f]"></a>crm_usb_clock_div_set</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_usb_clock_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usbfs_init
</UL>

<P><STRONG><a name="[14e]"></a>crm_usb_clock_source_select</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_usb_clock_source_select))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usbfs_init
</UL>

<P><STRONG><a name="[14d]"></a>crm_usb_interrupt_remapping_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_crm.o(.text.crm_usb_interrupt_remapping_set))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usbfs_init
</UL>

<P><STRONG><a name="[df]"></a>dma_channel_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a403a_dma.o(.text.dma_channel_enable))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12a]"></a>dma_default_para_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a403a_dma.o(.text.dma_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
</UL>

<P><STRONG><a name="[bb]"></a>dma_flag_clear</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, at32a403a_dma.o(.text.dma_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[12c]"></a>dma_flexible_config</STRONG> (Thumb, 162 bytes, Stack size 0 bytes, at32a403a_dma.o(.text.dma_flexible_config))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
</UL>

<P><STRONG><a name="[12b]"></a>dma_init</STRONG> (Thumb, 124 bytes, Stack size 0 bytes, at32a403a_dma.o(.text.dma_init))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
</UL>

<P><STRONG><a name="[de]"></a>dma_interrupt_enable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a403a_dma.o(.text.dma_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ba]"></a>dma_interrupt_flag_get</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a403a_dma.o(.text.dma_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[129]"></a>dma_reset</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, at32a403a_dma.o(.text.dma_reset))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
</UL>

<P><STRONG><a name="[8d]"></a>gpio_bits_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a403a_gpio.o(.text.gpio_bits_reset))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_CommRead
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>

<P><STRONG><a name="[8e]"></a>gpio_bits_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a403a_gpio.o(.text.gpio_bits_set))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_CommRead
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>

<P><STRONG><a name="[113]"></a>gpio_default_para_init</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_gpio.o(.text.gpio_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
</UL>

<P><STRONG><a name="[114]"></a>gpio_init</STRONG> (Thumb, 190 bytes, Stack size 16 bytes, at32a403a_gpio.o(.text.gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = gpio_init
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
</UL>

<P><STRONG><a name="[127]"></a>gpio_pin_remap_config</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, at32a403a_gpio.o(.text.gpio_pin_remap_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = gpio_pin_remap_config
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_debug_config
</UL>

<P><STRONG><a name="[80]"></a>main</STRONG> (Thumb, 262 bytes, Stack size 0 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 352 + Unknown Stack Size
<LI>Call Chain = main &rArr; wk_usb_app_task &rArr; AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Tasks_Execute
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sensor_proc_task
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwTrigger1ms
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_task
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_interrupt_enable
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_enable
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_enable
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PMSM_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_Init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEvent_Init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sensor_proc_init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwInit
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_delay_ms
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usbfs_init
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr6_init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_counter_enable
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_enable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma_channel_config
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_dma1_channel1_init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc1_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_adc2_init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_gpio_config
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_timebase_init
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_nvic_config
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_debug_config
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_periph_clock_config
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[12e]"></a>nvic_irq_enable</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, at32a403a_misc.o(.text.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_nvic_config
</UL>

<P><STRONG><a name="[12d]"></a>nvic_priority_group_config</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, at32a403a_misc.o(.text.nvic_priority_group_config))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_nvic_config
</UL>

<P><STRONG><a name="[c8]"></a>sensor_get_value</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, sensor_proc.o(.text.sensor_get_value))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateF2SensorData
</UL>

<P><STRONG><a name="[e9]"></a>sensor_proc_init</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, sensor_proc.o(.text.sensor_proc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = sensor_proc_init
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ef]"></a>sensor_proc_task</STRONG> (Thumb, 476 bytes, Stack size 56 bytes, sensor_proc.o(.text.sensor_proc_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = sensor_proc_task &rArr; UpdateF2SensorData &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PT1000_ADC_to_Temperature
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateF2SensorData
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bc]"></a>sensor_set_dma_ready</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sensor_proc.o(.text.sensor_set_dma_ready))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[130]"></a>spi_default_para_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a403a_spi.o(.text.spi_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
</UL>

<P><STRONG><a name="[132]"></a>spi_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_spi.o(.text.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
</UL>

<P><STRONG><a name="[94]"></a>spi_i2s_data_receive</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a403a_spi.o(.text.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>

<P><STRONG><a name="[92]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32a403a_spi.o(.text.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>

<P><STRONG><a name="[93]"></a>spi_i2s_flag_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a403a_spi.o(.text.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_READ
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD2S1210_WRITE
</UL>

<P><STRONG><a name="[131]"></a>spi_init</STRONG> (Thumb, 236 bytes, Stack size 0 bytes, at32a403a_spi.o(.text.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_spi3_init
</UL>

<P><STRONG><a name="[f1]"></a>system_core_clock_update</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, system_at32a403a.o(.text.system_core_clock_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = system_core_clock_update
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch_status_get
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_system_clock_config
</UL>

<P><STRONG><a name="[13e]"></a>systick_clock_source_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, at32a403a_misc.o(.text.systick_clock_source_config))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_timebase_init
</UL>

<P><STRONG><a name="[143]"></a>tmr_base_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_base_init))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr6_init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[14a]"></a>tmr_brkdt_config</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_brkdt_config))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[147]"></a>tmr_channel_value_set</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_channel_value_set))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[140]"></a>tmr_clock_source_div_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_clock_source_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[13f]"></a>tmr_cnt_dir_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_cnt_dir_set))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr6_init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[e2]"></a>tmr_counter_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_counter_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr6_init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14c]"></a>tmr_encoder_mode_config</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_encoder_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
</UL>

<P><STRONG><a name="[c0]"></a>tmr_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR6_GLOBAL_IRQHandler
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR1_OVF_TMR10_IRQHandler
</UL>

<P><STRONG><a name="[14b]"></a>tmr_input_channel_init</STRONG> (Thumb, 460 bytes, Stack size 8 bytes, at32a403a_tmr.o(.text.tmr_input_channel_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = tmr_input_channel_init
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
</UL>

<P><STRONG><a name="[eb]"></a>tmr_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bf]"></a>tmr_interrupt_flag_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR6_GLOBAL_IRQHandler
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR1_OVF_TMR10_IRQHandler
</UL>

<P><STRONG><a name="[148]"></a>tmr_output_channel_buffer_enable</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_output_channel_buffer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[146]"></a>tmr_output_channel_config</STRONG> (Thumb, 250 bytes, Stack size 24 bytes, at32a403a_tmr.o(.text.tmr_output_channel_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tmr_output_channel_config
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[149]"></a>tmr_output_channel_immediately_set</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_output_channel_immediately_set))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[ec]"></a>tmr_output_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_output_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[142]"></a>tmr_period_buffer_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_period_buffer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr6_init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[145]"></a>tmr_primary_mode_select</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_primary_mode_select))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr6_init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[141]"></a>tmr_repetition_counter_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_repetition_counter_set))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[144]"></a>tmr_sub_sync_mode_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_tmr.o(.text.tmr_sub_sync_mode_set))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr3_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_tmr1_init
</UL>

<P><STRONG><a name="[111]"></a>usb_buffer_free</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_buffer_free))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
</UL>

<P><STRONG><a name="[f7]"></a>usb_connect</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_connect))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_connect
</UL>

<P><STRONG><a name="[fb]"></a>usb_dev_init</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_dev_init))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[106]"></a>usb_enter_suspend</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_enter_suspend))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enter_suspend
</UL>

<P><STRONG><a name="[107]"></a>usb_ept_close</STRONG> (Thumb, 218 bytes, Stack size 16 bytes, at32a403a_usb.o(.text.usb_ept_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usb_ept_close
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_close
</UL>

<P><STRONG><a name="[108]"></a>usb_ept_open</STRONG> (Thumb, 808 bytes, Stack size 32 bytes, at32a403a_usb.o(.text.usb_ept_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usb_ept_open
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_open
</UL>

<P><STRONG><a name="[110]"></a>usb_exit_suspend</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_exit_suspend))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[10e]"></a>usb_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[10c]"></a>usb_read_packet</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_read_packet))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_eptn_handler
</UL>

<P><STRONG><a name="[a9]"></a>usb_send_data</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usb_app.o(.text.usb_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_vcp_send_data
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>

<P><STRONG><a name="[10b]"></a>usb_set_address</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_set_address))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_eptn_handler
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
</UL>

<P><STRONG><a name="[fa]"></a>usb_usbbufs_enable</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_usbbufs_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
</UL>

<P><STRONG><a name="[f4]"></a>usb_vcp_get_rxdata</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, cdc_class.o(.text.usb_vcp_get_rxdata))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usb_vcp_get_rxdata
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_recv
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_task
</UL>

<P><STRONG><a name="[f3]"></a>usb_vcp_send_data</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, cdc_class.o(.text.usb_vcp_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_send_data
</UL>

<P><STRONG><a name="[109]"></a>usb_write_packet</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, at32a403a_usb.o(.text.usb_write_packet))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>

<P><STRONG><a name="[104]"></a>usbd_clear_stall</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_clear_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_clear_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
</UL>

<P><STRONG><a name="[f6]"></a>usbd_connect</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_connect))
<BR><BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_connect
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_init
</UL>

<P><STRONG><a name="[f8]"></a>usbd_core_in_handler</STRONG> (Thumb, 222 bytes, Stack size 24 bytes, usbd_core.o(.text.usbd_core_in_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usbd_core_in_handler &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_eptn_handler
</UL>

<P><STRONG><a name="[f9]"></a>usbd_core_init</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, usbd_core.o(.text.usbd_core_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_core_init
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_usbbufs_enable
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dev_init
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usb_app_init
</UL>

<P><STRONG><a name="[fc]"></a>usbd_core_out_handler</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_core_out_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usbd_core_out_handler &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_eptn_handler
</UL>

<P><STRONG><a name="[fd]"></a>usbd_core_setup_handler</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_core_setup_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_interface_request
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_device_request
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_request_parse
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_eptn_handler
</UL>

<P><STRONG><a name="[d1]"></a>usbd_ctrl_recv</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_ctrl_recv))
<BR><BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_setup_handler
</UL>

<P><STRONG><a name="[d3]"></a>usbd_ctrl_send</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_ctrl_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_ctrl_send &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_device_request
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_setup_handler
</UL>

<P><STRONG><a name="[102]"></a>usbd_ctrl_send_status</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_ctrl_send_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_ctrl_send_status &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_interface_request
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_device_request
</UL>

<P><STRONG><a name="[d2]"></a>usbd_ctrl_unsupport</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_ctrl_unsupport))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_ctrl_unsupport
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_interface_request
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_device_request
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_setup_handler
</UL>

<P><STRONG><a name="[ff]"></a>usbd_device_request</STRONG> (Thumb, 582 bytes, Stack size 16 bytes, usbd_sdr.o(.text.usbd_device_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = usbd_device_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_unsupport
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send_status
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
</UL>

<P><STRONG><a name="[101]"></a>usbd_endpoint_request</STRONG> (Thumb, 254 bytes, Stack size 16 bytes, usbd_sdr.o(.text.usbd_endpoint_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_unsupport
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_stall
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_clear_stall
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send_status
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
</UL>

<P><STRONG><a name="[105]"></a>usbd_enter_suspend</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_enter_suspend))
<BR><BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_enter_suspend
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[ce]"></a>usbd_ept_buf_custom_define</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_ept_buf_custom_define))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_init_handler
</UL>

<P><STRONG><a name="[cc]"></a>usbd_ept_close</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_ept_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_ept_close &rArr; usb_ept_close
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_close
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_clear_handler
</UL>

<P><STRONG><a name="[112]"></a>usbd_ept_defaut_init</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_ept_defaut_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_ept_defaut_init
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
</UL>

<P><STRONG><a name="[cf]"></a>usbd_ept_open</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, usbd_core.o(.text.usbd_ept_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usbd_ept_open &rArr; usb_ept_open
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ept_open
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_init_handler
</UL>

<P><STRONG><a name="[d0]"></a>usbd_ept_recv</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_ept_recv))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_eptn_handler
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_vcp_get_rxdata
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_init_handler
</UL>

<P><STRONG><a name="[f5]"></a>usbd_ept_send</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, usbd_core.o(.text.usbd_ept_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_write_packet
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_eptn_handler
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send_status
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_out_handler
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_in_handler
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_vcp_send_data
</UL>

<P><STRONG><a name="[10a]"></a>usbd_eptn_handler</STRONG> (Thumb, 416 bytes, Stack size 24 bytes, usbd_int.o(.text.usbd_eptn_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = usbd_eptn_handler &rArr; usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_out_handler
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_recv
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_send
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_in_handler
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_set_address
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_read_packet
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[cd]"></a>usbd_get_recv_len</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, usbd_core.o(.text.usbd_get_recv_len))
<BR><BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_out_handler
<LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;class_ept0_rx_handler
</UL>

<P><STRONG><a name="[100]"></a>usbd_interface_request</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, usbd_sdr.o(.text.usbd_interface_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usbd_interface_request &rArr; usbd_ctrl_send_status &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_unsupport
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send_status
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
</UL>

<P><STRONG><a name="[10d]"></a>usbd_irq_handler</STRONG> (Thumb, 214 bytes, Stack size 24 bytes, usbd_int.o(.text.usbd_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = usbd_irq_handler &rArr; usbd_eptn_handler &rArr; usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_eptn_handler
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_handler
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enter_suspend
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_flag_clear
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_exit_suspend
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_usbfs_irq_handler
</UL>

<P><STRONG><a name="[10f]"></a>usbd_reset_handler</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, usbd_int.o(.text.usbd_reset_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usbd_reset_handler &rArr; usbd_ept_open &rArr; usb_ept_open
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_defaut_init
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_open
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_buf_custom_define
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_buffer_free
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_set_address
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>

<P><STRONG><a name="[103]"></a>usbd_set_stall</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, usbd_core.o(.text.usbd_set_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_set_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_endpoint_request
</UL>

<P><STRONG><a name="[fe]"></a>usbd_setup_request_parse</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, usbd_sdr.o(.text.usbd_setup_request_parse))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_setup_handler
</UL>

<P><STRONG><a name="[153]"></a>wdt_counter_reload</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32a403a_wdt.o(.text.wdt_counter_reload))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
</UL>

<P><STRONG><a name="[151]"></a>wdt_divider_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32a403a_wdt.o(.text.wdt_divider_set))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
</UL>

<P><STRONG><a name="[150]"></a>wdt_register_write_enable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, at32a403a_wdt.o(.text.wdt_register_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
</UL>

<P><STRONG><a name="[152]"></a>wdt_reload_value_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, at32a403a_wdt.o(.text.wdt_reload_value_set))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_wdt_init
</UL>

<P><STRONG><a name="[db]"></a>wk_adc1_init</STRONG> (Thumb, 250 bytes, Stack size 32 bytes, at32a403a_wk_config.o(.text.wk_adc1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = wk_adc1_init &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_adc_clock_div_set
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_ordinary_part_mode_enable
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_ordinary_conversion_trigger_set
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_ordinary_channel_set
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_status_get
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_start
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_init_status_get
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_mode_enable
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_base_config
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_base_default_para_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_combine_mode_select
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_enable
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[da]"></a>wk_adc2_init</STRONG> (Thumb, 262 bytes, Stack size 32 bytes, at32a403a_wk_config.o(.text.wk_adc2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = wk_adc2_init &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_adc_clock_div_set
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_offset_value_set
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_conversion_trigger_set
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_channel_set
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_preempt_channel_length_set
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_voltage_monitor_threshold_value_set
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_voltage_monitor_enable
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_status_get
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_start
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_init_status_get
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_base_config
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_base_default_para_init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_enable
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d6]"></a>wk_debug_config</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32a403a_wk_config.o(.text.wk_debug_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = wk_debug_config &rArr; gpio_pin_remap_config
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_remap_config
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e8]"></a>wk_delay_ms</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, wk_system.o(.text.wk_delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = wk_delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[128]"></a>wk_delay_us</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, wk_system.o(.text.wk_delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wk_delay_ms
</UL>

<P><STRONG><a name="[dc]"></a>wk_dma1_channel1_init</STRONG> (Thumb, 72 bytes, Stack size 40 bytes, at32a403a_wk_config.o(.text.wk_dma1_channel1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wk_dma1_channel1_init
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_default_para_init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flexible_config
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[dd]"></a>wk_dma_channel_config</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32a403a_wk_config.o(.text.wk_dma_channel_config))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d9]"></a>wk_gpio_config</STRONG> (Thumb, 280 bytes, Stack size 48 bytes, at32a403a_wk_config.o(.text.wk_gpio_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = wk_gpio_config &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_reset
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bits_set
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d7]"></a>wk_nvic_config</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, at32a403a_wk_config.o(.text.wk_nvic_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = wk_nvic_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_config
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d5]"></a>wk_periph_clock_config</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, at32a403a_wk_config.o(.text.wk_periph_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wk_periph_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e0]"></a>wk_spi3_init</STRONG> (Thumb, 140 bytes, Stack size 40 bytes, at32a403a_wk_config.o(.text.wk_spi3_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = wk_spi3_init &rArr; gpio_init
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_default_para_init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d4]"></a>wk_system_clock_config</STRONG> (Thumb, 146 bytes, Stack size 8 bytes, at32a403a_wk_config.o(.text.wk_system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = wk_system_clock_config &rArr; system_core_clock_update
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_core_clock_update
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_auto_step_mode_enable
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch_status_get
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_pll_config
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_apb2_div_set
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_apb1_div_set
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_ahb_div_set
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clock_source_enable
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_hext_stable_wait
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_flag_get
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d8]"></a>wk_timebase_init</STRONG> (Thumb, 72 bytes, Stack size 40 bytes, wk_system.o(.text.wk_timebase_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = wk_timebase_init &rArr; crm_clocks_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_clock_source_config
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clocks_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e1]"></a>wk_tmr1_init</STRONG> (Thumb, 496 bytes, Stack size 56 bytes, at32a403a_wk_config.o(.text.wk_tmr1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = wk_tmr1_init &rArr; tmr_output_channel_config
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_brkdt_config
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_sub_sync_mode_set
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_primary_mode_select
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_channel_immediately_set
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_channel_buffer_enable
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_period_buffer_enable
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_channel_value_set
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_channel_config
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_repetition_counter_set
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_cnt_dir_set
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_clock_source_div_set
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_base_init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_output_enable
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_counter_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e3]"></a>wk_tmr3_init</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, at32a403a_wk_config.o(.text.wk_tmr3_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = wk_tmr3_init &rArr; gpio_pin_remap_config
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_encoder_mode_config
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_sub_sync_mode_set
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_primary_mode_select
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_input_channel_init
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_period_buffer_enable
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_cnt_dir_set
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_clock_source_div_set
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_base_init
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_remap_config
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_counter_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e4]"></a>wk_tmr6_init</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, at32a403a_wk_config.o(.text.wk_tmr6_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wk_tmr6_init
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_primary_mode_select
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_period_buffer_enable
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_cnt_dir_set
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_base_init
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_counter_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e7]"></a>wk_usb_app_init</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, usb_app.o(.text.wk_usb_app_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = wk_usb_app_init &rArr; usbd_core_init
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_core_init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_connect
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ee]"></a>wk_usb_app_task</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, usb_app.o(.text.wk_usb_app_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = wk_usb_app_task &rArr; AnoPTv8HwRecvBytes &rArr; AnoPTv8RecvBytes &rArr; AnoPTv8ParFrameAnl &rArr; AnoPTv8SendParInfo &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwRecvBytes
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_vcp_get_rxdata
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e5]"></a>wk_usbfs_init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, at32a403a_wk_config.o(.text.wk_usbfs_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wk_usbfs_init
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_usb_clock_source_select
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_usb_interrupt_remapping_set
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_usb_clock_div_set
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c6]"></a>wk_usbfs_irq_handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, usb_app.o(.text.wk_usbfs_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = wk_usbfs_irq_handler &rArr; usbd_irq_handler &rArr; usbd_eptn_handler &rArr; usbd_core_setup_handler &rArr; usbd_endpoint_request &rArr; usbd_ctrl_send &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_irq_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_MAPL_IRQHandler
</UL>

<P><STRONG><a name="[e6]"></a>wk_wdt_init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, at32a403a_wk_config.o(.text.wk_wdt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wk_wdt_init
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_register_write_enable
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_divider_set
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_reload_value_set
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_counter_reload
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7a]"></a>_fp_init</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[18b]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[18c]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[0]"></a>class_init_handler</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, cdc_class.o(.text.class_init_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = class_init_handler &rArr; usbd_ept_open &rArr; usb_ept_open
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_open
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_buf_custom_define
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_recv
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[1]"></a>class_clear_handler</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, cdc_class.o(.text.class_clear_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = class_clear_handler &rArr; usbd_ept_close &rArr; usb_ept_close
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ept_close
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[2]"></a>class_setup_handler</STRONG> (Thumb, 172 bytes, Stack size 8 bytes, cdc_class.o(.text.class_setup_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = class_setup_handler &rArr; usbd_ctrl_send &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_unsupport
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_recv
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctrl_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[3]"></a>class_ept0_tx_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_class.o(.text.class_ept0_tx_handler))
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[4]"></a>class_ept0_rx_handler</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, cdc_class.o(.text.class_ept0_rx_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = class_ept0_rx_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_get_recv_len
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[5]"></a>class_in_handler</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, cdc_class.o(.text.class_in_handler))
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[6]"></a>class_out_handler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, cdc_class.o(.text.class_out_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = class_out_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_get_recv_len
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[7]"></a>class_sof_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_class.o(.text.class_sof_handler))
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[8]"></a>class_event_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_class.o(.text.class_event_handler))
<BR>[Address Reference Count : 1]<UL><LI> cdc_class.o(.data.cdc_class_handler)
</UL>
<P><STRONG><a name="[9]"></a>get_device_descriptor</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_descriptor))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[a]"></a>get_device_qualifier</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_qualifier))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[b]"></a>get_device_configuration</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_configuration))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[c]"></a>get_device_other_speed</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_other_speed))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[d]"></a>get_device_lang_id</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, cdc_desc.o(.text.get_device_lang_id))
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[e]"></a>get_device_manufacturer_string</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, cdc_desc.o(.text.get_device_manufacturer_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_device_manufacturer_string
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[f]"></a>get_device_product_string</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, cdc_desc.o(.text.get_device_product_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_device_product_string
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[10]"></a>get_device_serial_string</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, cdc_desc.o(.text.get_device_serial_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = get_device_serial_string
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[11]"></a>get_device_interface_string</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, cdc_desc.o(.text.get_device_interface_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_device_interface_string
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[12]"></a>get_device_config_string</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, cdc_desc.o(.text.get_device_config_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_device_config_string
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_desc.o(.data.cdc_desc_handler)
</UL>
<P><STRONG><a name="[70]"></a>AnoPTv8CmdFun_MotorStart</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorStart))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorStart)
</UL>
<P><STRONG><a name="[71]"></a>AnoPTv8CmdFun_MotorStop</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorStop))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorStop)
</UL>
<P><STRONG><a name="[6d]"></a>AnoPTv8CmdFun_MotorReset</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorReset))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorEStop)
</UL>
<P><STRONG><a name="[6f]"></a>AnoPTv8CmdFun_MotorSelfTest</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorSelfTest))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorSelfTest)
</UL>
<P><STRONG><a name="[6e]"></a>AnoPTv8CmdFun_MotorGetFault</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorGetFault))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorGetFault)
</UL>
<P><STRONG><a name="[6c]"></a>AnoPTv8CmdFun_MotorClearFault</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_MotorClearFault))
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoMotorClearFault)
</UL>
<P><STRONG><a name="[72]"></a>AnoPTv8CmdFun_WaveCtrl</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, motorcmd.o(.text.AnoPTv8CmdFun_WaveCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = AnoPTv8CmdFun_WaveCtrl &rArr; AnoPTv8SendStr &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoWaveCtrl)
</UL>
<P><STRONG><a name="[73]"></a>AnoPTv8CmdFun_WaveStopAll</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, motorcmd.o(.text.AnoPTv8CmdFun_WaveStopAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = AnoPTv8CmdFun_WaveStopAll &rArr; AnoPTv8SendStr &rArr; AnoPTv8SendBuf &rArr; AnoPTv8HwSendBytes &rArr; usb_send_data &rArr; usb_vcp_send_data &rArr; usbd_ept_send
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> motorcmd.o(.rodata._pCmdInfoWaveStopAll)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
