Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_system_clock_config) for wk_system_clock_config
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_periph_clock_config) for wk_periph_clock_config
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_debug_config) for wk_debug_config
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_nvic_config) for wk_nvic_config
    main.o(.text.main) refers to wk_system.o(.text.wk_timebase_init) for wk_timebase_init
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_gpio_config) for wk_gpio_config
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_adc2_init) for wk_adc2_init
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_adc1_init) for wk_adc1_init
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_dma1_channel1_init) for wk_dma1_channel1_init
    main.o(.text.main) refers to main.o(.bss.adc1_dma_buffer) for adc1_dma_buffer
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_dma_channel_config) for wk_dma_channel_config
    main.o(.text.main) refers to at32a403a_dma.o(.text.dma_interrupt_enable) for dma_interrupt_enable
    main.o(.text.main) refers to at32a403a_dma.o(.text.dma_channel_enable) for dma_channel_enable
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_spi3_init) for wk_spi3_init
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_tmr1_init) for wk_tmr1_init
    main.o(.text.main) refers to at32a403a_tmr.o(.text.tmr_counter_enable) for tmr_counter_enable
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_tmr3_init) for wk_tmr3_init
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_tmr6_init) for wk_tmr6_init
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_usbfs_init) for wk_usbfs_init
    main.o(.text.main) refers to at32a403a_wk_config.o(.text.wk_wdt_init) for wk_wdt_init
    main.o(.text.main) refers to usb_app.o(.text.wk_usb_app_init) for wk_usb_app_init
    main.o(.text.main) refers to wk_system.o(.text.wk_delay_ms) for wk_delay_ms
    main.o(.text.main) refers to hwinterface.o(.text.AnoPTv8HwInit) for AnoPTv8HwInit
    main.o(.text.main) refers to sensor_proc.o(.text.sensor_proc_init) for sensor_proc_init
    main.o(.text.main) refers to sys_timerevent.o(.text.TimerEvent_Init) for TimerEvent_Init
    main.o(.text.main) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    main.o(.text.main) refers to ad2s1212_spi.o(.text.AD2S1210_Init) for AD2S1210_Init
    main.o(.text.main) refers to adc_pmsm.o(.text.ADC_PMSM_Init) for ADC_PMSM_Init
    main.o(.text.main) refers to at32a403a_tmr.o(.text.tmr_interrupt_enable) for tmr_interrupt_enable
    main.o(.text.main) refers to usb_app.o(.text.wk_usb_app_task) for wk_usb_app_task
    main.o(.text.main) refers to hwinterface.o(.text.AnoPTv8HwTrigger1ms) for AnoPTv8HwTrigger1ms
    main.o(.text.main) refers to sensor_proc.o(.text.sensor_proc_task) for sensor_proc_task
    main.o(.text.main) refers to sysfsm.o(.text.Timer_Tasks_Execute) for Timer_Tasks_Execute
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    sysfsm.o(.text.Timer_Tasks_Execute) refers to sys_timerevent.o(.bss.gTimerFlag) for gTimerFlag
    sysfsm.o(.text.Timer_Tasks_Execute) refers to ad2s1212_spi.o(.text.AD2S1210_CommRead) for AD2S1210_CommRead
    sysfsm.o(.text.Timer_Tasks_Execute) refers to sysfsm.o(.bss.jiaodu) for jiaodu
    sysfsm.o(.text.Timer_Tasks_Execute) refers to adc_pmsm.o(.text.ADC_PMSM_ProcessRectifiedAverage) for ADC_PMSM_ProcessRectifiedAverage
    sysfsm.o(.text.Timer_Tasks_Execute) refers to motordata.o(.text.UpdateF1FocData) for UpdateF1FocData
    sysfsm.o(.text.Timer_Tasks_Execute) refers to sysfsm.o(.bss.ms_cnt) for ms_cnt
    sysfsm.o(.text.Timer_Tasks_Execute) refers to sysfsm.o(.bss.test_time) for test_time
    sysfsm.o(.ARM.exidx.text.Timer_Tasks_Execute) refers to sysfsm.o(.text.Timer_Tasks_Execute) for [Anonymous Symbol]
    at32a403a_adc.o(.text.adc_reset) refers to at32a403a_crm.o(.text.crm_periph_reset) for crm_periph_reset
    at32a403a_adc.o(.ARM.exidx.text.adc_reset) refers to at32a403a_adc.o(.text.adc_reset) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_enable) refers to at32a403a_adc.o(.text.adc_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_combine_mode_select) refers to at32a403a_adc.o(.text.adc_combine_mode_select) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_base_default_para_init) refers to at32a403a_adc.o(.text.adc_base_default_para_init) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_base_config) refers to at32a403a_adc.o(.text.adc_base_config) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_dma_mode_enable) refers to at32a403a_adc.o(.text.adc_dma_mode_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_interrupt_enable) refers to at32a403a_adc.o(.text.adc_interrupt_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_calibration_init) refers to at32a403a_adc.o(.text.adc_calibration_init) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_calibration_init_status_get) refers to at32a403a_adc.o(.text.adc_calibration_init_status_get) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_calibration_start) refers to at32a403a_adc.o(.text.adc_calibration_start) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_calibration_status_get) refers to at32a403a_adc.o(.text.adc_calibration_status_get) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_voltage_monitor_enable) refers to at32a403a_adc.o(.text.adc_voltage_monitor_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_voltage_monitor_threshold_value_set) refers to at32a403a_adc.o(.text.adc_voltage_monitor_threshold_value_set) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_voltage_monitor_single_channel_select) refers to at32a403a_adc.o(.text.adc_voltage_monitor_single_channel_select) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_channel_set) refers to at32a403a_adc.o(.text.adc_ordinary_channel_set) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_preempt_channel_length_set) refers to at32a403a_adc.o(.text.adc_preempt_channel_length_set) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_preempt_channel_set) refers to at32a403a_adc.o(.text.adc_preempt_channel_set) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_conversion_trigger_set) refers to at32a403a_adc.o(.text.adc_ordinary_conversion_trigger_set) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_preempt_conversion_trigger_set) refers to at32a403a_adc.o(.text.adc_preempt_conversion_trigger_set) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_preempt_offset_value_set) refers to at32a403a_adc.o(.text.adc_preempt_offset_value_set) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_part_count_set) refers to at32a403a_adc.o(.text.adc_ordinary_part_count_set) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_part_mode_enable) refers to at32a403a_adc.o(.text.adc_ordinary_part_mode_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_preempt_part_mode_enable) refers to at32a403a_adc.o(.text.adc_preempt_part_mode_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_preempt_auto_mode_enable) refers to at32a403a_adc.o(.text.adc_preempt_auto_mode_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_tempersensor_vintrv_enable) refers to at32a403a_adc.o(.text.adc_tempersensor_vintrv_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_software_trigger_enable) refers to at32a403a_adc.o(.text.adc_ordinary_software_trigger_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_software_trigger_status_get) refers to at32a403a_adc.o(.text.adc_ordinary_software_trigger_status_get) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_preempt_software_trigger_enable) refers to at32a403a_adc.o(.text.adc_preempt_software_trigger_enable) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_preempt_software_trigger_status_get) refers to at32a403a_adc.o(.text.adc_preempt_software_trigger_status_get) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_conversion_data_get) refers to at32a403a_adc.o(.text.adc_ordinary_conversion_data_get) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_combine_ordinary_conversion_data_get) refers to at32a403a_adc.o(.text.adc_combine_ordinary_conversion_data_get) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_preempt_conversion_data_get) refers to at32a403a_adc.o(.text.adc_preempt_conversion_data_get) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_flag_get) refers to at32a403a_adc.o(.text.adc_flag_get) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_interrupt_flag_get) refers to at32a403a_adc.o(.text.adc_interrupt_flag_get) for [Anonymous Symbol]
    at32a403a_adc.o(.ARM.exidx.text.adc_flag_clear) refers to at32a403a_adc.o(.text.adc_flag_clear) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_reset) refers to at32a403a_crm.o(.text.crm_reset) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_lext_bypass) refers to at32a403a_crm.o(.text.crm_lext_bypass) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_hext_bypass) refers to at32a403a_crm.o(.text.crm_hext_bypass) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_flag_get) refers to at32a403a_crm.o(.text.crm_flag_get) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_interrupt_flag_get) refers to at32a403a_crm.o(.text.crm_interrupt_flag_get) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_hext_stable_wait) refers to at32a403a_crm.o(.text.crm_hext_stable_wait) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_hick_clock_trimming_set) refers to at32a403a_crm.o(.text.crm_hick_clock_trimming_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_hick_clock_calibration_set) refers to at32a403a_crm.o(.text.crm_hick_clock_calibration_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_periph_clock_enable) refers to at32a403a_crm.o(.text.crm_periph_clock_enable) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_periph_reset) refers to at32a403a_crm.o(.text.crm_periph_reset) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_periph_sleep_mode_clock_enable) refers to at32a403a_crm.o(.text.crm_periph_sleep_mode_clock_enable) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_clock_source_enable) refers to at32a403a_crm.o(.text.crm_clock_source_enable) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_flag_clear) refers to at32a403a_crm.o(.text.crm_flag_clear) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_rtc_clock_select) refers to at32a403a_crm.o(.text.crm_rtc_clock_select) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_rtc_clock_enable) refers to at32a403a_crm.o(.text.crm_rtc_clock_enable) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_ahb_div_set) refers to at32a403a_crm.o(.text.crm_ahb_div_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_apb1_div_set) refers to at32a403a_crm.o(.text.crm_apb1_div_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_apb2_div_set) refers to at32a403a_crm.o(.text.crm_apb2_div_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_adc_clock_div_set) refers to at32a403a_crm.o(.text.crm_adc_clock_div_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_usb_clock_div_set) refers to at32a403a_crm.o(.text.crm_usb_clock_div_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_clock_failure_detection_enable) refers to at32a403a_crm.o(.text.crm_clock_failure_detection_enable) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_battery_powered_domain_reset) refers to at32a403a_crm.o(.text.crm_battery_powered_domain_reset) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_pll_config) refers to at32a403a_crm.o(.text.crm_pll_config) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_sysclk_switch) refers to at32a403a_crm.o(.text.crm_sysclk_switch) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_sysclk_switch_status_get) refers to at32a403a_crm.o(.text.crm_sysclk_switch_status_get) for [Anonymous Symbol]
    at32a403a_crm.o(.text.crm_clocks_freq_get) refers to at32a403a_crm.o(.rodata.cst8) for crm_clocks_freq_get.ahb_apb2_div_table
    at32a403a_crm.o(.ARM.exidx.text.crm_clocks_freq_get) refers to at32a403a_crm.o(.text.crm_clocks_freq_get) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_clock_out_set) refers to at32a403a_crm.o(.text.crm_clock_out_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_interrupt_enable) refers to at32a403a_crm.o(.text.crm_interrupt_enable) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_auto_step_mode_enable) refers to at32a403a_crm.o(.text.crm_auto_step_mode_enable) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_usb_interrupt_remapping_set) refers to at32a403a_crm.o(.text.crm_usb_interrupt_remapping_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_hick_divider_select) refers to at32a403a_crm.o(.text.crm_hick_divider_select) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_hick_sclk_frequency_select) refers to at32a403a_crm.o(.text.crm_hick_sclk_frequency_select) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_usb_clock_source_select) refers to at32a403a_crm.o(.text.crm_usb_clock_source_select) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_clkout_to_tmr10_enable) refers to at32a403a_crm.o(.text.crm_clkout_to_tmr10_enable) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_hext_clock_div_set) refers to at32a403a_crm.o(.text.crm_hext_clock_div_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_clkout_div_set) refers to at32a403a_crm.o(.text.crm_clkout_div_set) for [Anonymous Symbol]
    at32a403a_crm.o(.ARM.exidx.text.crm_emac_output_pulse_set) refers to at32a403a_crm.o(.text.crm_emac_output_pulse_set) for [Anonymous Symbol]
    at32a403a_debug.o(.ARM.exidx.text.debug_device_id_get) refers to at32a403a_debug.o(.text.debug_device_id_get) for [Anonymous Symbol]
    at32a403a_debug.o(.ARM.exidx.text.debug_periph_mode_set) refers to at32a403a_debug.o(.text.debug_periph_mode_set) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_reset) refers to at32a403a_dma.o(.text.dma_reset) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_data_number_set) refers to at32a403a_dma.o(.text.dma_data_number_set) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_data_number_get) refers to at32a403a_dma.o(.text.dma_data_number_get) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_interrupt_enable) refers to at32a403a_dma.o(.text.dma_interrupt_enable) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_channel_enable) refers to at32a403a_dma.o(.text.dma_channel_enable) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_flexible_config) refers to at32a403a_dma.o(.text.dma_flexible_config) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_flag_get) refers to at32a403a_dma.o(.text.dma_flag_get) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_interrupt_flag_get) refers to at32a403a_dma.o(.text.dma_interrupt_flag_get) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_flag_clear) refers to at32a403a_dma.o(.text.dma_flag_clear) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_default_para_init) refers to at32a403a_dma.o(.text.dma_default_para_init) for [Anonymous Symbol]
    at32a403a_dma.o(.ARM.exidx.text.dma_init) refers to at32a403a_dma.o(.text.dma_init) for [Anonymous Symbol]
    at32a403a_exint.o(.ARM.exidx.text.exint_reset) refers to at32a403a_exint.o(.text.exint_reset) for [Anonymous Symbol]
    at32a403a_exint.o(.ARM.exidx.text.exint_default_para_init) refers to at32a403a_exint.o(.text.exint_default_para_init) for [Anonymous Symbol]
    at32a403a_exint.o(.ARM.exidx.text.exint_init) refers to at32a403a_exint.o(.text.exint_init) for [Anonymous Symbol]
    at32a403a_exint.o(.ARM.exidx.text.exint_flag_clear) refers to at32a403a_exint.o(.text.exint_flag_clear) for [Anonymous Symbol]
    at32a403a_exint.o(.ARM.exidx.text.exint_flag_get) refers to at32a403a_exint.o(.text.exint_flag_get) for [Anonymous Symbol]
    at32a403a_exint.o(.ARM.exidx.text.exint_interrupt_flag_get) refers to at32a403a_exint.o(.text.exint_interrupt_flag_get) for [Anonymous Symbol]
    at32a403a_exint.o(.ARM.exidx.text.exint_software_interrupt_event_generate) refers to at32a403a_exint.o(.text.exint_software_interrupt_event_generate) for [Anonymous Symbol]
    at32a403a_exint.o(.ARM.exidx.text.exint_interrupt_enable) refers to at32a403a_exint.o(.text.exint_interrupt_enable) for [Anonymous Symbol]
    at32a403a_exint.o(.ARM.exidx.text.exint_event_enable) refers to at32a403a_exint.o(.text.exint_event_enable) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_flag_get) refers to at32a403a_flash.o(.text.flash_flag_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_flag_clear) refers to at32a403a_flash.o(.text.flash_flag_clear) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_operation_status_get) refers to at32a403a_flash.o(.text.flash_operation_status_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank1_operation_status_get) refers to at32a403a_flash.o(.text.flash_bank1_operation_status_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank2_operation_status_get) refers to at32a403a_flash.o(.text.flash_bank2_operation_status_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_spim_operation_status_get) refers to at32a403a_flash.o(.text.flash_spim_operation_status_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_operation_wait_for) refers to at32a403a_flash.o(.text.flash_operation_wait_for) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank1_operation_wait_for) refers to at32a403a_flash.o(.text.flash_bank1_operation_wait_for) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank2_operation_wait_for) refers to at32a403a_flash.o(.text.flash_bank2_operation_wait_for) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_spim_operation_wait_for) refers to at32a403a_flash.o(.text.flash_spim_operation_wait_for) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_unlock) refers to at32a403a_flash.o(.text.flash_unlock) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank1_unlock) refers to at32a403a_flash.o(.text.flash_bank1_unlock) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank2_unlock) refers to at32a403a_flash.o(.text.flash_bank2_unlock) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_spim_unlock) refers to at32a403a_flash.o(.text.flash_spim_unlock) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_lock) refers to at32a403a_flash.o(.text.flash_lock) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank1_lock) refers to at32a403a_flash.o(.text.flash_bank1_lock) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank2_lock) refers to at32a403a_flash.o(.text.flash_bank2_lock) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_spim_lock) refers to at32a403a_flash.o(.text.flash_spim_lock) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_sector_erase) refers to at32a403a_flash.o(.text.flash_sector_erase) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_spim_dummy_read) refers to at32a403a_flash.o(.text.flash_spim_dummy_read) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_internal_all_erase) refers to at32a403a_flash.o(.text.flash_internal_all_erase) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank1_erase) refers to at32a403a_flash.o(.text.flash_bank1_erase) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_bank2_erase) refers to at32a403a_flash.o(.text.flash_bank2_erase) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_spim_all_erase) refers to at32a403a_flash.o(.text.flash_spim_all_erase) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_user_system_data_erase) refers to at32a403a_flash.o(.text.flash_user_system_data_erase) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_fap_status_get) refers to at32a403a_flash.o(.text.flash_fap_status_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_word_program) refers to at32a403a_flash.o(.text.flash_word_program) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_halfword_program) refers to at32a403a_flash.o(.text.flash_halfword_program) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_byte_program) refers to at32a403a_flash.o(.text.flash_byte_program) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_user_system_data_program) refers to at32a403a_flash.o(.text.flash_user_system_data_program) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_epp_set) refers to at32a403a_flash.o(.text.flash_epp_set) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_epp_status_get) refers to at32a403a_flash.o(.text.flash_epp_status_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_fap_enable) refers to at32a403a_flash.o(.text.flash_fap_enable) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_ssb_set) refers to at32a403a_flash.o(.text.flash_ssb_set) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_ssb_status_get) refers to at32a403a_flash.o(.text.flash_ssb_status_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_interrupt_enable) refers to at32a403a_flash.o(.text.flash_interrupt_enable) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_spim_model_select) refers to at32a403a_flash.o(.text.flash_spim_model_select) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_spim_encryption_range_set) refers to at32a403a_flash.o(.text.flash_spim_encryption_range_set) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_spim_mass_program) refers to at32a403a_flash.o(.text.flash_spim_mass_program) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_slib_enable) refers to at32a403a_flash.o(.text.flash_slib_enable) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_slib_disable) refers to at32a403a_flash.o(.text.flash_slib_disable) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_slib_remaining_count_get) refers to at32a403a_flash.o(.text.flash_slib_remaining_count_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_slib_state_get) refers to at32a403a_flash.o(.text.flash_slib_state_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_slib_start_sector_get) refers to at32a403a_flash.o(.text.flash_slib_start_sector_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_slib_datastart_sector_get) refers to at32a403a_flash.o(.text.flash_slib_datastart_sector_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_slib_end_sector_get) refers to at32a403a_flash.o(.text.flash_slib_end_sector_get) for [Anonymous Symbol]
    at32a403a_flash.o(.ARM.exidx.text.flash_crc_calibrate) refers to at32a403a_flash.o(.text.flash_crc_calibrate) for [Anonymous Symbol]
    at32a403a_gpio.o(.text.gpio_reset) refers to at32a403a_crm.o(.text.crm_periph_reset) for crm_periph_reset
    at32a403a_gpio.o(.ARM.exidx.text.gpio_reset) refers to at32a403a_gpio.o(.text.gpio_reset) for [Anonymous Symbol]
    at32a403a_gpio.o(.text.gpio_iomux_reset) refers to at32a403a_crm.o(.text.crm_periph_reset) for crm_periph_reset
    at32a403a_gpio.o(.ARM.exidx.text.gpio_iomux_reset) refers to at32a403a_gpio.o(.text.gpio_iomux_reset) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_init) refers to at32a403a_gpio.o(.text.gpio_init) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_default_para_init) refers to at32a403a_gpio.o(.text.gpio_default_para_init) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_input_data_bit_read) refers to at32a403a_gpio.o(.text.gpio_input_data_bit_read) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_input_data_read) refers to at32a403a_gpio.o(.text.gpio_input_data_read) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_output_data_bit_read) refers to at32a403a_gpio.o(.text.gpio_output_data_bit_read) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_output_data_read) refers to at32a403a_gpio.o(.text.gpio_output_data_read) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_bits_set) refers to at32a403a_gpio.o(.text.gpio_bits_set) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_bits_reset) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_bits_toggle) refers to at32a403a_gpio.o(.text.gpio_bits_toggle) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_bits_write) refers to at32a403a_gpio.o(.text.gpio_bits_write) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_port_write) refers to at32a403a_gpio.o(.text.gpio_port_write) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_pin_wp_config) refers to at32a403a_gpio.o(.text.gpio_pin_wp_config) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_pins_huge_driven_config) refers to at32a403a_gpio.o(.text.gpio_pins_huge_driven_config) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_event_output_config) refers to at32a403a_gpio.o(.text.gpio_event_output_config) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_event_output_enable) refers to at32a403a_gpio.o(.text.gpio_event_output_enable) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_pin_remap_config) refers to at32a403a_gpio.o(.text.gpio_pin_remap_config) for [Anonymous Symbol]
    at32a403a_gpio.o(.ARM.exidx.text.gpio_exint_line_config) refers to at32a403a_gpio.o(.text.gpio_exint_line_config) for [Anonymous Symbol]
    at32a403a_misc.o(.text.nvic_system_reset) refers to at32a403a_misc.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    at32a403a_misc.o(.ARM.exidx.text.nvic_system_reset) refers to at32a403a_misc.o(.text.nvic_system_reset) for [Anonymous Symbol]
    at32a403a_misc.o(.ARM.exidx.text.__NVIC_SystemReset) refers to at32a403a_misc.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    at32a403a_misc.o(.ARM.exidx.text.nvic_irq_enable) refers to at32a403a_misc.o(.text.nvic_irq_enable) for [Anonymous Symbol]
    at32a403a_misc.o(.ARM.exidx.text.nvic_irq_disable) refers to at32a403a_misc.o(.text.nvic_irq_disable) for [Anonymous Symbol]
    at32a403a_misc.o(.ARM.exidx.text.nvic_priority_group_config) refers to at32a403a_misc.o(.text.nvic_priority_group_config) for [Anonymous Symbol]
    at32a403a_misc.o(.ARM.exidx.text.nvic_vector_table_set) refers to at32a403a_misc.o(.text.nvic_vector_table_set) for [Anonymous Symbol]
    at32a403a_misc.o(.ARM.exidx.text.nvic_lowpower_mode_config) refers to at32a403a_misc.o(.text.nvic_lowpower_mode_config) for [Anonymous Symbol]
    at32a403a_misc.o(.ARM.exidx.text.systick_clock_source_config) refers to at32a403a_misc.o(.text.systick_clock_source_config) for [Anonymous Symbol]
    at32a403a_pwc.o(.text.pwc_reset) refers to at32a403a_crm.o(.text.crm_periph_reset) for crm_periph_reset
    at32a403a_pwc.o(.ARM.exidx.text.pwc_reset) refers to at32a403a_pwc.o(.text.pwc_reset) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_battery_powered_domain_access) refers to at32a403a_pwc.o(.text.pwc_battery_powered_domain_access) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_pvm_level_select) refers to at32a403a_pwc.o(.text.pwc_pvm_level_select) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_power_voltage_monitor_enable) refers to at32a403a_pwc.o(.text.pwc_power_voltage_monitor_enable) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_wakeup_pin_enable) refers to at32a403a_pwc.o(.text.pwc_wakeup_pin_enable) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_flag_clear) refers to at32a403a_pwc.o(.text.pwc_flag_clear) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_flag_get) refers to at32a403a_pwc.o(.text.pwc_flag_get) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_sleep_mode_enter) refers to at32a403a_pwc.o(.text.pwc_sleep_mode_enter) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_deep_sleep_mode_enter) refers to at32a403a_pwc.o(.text.pwc_deep_sleep_mode_enter) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_voltage_regulate_set) refers to at32a403a_pwc.o(.text.pwc_voltage_regulate_set) for [Anonymous Symbol]
    at32a403a_pwc.o(.ARM.exidx.text.pwc_standby_mode_enter) refers to at32a403a_pwc.o(.text.pwc_standby_mode_enter) for [Anonymous Symbol]
    at32a403a_spi.o(.text.spi_i2s_reset) refers to at32a403a_crm.o(.text.crm_periph_reset) for crm_periph_reset
    at32a403a_spi.o(.ARM.exidx.text.spi_i2s_reset) refers to at32a403a_spi.o(.text.spi_i2s_reset) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_default_para_init) refers to at32a403a_spi.o(.text.spi_default_para_init) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_init) refers to at32a403a_spi.o(.text.spi_init) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_crc_next_transmit) refers to at32a403a_spi.o(.text.spi_crc_next_transmit) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_crc_polynomial_set) refers to at32a403a_spi.o(.text.spi_crc_polynomial_set) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_crc_polynomial_get) refers to at32a403a_spi.o(.text.spi_crc_polynomial_get) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_crc_enable) refers to at32a403a_spi.o(.text.spi_crc_enable) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_crc_value_get) refers to at32a403a_spi.o(.text.spi_crc_value_get) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_hardware_cs_output_enable) refers to at32a403a_spi.o(.text.spi_hardware_cs_output_enable) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_software_cs_internal_level_set) refers to at32a403a_spi.o(.text.spi_software_cs_internal_level_set) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_frame_bit_num_set) refers to at32a403a_spi.o(.text.spi_frame_bit_num_set) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_half_duplex_direction_set) refers to at32a403a_spi.o(.text.spi_half_duplex_direction_set) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_enable) refers to at32a403a_spi.o(.text.spi_enable) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.i2s_default_para_init) refers to at32a403a_spi.o(.text.i2s_default_para_init) for [Anonymous Symbol]
    at32a403a_spi.o(.text.i2s_init) refers to at32a403a_crm.o(.text.crm_clocks_freq_get) for crm_clocks_freq_get
    at32a403a_spi.o(.ARM.exidx.text.i2s_init) refers to at32a403a_spi.o(.text.i2s_init) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.i2s_enable) refers to at32a403a_spi.o(.text.i2s_enable) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_i2s_interrupt_enable) refers to at32a403a_spi.o(.text.spi_i2s_interrupt_enable) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_i2s_dma_transmitter_enable) refers to at32a403a_spi.o(.text.spi_i2s_dma_transmitter_enable) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_i2s_dma_receiver_enable) refers to at32a403a_spi.o(.text.spi_i2s_dma_receiver_enable) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_i2s_data_transmit) refers to at32a403a_spi.o(.text.spi_i2s_data_transmit) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_i2s_data_receive) refers to at32a403a_spi.o(.text.spi_i2s_data_receive) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_i2s_flag_get) refers to at32a403a_spi.o(.text.spi_i2s_flag_get) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_i2s_interrupt_flag_get) refers to at32a403a_spi.o(.text.spi_i2s_interrupt_flag_get) for [Anonymous Symbol]
    at32a403a_spi.o(.ARM.exidx.text.spi_i2s_flag_clear) refers to at32a403a_spi.o(.text.spi_i2s_flag_clear) for [Anonymous Symbol]
    at32a403a_tmr.o(.text.tmr_reset) refers to at32a403a_crm.o(.text.crm_periph_reset) for crm_periph_reset
    at32a403a_tmr.o(.ARM.exidx.text.tmr_reset) refers to at32a403a_tmr.o(.text.tmr_reset) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_counter_enable) refers to at32a403a_tmr.o(.text.tmr_counter_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_output_default_para_init) refers to at32a403a_tmr.o(.text.tmr_output_default_para_init) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_input_default_para_init) refers to at32a403a_tmr.o(.text.tmr_input_default_para_init) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_brkdt_default_para_init) refers to at32a403a_tmr.o(.text.tmr_brkdt_default_para_init) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_base_init) refers to at32a403a_tmr.o(.text.tmr_base_init) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_clock_source_div_set) refers to at32a403a_tmr.o(.text.tmr_clock_source_div_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_cnt_dir_set) refers to at32a403a_tmr.o(.text.tmr_cnt_dir_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_repetition_counter_set) refers to at32a403a_tmr.o(.text.tmr_repetition_counter_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_counter_value_set) refers to at32a403a_tmr.o(.text.tmr_counter_value_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_counter_value_get) refers to at32a403a_tmr.o(.text.tmr_counter_value_get) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_div_value_set) refers to at32a403a_tmr.o(.text.tmr_div_value_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_div_value_get) refers to at32a403a_tmr.o(.text.tmr_div_value_get) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_config) refers to at32a403a_tmr.o(.text.tmr_output_channel_config) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_mode_select) refers to at32a403a_tmr.o(.text.tmr_output_channel_mode_select) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_period_value_set) refers to at32a403a_tmr.o(.text.tmr_period_value_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_period_value_get) refers to at32a403a_tmr.o(.text.tmr_period_value_get) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_value_set) refers to at32a403a_tmr.o(.text.tmr_channel_value_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_value_get) refers to at32a403a_tmr.o(.text.tmr_channel_value_get) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_period_buffer_enable) refers to at32a403a_tmr.o(.text.tmr_period_buffer_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_buffer_enable) refers to at32a403a_tmr.o(.text.tmr_output_channel_buffer_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_immediately_set) refers to at32a403a_tmr.o(.text.tmr_output_channel_immediately_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_switch_set) refers to at32a403a_tmr.o(.text.tmr_output_channel_switch_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_one_cycle_mode_enable) refers to at32a403a_tmr.o(.text.tmr_one_cycle_mode_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_32_bit_function_enable) refers to at32a403a_tmr.o(.text.tmr_32_bit_function_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_overflow_request_source_set) refers to at32a403a_tmr.o(.text.tmr_overflow_request_source_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_overflow_event_disable) refers to at32a403a_tmr.o(.text.tmr_overflow_event_disable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_input_channel_init) refers to at32a403a_tmr.o(.text.tmr_input_channel_init) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_enable) refers to at32a403a_tmr.o(.text.tmr_channel_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_input_channel_filter_set) refers to at32a403a_tmr.o(.text.tmr_input_channel_filter_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_pwm_input_config) refers to at32a403a_tmr.o(.text.tmr_pwm_input_config) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_channel1_input_select) refers to at32a403a_tmr.o(.text.tmr_channel1_input_select) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_input_channel_divider_set) refers to at32a403a_tmr.o(.text.tmr_input_channel_divider_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_primary_mode_select) refers to at32a403a_tmr.o(.text.tmr_primary_mode_select) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_sub_mode_select) refers to at32a403a_tmr.o(.text.tmr_sub_mode_select) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_dma_select) refers to at32a403a_tmr.o(.text.tmr_channel_dma_select) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_hall_select) refers to at32a403a_tmr.o(.text.tmr_hall_select) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_buffer_enable) refers to at32a403a_tmr.o(.text.tmr_channel_buffer_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_trigger_input_select) refers to at32a403a_tmr.o(.text.tmr_trigger_input_select) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_sub_sync_mode_set) refers to at32a403a_tmr.o(.text.tmr_sub_sync_mode_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_dma_request_enable) refers to at32a403a_tmr.o(.text.tmr_dma_request_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_interrupt_enable) refers to at32a403a_tmr.o(.text.tmr_interrupt_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_interrupt_flag_get) refers to at32a403a_tmr.o(.text.tmr_interrupt_flag_get) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_flag_get) refers to at32a403a_tmr.o(.text.tmr_flag_get) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_flag_clear) refers to at32a403a_tmr.o(.text.tmr_flag_clear) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_event_sw_trigger) refers to at32a403a_tmr.o(.text.tmr_event_sw_trigger) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_output_enable) refers to at32a403a_tmr.o(.text.tmr_output_enable) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_internal_clock_set) refers to at32a403a_tmr.o(.text.tmr_internal_clock_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_polarity_set) refers to at32a403a_tmr.o(.text.tmr_output_channel_polarity_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_external_clock_config) refers to at32a403a_tmr.o(.text.tmr_external_clock_config) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_external_clock_mode1_config) refers to at32a403a_tmr.o(.text.tmr_external_clock_mode1_config) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_external_clock_mode2_config) refers to at32a403a_tmr.o(.text.tmr_external_clock_mode2_config) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_encoder_mode_config) refers to at32a403a_tmr.o(.text.tmr_encoder_mode_config) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_force_output_set) refers to at32a403a_tmr.o(.text.tmr_force_output_set) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_dma_control_config) refers to at32a403a_tmr.o(.text.tmr_dma_control_config) for [Anonymous Symbol]
    at32a403a_tmr.o(.ARM.exidx.text.tmr_brkdt_config) refers to at32a403a_tmr.o(.text.tmr_brkdt_config) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_dev_init) refers to at32a403a_usb.o(.text.usb_dev_init) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_interrupt_enable) refers to at32a403a_usb.o(.text.usb_interrupt_enable) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_connect) refers to at32a403a_usb.o(.text.usb_connect) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_disconnect) refers to at32a403a_usb.o(.text.usb_disconnect) for [Anonymous Symbol]
    at32a403a_usb.o(.text.usb_usbbufs_enable) refers to at32a403a_usb.o(.data.g_usb_packet_address) for g_usb_packet_address
    at32a403a_usb.o(.ARM.exidx.text.usb_usbbufs_enable) refers to at32a403a_usb.o(.text.usb_usbbufs_enable) for [Anonymous Symbol]
    at32a403a_usb.o(.text.usb_ept_open) refers to at32a403a_usb.o(.data.g_usb_packet_address) for g_usb_packet_address
    at32a403a_usb.o(.ARM.exidx.text.usb_ept_open) refers to at32a403a_usb.o(.text.usb_ept_open) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_ept_close) refers to at32a403a_usb.o(.text.usb_ept_close) for [Anonymous Symbol]
    at32a403a_usb.o(.text.usb_write_packet) refers to at32a403a_usb.o(.data.g_usb_packet_address) for g_usb_packet_address
    at32a403a_usb.o(.ARM.exidx.text.usb_write_packet) refers to at32a403a_usb.o(.text.usb_write_packet) for [Anonymous Symbol]
    at32a403a_usb.o(.text.usb_read_packet) refers to at32a403a_usb.o(.data.g_usb_packet_address) for g_usb_packet_address
    at32a403a_usb.o(.ARM.exidx.text.usb_read_packet) refers to at32a403a_usb.o(.text.usb_read_packet) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_set_address) refers to at32a403a_usb.o(.text.usb_set_address) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_ept_stall) refers to at32a403a_usb.o(.text.usb_ept_stall) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_enter_suspend) refers to at32a403a_usb.o(.text.usb_enter_suspend) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_exit_suspend) refers to at32a403a_usb.o(.text.usb_exit_suspend) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_remote_wkup_set) refers to at32a403a_usb.o(.text.usb_remote_wkup_set) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_remote_wkup_clear) refers to at32a403a_usb.o(.text.usb_remote_wkup_clear) for [Anonymous Symbol]
    at32a403a_usb.o(.text.usb_buffer_malloc) refers to at32a403a_usb.o(.data.g_usb_offset_addr) for g_usb_offset_addr
    at32a403a_usb.o(.ARM.exidx.text.usb_buffer_malloc) refers to at32a403a_usb.o(.text.usb_buffer_malloc) for [Anonymous Symbol]
    at32a403a_usb.o(.text.usb_buffer_free) refers to at32a403a_usb.o(.data.g_usb_offset_addr) for g_usb_offset_addr
    at32a403a_usb.o(.ARM.exidx.text.usb_buffer_free) refers to at32a403a_usb.o(.text.usb_buffer_free) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_flag_get) refers to at32a403a_usb.o(.text.usb_flag_get) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_interrupt_flag_get) refers to at32a403a_usb.o(.text.usb_interrupt_flag_get) for [Anonymous Symbol]
    at32a403a_usb.o(.ARM.exidx.text.usb_flag_clear) refers to at32a403a_usb.o(.text.usb_flag_clear) for [Anonymous Symbol]
    at32a403a_wdt.o(.ARM.exidx.text.wdt_enable) refers to at32a403a_wdt.o(.text.wdt_enable) for [Anonymous Symbol]
    at32a403a_wdt.o(.ARM.exidx.text.wdt_counter_reload) refers to at32a403a_wdt.o(.text.wdt_counter_reload) for [Anonymous Symbol]
    at32a403a_wdt.o(.ARM.exidx.text.wdt_reload_value_set) refers to at32a403a_wdt.o(.text.wdt_reload_value_set) for [Anonymous Symbol]
    at32a403a_wdt.o(.ARM.exidx.text.wdt_divider_set) refers to at32a403a_wdt.o(.text.wdt_divider_set) for [Anonymous Symbol]
    at32a403a_wdt.o(.ARM.exidx.text.wdt_register_write_enable) refers to at32a403a_wdt.o(.text.wdt_register_write_enable) for [Anonymous Symbol]
    at32a403a_wdt.o(.ARM.exidx.text.wdt_flag_get) refers to at32a403a_wdt.o(.text.wdt_flag_get) for [Anonymous Symbol]
    startup_at32a403a.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_at32a403a.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_at32a403a.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_at32a403a.o(RESET) refers to startup_at32a403a.o(STACK) for __initial_sp
    startup_at32a403a.o(RESET) refers to startup_at32a403a.o(.text) for Reset_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.NMI_Handler) for NMI_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.HardFault_Handler) for HardFault_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.MemManage_Handler) for MemManage_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.BusFault_Handler) for BusFault_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.SVC_Handler) for SVC_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.PendSV_Handler) for PendSV_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.SysTick_Handler) for SysTick_Handler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.ADC1_2_IRQHandler) for ADC1_2_IRQHandler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.TMR1_BRK_TMR9_IRQHandler) for TMR1_BRK_TMR9_IRQHandler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.TMR1_OVF_TMR10_IRQHandler) for TMR1_OVF_TMR10_IRQHandler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.TMR6_GLOBAL_IRQHandler) for TMR6_GLOBAL_IRQHandler
    startup_at32a403a.o(RESET) refers to at32a403a_int.o(.text.USBFS_MAPL_IRQHandler) for USBFS_MAPL_IRQHandler
    startup_at32a403a.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_at32a403a.o(.text) refers to system_at32a403a.o(.text.SystemInit) for SystemInit
    startup_at32a403a.o(.text) refers to __main.o(!!!main) for __main
    startup_at32a403a.o(.text) refers to startup_at32a403a.o(HEAP) for Heap_Mem
    startup_at32a403a.o(.text) refers to startup_at32a403a.o(STACK) for Stack_Mem
    system_at32a403a.o(.ARM.exidx.text.SystemInit) refers to system_at32a403a.o(.text.SystemInit) for [Anonymous Symbol]
    system_at32a403a.o(.text.system_core_clock_update) refers to at32a403a_crm.o(.text.crm_sysclk_switch_status_get) for crm_sysclk_switch_status_get
    system_at32a403a.o(.text.system_core_clock_update) refers to system_at32a403a.o(.data.system_core_clock) for system_core_clock
    system_at32a403a.o(.ARM.exidx.text.system_core_clock_update) refers to system_at32a403a.o(.text.system_core_clock_update) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_core_in_handler) refers to usbd_core.o(.text.usbd_ept_send) for usbd_ept_send
    usbd_core.o(.ARM.exidx.text.usbd_core_in_handler) refers to usbd_core.o(.text.usbd_core_in_handler) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_ept_send) refers to at32a403a_usb.o(.data.g_usb_packet_address) for g_usb_packet_address
    usbd_core.o(.text.usbd_ept_send) refers to at32a403a_usb.o(.text.usb_write_packet) for usb_write_packet
    usbd_core.o(.ARM.exidx.text.usbd_ept_send) refers to usbd_core.o(.text.usbd_ept_send) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_ept_recv) refers to usbd_core.o(.text.usbd_ept_recv) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_ctrl_recv_status) refers to usbd_core.o(.text.usbd_ctrl_recv_status) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_core_out_handler) refers to usbd_core.o(.text.usbd_ept_send) for usbd_ept_send
    usbd_core.o(.ARM.exidx.text.usbd_core_out_handler) refers to usbd_core.o(.text.usbd_core_out_handler) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_ctrl_send_status) refers to usbd_core.o(.text.usbd_ept_send) for usbd_ept_send
    usbd_core.o(.ARM.exidx.text.usbd_ctrl_send_status) refers to usbd_core.o(.text.usbd_ctrl_send_status) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_core_setup_handler) refers to usbd_sdr.o(.text.usbd_setup_request_parse) for usbd_setup_request_parse
    usbd_core.o(.text.usbd_core_setup_handler) refers to usbd_sdr.o(.text.usbd_device_request) for usbd_device_request
    usbd_core.o(.text.usbd_core_setup_handler) refers to usbd_sdr.o(.text.usbd_interface_request) for usbd_interface_request
    usbd_core.o(.text.usbd_core_setup_handler) refers to usbd_sdr.o(.text.usbd_endpoint_request) for usbd_endpoint_request
    usbd_core.o(.ARM.exidx.text.usbd_core_setup_handler) refers to usbd_core.o(.text.usbd_core_setup_handler) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_ctrl_send) refers to usbd_core.o(.text.usbd_ept_send) for usbd_ept_send
    usbd_core.o(.ARM.exidx.text.usbd_ctrl_send) refers to usbd_core.o(.text.usbd_ctrl_send) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_ctrl_recv) refers to usbd_core.o(.text.usbd_ctrl_recv) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_clear_stall) refers to usbd_core.o(.text.usbd_clear_stall) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_set_stall) refers to usbd_core.o(.text.usbd_set_stall) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_ctrl_unsupport) refers to usbd_core.o(.text.usbd_ctrl_unsupport) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_get_recv_len) refers to usbd_core.o(.text.usbd_get_recv_len) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_ept_dbuffer_enable) refers to usbd_core.o(.text.usbd_ept_dbuffer_enable) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_ept_buf_auto_define) refers to at32a403a_usb.o(.text.usb_buffer_malloc) for usb_buffer_malloc
    usbd_core.o(.ARM.exidx.text.usbd_ept_buf_auto_define) refers to usbd_core.o(.text.usbd_ept_buf_auto_define) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_ept_buf_custom_define) refers to usbd_core.o(.text.usbd_ept_buf_custom_define) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_ept_open) refers to at32a403a_usb.o(.text.usb_ept_open) for usb_ept_open
    usbd_core.o(.ARM.exidx.text.usbd_ept_open) refers to usbd_core.o(.text.usbd_ept_open) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_ept_close) refers to at32a403a_usb.o(.text.usb_ept_close) for usb_ept_close
    usbd_core.o(.ARM.exidx.text.usbd_ept_close) refers to usbd_core.o(.text.usbd_ept_close) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_connect) refers to at32a403a_usb.o(.text.usb_connect) for usb_connect
    usbd_core.o(.ARM.exidx.text.usbd_connect) refers to usbd_core.o(.text.usbd_connect) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_disconnect) refers to at32a403a_usb.o(.text.usb_disconnect) for usb_disconnect
    usbd_core.o(.ARM.exidx.text.usbd_disconnect) refers to usbd_core.o(.text.usbd_disconnect) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_set_device_addr) refers to at32a403a_usb.o(.text.usb_set_address) for usb_set_address
    usbd_core.o(.ARM.exidx.text.usbd_set_device_addr) refers to usbd_core.o(.text.usbd_set_device_addr) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_connect_state_get) refers to usbd_core.o(.text.usbd_connect_state_get) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_remote_wakeup) refers to at32a403a_usb.o(.text.usb_exit_suspend) for usb_exit_suspend
    usbd_core.o(.text.usbd_remote_wakeup) refers to at32a403a_usb.o(.text.usb_remote_wkup_set) for usb_remote_wkup_set
    usbd_core.o(.text.usbd_remote_wakeup) refers to usb_app.o(.text.usb_delay_ms) for usb_delay_ms
    usbd_core.o(.text.usbd_remote_wakeup) refers to at32a403a_usb.o(.text.usb_remote_wkup_clear) for usb_remote_wkup_clear
    usbd_core.o(.ARM.exidx.text.usbd_remote_wakeup) refers to usbd_core.o(.text.usbd_remote_wakeup) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_enter_suspend) refers to at32a403a_usb.o(.text.usb_enter_suspend) for usb_enter_suspend
    usbd_core.o(.ARM.exidx.text.usbd_enter_suspend) refers to usbd_core.o(.text.usbd_enter_suspend) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.usbd_ept_defaut_init) refers to usbd_core.o(.text.usbd_ept_defaut_init) for [Anonymous Symbol]
    usbd_core.o(.text.usbd_core_init) refers to at32a403a_usb.o(.text.usb_usbbufs_enable) for usb_usbbufs_enable
    usbd_core.o(.text.usbd_core_init) refers to at32a403a_usb.o(.text.usb_dev_init) for usb_dev_init
    usbd_core.o(.ARM.exidx.text.usbd_core_init) refers to usbd_core.o(.text.usbd_core_init) for [Anonymous Symbol]
    usbd_int.o(.text.usbd_irq_handler) refers to at32a403a_usb.o(.text.usb_flag_clear) for usb_flag_clear
    usbd_int.o(.text.usbd_irq_handler) refers to usbd_int.o(.text.usbd_reset_handler) for usbd_reset_handler
    usbd_int.o(.text.usbd_irq_handler) refers to usbd_core.o(.text.usbd_enter_suspend) for usbd_enter_suspend
    usbd_int.o(.text.usbd_irq_handler) refers to at32a403a_usb.o(.text.usb_exit_suspend) for usb_exit_suspend
    usbd_int.o(.text.usbd_irq_handler) refers to usbd_int.o(.text.usbd_eptn_handler) for usbd_eptn_handler
    usbd_int.o(.ARM.exidx.text.usbd_irq_handler) refers to usbd_int.o(.text.usbd_irq_handler) for [Anonymous Symbol]
    usbd_int.o(.text.usbd_ept_loop_handler) refers to usbd_int.o(.text.usbd_eptn_handler) for usbd_eptn_handler
    usbd_int.o(.ARM.exidx.text.usbd_ept_loop_handler) refers to usbd_int.o(.text.usbd_ept_loop_handler) for [Anonymous Symbol]
    usbd_int.o(.text.usbd_reset_handler) refers to at32a403a_usb.o(.text.usb_buffer_free) for usb_buffer_free
    usbd_int.o(.text.usbd_reset_handler) refers to usbd_core.o(.text.usbd_ept_defaut_init) for usbd_ept_defaut_init
    usbd_int.o(.text.usbd_reset_handler) refers to usbd_core.o(.text.usbd_ept_buf_custom_define) for usbd_ept_buf_custom_define
    usbd_int.o(.text.usbd_reset_handler) refers to usbd_core.o(.text.usbd_ept_open) for usbd_ept_open
    usbd_int.o(.text.usbd_reset_handler) refers to at32a403a_usb.o(.text.usb_set_address) for usb_set_address
    usbd_int.o(.ARM.exidx.text.usbd_reset_handler) refers to usbd_int.o(.text.usbd_reset_handler) for [Anonymous Symbol]
    usbd_int.o(.ARM.exidx.text.usbd_sof_handler) refers to usbd_int.o(.text.usbd_sof_handler) for [Anonymous Symbol]
    usbd_int.o(.text.usbd_suspend_handler) refers to usbd_core.o(.text.usbd_enter_suspend) for usbd_enter_suspend
    usbd_int.o(.ARM.exidx.text.usbd_suspend_handler) refers to usbd_int.o(.text.usbd_suspend_handler) for [Anonymous Symbol]
    usbd_int.o(.text.usbd_wakeup_handler) refers to at32a403a_usb.o(.text.usb_exit_suspend) for usb_exit_suspend
    usbd_int.o(.ARM.exidx.text.usbd_wakeup_handler) refers to usbd_int.o(.text.usbd_wakeup_handler) for [Anonymous Symbol]
    usbd_int.o(.text.usbd_eptn_handler) refers to at32a403a_usb.o(.data.g_usb_packet_address) for g_usb_packet_address
    usbd_int.o(.text.usbd_eptn_handler) refers to usbd_core.o(.text.usbd_ept_send) for usbd_ept_send
    usbd_int.o(.text.usbd_eptn_handler) refers to usbd_core.o(.text.usbd_core_in_handler) for usbd_core_in_handler
    usbd_int.o(.text.usbd_eptn_handler) refers to at32a403a_usb.o(.text.usb_set_address) for usb_set_address
    usbd_int.o(.text.usbd_eptn_handler) refers to at32a403a_usb.o(.text.usb_read_packet) for usb_read_packet
    usbd_int.o(.text.usbd_eptn_handler) refers to usbd_core.o(.text.usbd_core_setup_handler) for usbd_core_setup_handler
    usbd_int.o(.text.usbd_eptn_handler) refers to usbd_core.o(.text.usbd_core_out_handler) for usbd_core_out_handler
    usbd_int.o(.text.usbd_eptn_handler) refers to usbd_core.o(.text.usbd_ept_recv) for usbd_ept_recv
    usbd_int.o(.ARM.exidx.text.usbd_eptn_handler) refers to usbd_int.o(.text.usbd_eptn_handler) for [Anonymous Symbol]
    usbd_sdr.o(.ARM.exidx.text.usbd_setup_request_parse) refers to usbd_sdr.o(.text.usbd_setup_request_parse) for [Anonymous Symbol]
    usbd_sdr.o(.text.usbd_device_request) refers to usbd_sdr.o(.bss.usbd_set_configuration.config_value) for usbd_set_configuration.config_value
    usbd_sdr.o(.text.usbd_device_request) refers to usbd_core.o(.text.usbd_ctrl_send_status) for usbd_ctrl_send_status
    usbd_sdr.o(.text.usbd_device_request) refers to usbd_core.o(.text.usbd_ctrl_unsupport) for usbd_ctrl_unsupport
    usbd_sdr.o(.text.usbd_device_request) refers to usbd_core.o(.text.usbd_ctrl_send) for usbd_ctrl_send
    usbd_sdr.o(.ARM.exidx.text.usbd_device_request) refers to usbd_sdr.o(.text.usbd_device_request) for [Anonymous Symbol]
    usbd_sdr.o(.text.usbd_interface_request) refers to usbd_core.o(.text.usbd_ctrl_unsupport) for usbd_ctrl_unsupport
    usbd_sdr.o(.text.usbd_interface_request) refers to usbd_core.o(.text.usbd_ctrl_send_status) for usbd_ctrl_send_status
    usbd_sdr.o(.ARM.exidx.text.usbd_interface_request) refers to usbd_sdr.o(.text.usbd_interface_request) for [Anonymous Symbol]
    usbd_sdr.o(.text.usbd_endpoint_request) refers to usbd_core.o(.text.usbd_set_stall) for usbd_set_stall
    usbd_sdr.o(.text.usbd_endpoint_request) refers to usbd_core.o(.text.usbd_clear_stall) for usbd_clear_stall
    usbd_sdr.o(.text.usbd_endpoint_request) refers to usbd_core.o(.text.usbd_ctrl_unsupport) for usbd_ctrl_unsupport
    usbd_sdr.o(.text.usbd_endpoint_request) refers to usbd_core.o(.text.usbd_ctrl_send_status) for usbd_ctrl_send_status
    usbd_sdr.o(.text.usbd_endpoint_request) refers to usbd_core.o(.text.usbd_ctrl_send) for usbd_ctrl_send
    usbd_sdr.o(.ARM.exidx.text.usbd_endpoint_request) refers to usbd_sdr.o(.text.usbd_endpoint_request) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.NMI_Handler) refers to at32a403a_int.o(.text.NMI_Handler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.HardFault_Handler) refers to at32a403a_int.o(.text.HardFault_Handler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.MemManage_Handler) refers to at32a403a_int.o(.text.MemManage_Handler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.BusFault_Handler) refers to at32a403a_int.o(.text.BusFault_Handler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.UsageFault_Handler) refers to at32a403a_int.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.SVC_Handler) refers to at32a403a_int.o(.text.SVC_Handler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.DebugMon_Handler) refers to at32a403a_int.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.PendSV_Handler) refers to at32a403a_int.o(.text.PendSV_Handler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.SysTick_Handler) refers to at32a403a_int.o(.text.SysTick_Handler) for [Anonymous Symbol]
    at32a403a_int.o(.text.DMA1_Channel1_IRQHandler) refers to at32a403a_dma.o(.text.dma_interrupt_flag_get) for dma_interrupt_flag_get
    at32a403a_int.o(.text.DMA1_Channel1_IRQHandler) refers to at32a403a_dma.o(.text.dma_flag_clear) for dma_flag_clear
    at32a403a_int.o(.text.DMA1_Channel1_IRQHandler) refers to sensor_proc.o(.text.sensor_set_dma_ready) for sensor_set_dma_ready
    at32a403a_int.o(.ARM.exidx.text.DMA1_Channel1_IRQHandler) refers to at32a403a_int.o(.text.DMA1_Channel1_IRQHandler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.ADC1_2_IRQHandler) refers to at32a403a_int.o(.text.ADC1_2_IRQHandler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.TMR1_BRK_TMR9_IRQHandler) refers to at32a403a_int.o(.text.TMR1_BRK_TMR9_IRQHandler) for [Anonymous Symbol]
    at32a403a_int.o(.ARM.exidx.text.TMR1_OVF_TMR10_IRQHandler) refers to at32a403a_int.o(.text.TMR1_OVF_TMR10_IRQHandler) for [Anonymous Symbol]
    at32a403a_int.o(.text.TMR6_GLOBAL_IRQHandler) refers to at32a403a_tmr.o(.text.tmr_interrupt_flag_get) for tmr_interrupt_flag_get
    at32a403a_int.o(.text.TMR6_GLOBAL_IRQHandler) refers to at32a403a_tmr.o(.text.tmr_flag_clear) for tmr_flag_clear
    at32a403a_int.o(.text.TMR6_GLOBAL_IRQHandler) refers to sys_timerevent.o(.text.TimerEvent_Handler) for TimerEvent_Handler
    at32a403a_int.o(.text.TMR6_GLOBAL_IRQHandler) refers to at32a403a_int.o(.bss.TIME_ADC1_cnt500us) for TIME_ADC1_cnt500us
    at32a403a_int.o(.text.TMR6_GLOBAL_IRQHandler) refers to at32a403a_adc.o(.text.adc_ordinary_software_trigger_enable) for adc_ordinary_software_trigger_enable
    at32a403a_int.o(.ARM.exidx.text.TMR6_GLOBAL_IRQHandler) refers to at32a403a_int.o(.text.TMR6_GLOBAL_IRQHandler) for [Anonymous Symbol]
    at32a403a_int.o(.text.USBFS_MAPL_IRQHandler) refers to usb_app.o(.text.wk_usbfs_irq_handler) for wk_usbfs_irq_handler
    at32a403a_int.o(.ARM.exidx.text.USBFS_MAPL_IRQHandler) refers to at32a403a_int.o(.text.USBFS_MAPL_IRQHandler) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_reset) for crm_reset
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_clock_source_enable) for crm_clock_source_enable
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_flag_get) for crm_flag_get
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_hext_stable_wait) for crm_hext_stable_wait
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_pll_config) for crm_pll_config
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_ahb_div_set) for crm_ahb_div_set
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_apb2_div_set) for crm_apb2_div_set
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_apb1_div_set) for crm_apb1_div_set
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_auto_step_mode_enable) for crm_auto_step_mode_enable
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_sysclk_switch) for crm_sysclk_switch
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to at32a403a_crm.o(.text.crm_sysclk_switch_status_get) for crm_sysclk_switch_status_get
    at32a403a_wk_config.o(.text.wk_system_clock_config) refers to system_at32a403a.o(.text.system_core_clock_update) for system_core_clock_update
    at32a403a_wk_config.o(.ARM.exidx.text.wk_system_clock_config) refers to at32a403a_wk_config.o(.text.wk_system_clock_config) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_periph_clock_config) refers to at32a403a_crm.o(.text.crm_periph_clock_enable) for crm_periph_clock_enable
    at32a403a_wk_config.o(.ARM.exidx.text.wk_periph_clock_config) refers to at32a403a_wk_config.o(.text.wk_periph_clock_config) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_debug_config) refers to at32a403a_gpio.o(.text.gpio_pin_remap_config) for gpio_pin_remap_config
    at32a403a_wk_config.o(.ARM.exidx.text.wk_debug_config) refers to at32a403a_wk_config.o(.text.wk_debug_config) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_nvic_config) refers to at32a403a_misc.o(.text.nvic_priority_group_config) for nvic_priority_group_config
    at32a403a_wk_config.o(.text.wk_nvic_config) refers to at32a403a_misc.o(.text.nvic_irq_enable) for nvic_irq_enable
    at32a403a_wk_config.o(.ARM.exidx.text.wk_nvic_config) refers to at32a403a_wk_config.o(.text.wk_nvic_config) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_gpio_config) refers to at32a403a_gpio.o(.text.gpio_default_para_init) for gpio_default_para_init
    at32a403a_wk_config.o(.text.wk_gpio_config) refers to at32a403a_gpio.o(.text.gpio_init) for gpio_init
    at32a403a_wk_config.o(.text.wk_gpio_config) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    at32a403a_wk_config.o(.text.wk_gpio_config) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    at32a403a_wk_config.o(.ARM.exidx.text.wk_gpio_config) refers to at32a403a_wk_config.o(.text.wk_gpio_config) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_spi3_init) refers to at32a403a_gpio.o(.text.gpio_default_para_init) for gpio_default_para_init
    at32a403a_wk_config.o(.text.wk_spi3_init) refers to at32a403a_spi.o(.text.spi_default_para_init) for spi_default_para_init
    at32a403a_wk_config.o(.text.wk_spi3_init) refers to at32a403a_gpio.o(.text.gpio_init) for gpio_init
    at32a403a_wk_config.o(.text.wk_spi3_init) refers to at32a403a_spi.o(.text.spi_init) for spi_init
    at32a403a_wk_config.o(.text.wk_spi3_init) refers to at32a403a_spi.o(.text.spi_enable) for spi_enable
    at32a403a_wk_config.o(.ARM.exidx.text.wk_spi3_init) refers to at32a403a_wk_config.o(.text.wk_spi3_init) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_gpio.o(.text.gpio_default_para_init) for gpio_default_para_init
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_gpio.o(.text.gpio_init) for gpio_init
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_cnt_dir_set) for tmr_cnt_dir_set
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_clock_source_div_set) for tmr_clock_source_div_set
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_repetition_counter_set) for tmr_repetition_counter_set
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_period_buffer_enable) for tmr_period_buffer_enable
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_base_init) for tmr_base_init
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_sub_sync_mode_set) for tmr_sub_sync_mode_set
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_primary_mode_select) for tmr_primary_mode_select
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_output_channel_config) for tmr_output_channel_config
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_channel_value_set) for tmr_channel_value_set
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_output_channel_buffer_enable) for tmr_output_channel_buffer_enable
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_output_channel_immediately_set) for tmr_output_channel_immediately_set
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_brkdt_config) for tmr_brkdt_config
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_output_enable) for tmr_output_enable
    at32a403a_wk_config.o(.text.wk_tmr1_init) refers to at32a403a_tmr.o(.text.tmr_counter_enable) for tmr_counter_enable
    at32a403a_wk_config.o(.ARM.exidx.text.wk_tmr1_init) refers to at32a403a_wk_config.o(.text.wk_tmr1_init) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_gpio.o(.text.gpio_default_para_init) for gpio_default_para_init
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_gpio.o(.text.gpio_init) for gpio_init
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_gpio.o(.text.gpio_pin_remap_config) for gpio_pin_remap_config
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_tmr.o(.text.tmr_cnt_dir_set) for tmr_cnt_dir_set
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_tmr.o(.text.tmr_clock_source_div_set) for tmr_clock_source_div_set
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_tmr.o(.text.tmr_period_buffer_enable) for tmr_period_buffer_enable
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_tmr.o(.text.tmr_base_init) for tmr_base_init
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_tmr.o(.text.tmr_sub_sync_mode_set) for tmr_sub_sync_mode_set
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_tmr.o(.text.tmr_primary_mode_select) for tmr_primary_mode_select
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_tmr.o(.text.tmr_input_channel_init) for tmr_input_channel_init
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_tmr.o(.text.tmr_encoder_mode_config) for tmr_encoder_mode_config
    at32a403a_wk_config.o(.text.wk_tmr3_init) refers to at32a403a_tmr.o(.text.tmr_counter_enable) for tmr_counter_enable
    at32a403a_wk_config.o(.ARM.exidx.text.wk_tmr3_init) refers to at32a403a_wk_config.o(.text.wk_tmr3_init) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_tmr6_init) refers to at32a403a_tmr.o(.text.tmr_cnt_dir_set) for tmr_cnt_dir_set
    at32a403a_wk_config.o(.text.wk_tmr6_init) refers to at32a403a_tmr.o(.text.tmr_period_buffer_enable) for tmr_period_buffer_enable
    at32a403a_wk_config.o(.text.wk_tmr6_init) refers to at32a403a_tmr.o(.text.tmr_base_init) for tmr_base_init
    at32a403a_wk_config.o(.text.wk_tmr6_init) refers to at32a403a_tmr.o(.text.tmr_primary_mode_select) for tmr_primary_mode_select
    at32a403a_wk_config.o(.text.wk_tmr6_init) refers to at32a403a_tmr.o(.text.tmr_counter_enable) for tmr_counter_enable
    at32a403a_wk_config.o(.ARM.exidx.text.wk_tmr6_init) refers to at32a403a_wk_config.o(.text.wk_tmr6_init) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_usbfs_init) refers to at32a403a_crm.o(.text.crm_usb_interrupt_remapping_set) for crm_usb_interrupt_remapping_set
    at32a403a_wk_config.o(.text.wk_usbfs_init) refers to at32a403a_crm.o(.text.crm_usb_clock_source_select) for crm_usb_clock_source_select
    at32a403a_wk_config.o(.text.wk_usbfs_init) refers to at32a403a_crm.o(.text.crm_usb_clock_div_set) for crm_usb_clock_div_set
    at32a403a_wk_config.o(.ARM.exidx.text.wk_usbfs_init) refers to at32a403a_wk_config.o(.text.wk_usbfs_init) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_wdt_init) refers to at32a403a_wdt.o(.text.wdt_register_write_enable) for wdt_register_write_enable
    at32a403a_wk_config.o(.text.wk_wdt_init) refers to at32a403a_wdt.o(.text.wdt_divider_set) for wdt_divider_set
    at32a403a_wk_config.o(.text.wk_wdt_init) refers to at32a403a_wdt.o(.text.wdt_reload_value_set) for wdt_reload_value_set
    at32a403a_wk_config.o(.text.wk_wdt_init) refers to at32a403a_wdt.o(.text.wdt_counter_reload) for wdt_counter_reload
    at32a403a_wk_config.o(.ARM.exidx.text.wk_wdt_init) refers to at32a403a_wk_config.o(.text.wk_wdt_init) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_gpio.o(.text.gpio_default_para_init) for gpio_default_para_init
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_gpio.o(.text.gpio_init) for gpio_init
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_reset) for adc_reset
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_crm.o(.text.crm_adc_clock_div_set) for crm_adc_clock_div_set
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_combine_mode_select) for adc_combine_mode_select
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_base_default_para_init) for adc_base_default_para_init
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_base_config) for adc_base_config
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_ordinary_channel_set) for adc_ordinary_channel_set
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_ordinary_conversion_trigger_set) for adc_ordinary_conversion_trigger_set
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_ordinary_part_mode_enable) for adc_ordinary_part_mode_enable
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_dma_mode_enable) for adc_dma_mode_enable
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_enable) for adc_enable
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_calibration_init) for adc_calibration_init
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_calibration_init_status_get) for adc_calibration_init_status_get
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_calibration_start) for adc_calibration_start
    at32a403a_wk_config.o(.text.wk_adc1_init) refers to at32a403a_adc.o(.text.adc_calibration_status_get) for adc_calibration_status_get
    at32a403a_wk_config.o(.ARM.exidx.text.wk_adc1_init) refers to at32a403a_wk_config.o(.text.wk_adc1_init) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_gpio.o(.text.gpio_default_para_init) for gpio_default_para_init
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_gpio.o(.text.gpio_init) for gpio_init
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_reset) for adc_reset
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_crm.o(.text.crm_adc_clock_div_set) for crm_adc_clock_div_set
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_base_default_para_init) for adc_base_default_para_init
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_base_config) for adc_base_config
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_preempt_channel_length_set) for adc_preempt_channel_length_set
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_preempt_channel_set) for adc_preempt_channel_set
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_preempt_offset_value_set) for adc_preempt_offset_value_set
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_preempt_conversion_trigger_set) for adc_preempt_conversion_trigger_set
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_voltage_monitor_enable) for adc_voltage_monitor_enable
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_voltage_monitor_threshold_value_set) for adc_voltage_monitor_threshold_value_set
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_enable) for adc_enable
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_calibration_init) for adc_calibration_init
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_calibration_init_status_get) for adc_calibration_init_status_get
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_calibration_start) for adc_calibration_start
    at32a403a_wk_config.o(.text.wk_adc2_init) refers to at32a403a_adc.o(.text.adc_calibration_status_get) for adc_calibration_status_get
    at32a403a_wk_config.o(.ARM.exidx.text.wk_adc2_init) refers to at32a403a_wk_config.o(.text.wk_adc2_init) for [Anonymous Symbol]
    at32a403a_wk_config.o(.text.wk_dma1_channel1_init) refers to at32a403a_dma.o(.text.dma_reset) for dma_reset
    at32a403a_wk_config.o(.text.wk_dma1_channel1_init) refers to at32a403a_dma.o(.text.dma_default_para_init) for dma_default_para_init
    at32a403a_wk_config.o(.text.wk_dma1_channel1_init) refers to at32a403a_dma.o(.text.dma_init) for dma_init
    at32a403a_wk_config.o(.text.wk_dma1_channel1_init) refers to at32a403a_dma.o(.text.dma_flexible_config) for dma_flexible_config
    at32a403a_wk_config.o(.ARM.exidx.text.wk_dma1_channel1_init) refers to at32a403a_wk_config.o(.text.wk_dma1_channel1_init) for [Anonymous Symbol]
    at32a403a_wk_config.o(.ARM.exidx.text.wk_dma_channel_config) refers to at32a403a_wk_config.o(.text.wk_dma_channel_config) for [Anonymous Symbol]
    cdc_class.o(.text.class_init_handler) refers to usbd_core.o(.text.usbd_ept_buf_custom_define) for usbd_ept_buf_custom_define
    cdc_class.o(.text.class_init_handler) refers to usbd_core.o(.text.usbd_ept_open) for usbd_ept_open
    cdc_class.o(.text.class_init_handler) refers to usbd_core.o(.text.usbd_ept_recv) for usbd_ept_recv
    cdc_class.o(.text.class_init_handler) refers to cdc_class.o(.data.linecoding) for linecoding
    cdc_class.o(.ARM.exidx.text.class_init_handler) refers to cdc_class.o(.text.class_init_handler) for [Anonymous Symbol]
    cdc_class.o(.text.class_clear_handler) refers to usbd_core.o(.text.usbd_ept_close) for usbd_ept_close
    cdc_class.o(.ARM.exidx.text.class_clear_handler) refers to cdc_class.o(.text.class_clear_handler) for [Anonymous Symbol]
    cdc_class.o(.text.class_setup_handler) refers to usbd_core.o(.text.usbd_ctrl_recv) for usbd_ctrl_recv
    cdc_class.o(.text.class_setup_handler) refers to usbd_core.o(.text.usbd_ctrl_unsupport) for usbd_ctrl_unsupport
    cdc_class.o(.text.class_setup_handler) refers to usbd_core.o(.text.usbd_ctrl_send) for usbd_ctrl_send
    cdc_class.o(.ARM.exidx.text.class_setup_handler) refers to cdc_class.o(.text.class_setup_handler) for [Anonymous Symbol]
    cdc_class.o(.ARM.exidx.text.class_ept0_tx_handler) refers to cdc_class.o(.text.class_ept0_tx_handler) for [Anonymous Symbol]
    cdc_class.o(.text.class_ept0_rx_handler) refers to usbd_core.o(.text.usbd_get_recv_len) for usbd_get_recv_len
    cdc_class.o(.ARM.exidx.text.class_ept0_rx_handler) refers to cdc_class.o(.text.class_ept0_rx_handler) for [Anonymous Symbol]
    cdc_class.o(.ARM.exidx.text.class_in_handler) refers to cdc_class.o(.text.class_in_handler) for [Anonymous Symbol]
    cdc_class.o(.text.class_out_handler) refers to usbd_core.o(.text.usbd_get_recv_len) for usbd_get_recv_len
    cdc_class.o(.ARM.exidx.text.class_out_handler) refers to cdc_class.o(.text.class_out_handler) for [Anonymous Symbol]
    cdc_class.o(.ARM.exidx.text.class_sof_handler) refers to cdc_class.o(.text.class_sof_handler) for [Anonymous Symbol]
    cdc_class.o(.ARM.exidx.text.class_event_handler) refers to cdc_class.o(.text.class_event_handler) for [Anonymous Symbol]
    cdc_class.o(.text.usb_vcp_get_rxdata) refers to usbd_core.o(.text.usbd_ept_recv) for usbd_ept_recv
    cdc_class.o(.ARM.exidx.text.usb_vcp_get_rxdata) refers to cdc_class.o(.text.usb_vcp_get_rxdata) for [Anonymous Symbol]
    cdc_class.o(.text.usb_vcp_send_data) refers to usbd_core.o(.text.usbd_ept_send) for usbd_ept_send
    cdc_class.o(.ARM.exidx.text.usb_vcp_send_data) refers to cdc_class.o(.text.usb_vcp_send_data) for [Anonymous Symbol]
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.text.class_init_handler) for class_init_handler
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.text.class_clear_handler) for class_clear_handler
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.text.class_setup_handler) for class_setup_handler
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.text.class_ept0_tx_handler) for class_ept0_tx_handler
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.text.class_ept0_rx_handler) for class_ept0_rx_handler
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.text.class_in_handler) for class_in_handler
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.text.class_out_handler) for class_out_handler
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.text.class_sof_handler) for class_sof_handler
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.text.class_event_handler) for class_event_handler
    cdc_class.o(.data.cdc_class_handler) refers to cdc_class.o(.bss.cdc_struct) for cdc_struct
    cdc_desc.o(.text.get_device_descriptor) refers to cdc_desc.o(.data.device_descriptor) for device_descriptor
    cdc_desc.o(.ARM.exidx.text.get_device_descriptor) refers to cdc_desc.o(.text.get_device_descriptor) for [Anonymous Symbol]
    cdc_desc.o(.ARM.exidx.text.get_device_qualifier) refers to cdc_desc.o(.text.get_device_qualifier) for [Anonymous Symbol]
    cdc_desc.o(.text.get_device_configuration) refers to cdc_desc.o(.data.config_descriptor) for config_descriptor
    cdc_desc.o(.ARM.exidx.text.get_device_configuration) refers to cdc_desc.o(.text.get_device_configuration) for [Anonymous Symbol]
    cdc_desc.o(.ARM.exidx.text.get_device_other_speed) refers to cdc_desc.o(.text.get_device_other_speed) for [Anonymous Symbol]
    cdc_desc.o(.text.get_device_lang_id) refers to cdc_desc.o(.data.langid_descriptor) for langid_descriptor
    cdc_desc.o(.ARM.exidx.text.get_device_lang_id) refers to cdc_desc.o(.text.get_device_lang_id) for [Anonymous Symbol]
    cdc_desc.o(.text.get_device_manufacturer_string) refers to cdc_desc.o(.rodata.str1.1) for .L.str
    cdc_desc.o(.text.get_device_manufacturer_string) refers to cdc_desc.o(.bss.g_usbd_desc_buffer) for g_usbd_desc_buffer
    cdc_desc.o(.text.get_device_manufacturer_string) refers to cdc_desc.o(.bss.vp_desc) for vp_desc
    cdc_desc.o(.ARM.exidx.text.get_device_manufacturer_string) refers to cdc_desc.o(.text.get_device_manufacturer_string) for [Anonymous Symbol]
    cdc_desc.o(.text.get_device_product_string) refers to cdc_desc.o(.rodata.str1.1) for .L.str.1
    cdc_desc.o(.text.get_device_product_string) refers to cdc_desc.o(.bss.g_usbd_desc_buffer) for g_usbd_desc_buffer
    cdc_desc.o(.text.get_device_product_string) refers to cdc_desc.o(.bss.vp_desc) for vp_desc
    cdc_desc.o(.ARM.exidx.text.get_device_product_string) refers to cdc_desc.o(.text.get_device_product_string) for [Anonymous Symbol]
    cdc_desc.o(.text.get_device_serial_string) refers to cdc_desc.o(.data.g_string_serial) for g_string_serial
    cdc_desc.o(.text.get_device_serial_string) refers to cdc_desc.o(.data.serial_descriptor) for serial_descriptor
    cdc_desc.o(.ARM.exidx.text.get_device_serial_string) refers to cdc_desc.o(.text.get_device_serial_string) for [Anonymous Symbol]
    cdc_desc.o(.text.get_device_interface_string) refers to cdc_desc.o(.rodata.str1.1) for .L.str.3
    cdc_desc.o(.text.get_device_interface_string) refers to cdc_desc.o(.bss.g_usbd_desc_buffer) for g_usbd_desc_buffer
    cdc_desc.o(.text.get_device_interface_string) refers to cdc_desc.o(.bss.vp_desc) for vp_desc
    cdc_desc.o(.ARM.exidx.text.get_device_interface_string) refers to cdc_desc.o(.text.get_device_interface_string) for [Anonymous Symbol]
    cdc_desc.o(.text.get_device_config_string) refers to cdc_desc.o(.rodata.str1.1) for .L.str.4
    cdc_desc.o(.text.get_device_config_string) refers to cdc_desc.o(.bss.g_usbd_desc_buffer) for g_usbd_desc_buffer
    cdc_desc.o(.text.get_device_config_string) refers to cdc_desc.o(.bss.vp_desc) for vp_desc
    cdc_desc.o(.ARM.exidx.text.get_device_config_string) refers to cdc_desc.o(.text.get_device_config_string) for [Anonymous Symbol]
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_descriptor) for get_device_descriptor
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_qualifier) for get_device_qualifier
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_configuration) for get_device_configuration
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_other_speed) for get_device_other_speed
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_lang_id) for get_device_lang_id
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_manufacturer_string) for get_device_manufacturer_string
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_product_string) for get_device_product_string
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_serial_string) for get_device_serial_string
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_interface_string) for get_device_interface_string
    cdc_desc.o(.data.cdc_desc_handler) refers to cdc_desc.o(.text.get_device_config_string) for get_device_config_string
    cdc_desc.o(.data.device_descriptor) refers to cdc_desc.o(.data.g_usbd_descriptor) for [Anonymous Symbol]
    cdc_desc.o(.data.config_descriptor) refers to cdc_desc.o(.data.g_usbd_configuration) for [Anonymous Symbol]
    cdc_desc.o(.data.langid_descriptor) refers to cdc_desc.o(.data.g_string_lang_id) for [Anonymous Symbol]
    cdc_desc.o(.data.serial_descriptor) refers to cdc_desc.o(.data.g_string_serial) for [Anonymous Symbol]
    usb_app.o(.text.wk_usb_app_init) refers to usb_app.o(.bss.usb_core_dev) for usb_core_dev
    usb_app.o(.text.wk_usb_app_init) refers to cdc_class.o(.data.cdc_class_handler) for cdc_class_handler
    usb_app.o(.text.wk_usb_app_init) refers to cdc_desc.o(.data.cdc_desc_handler) for cdc_desc_handler
    usb_app.o(.text.wk_usb_app_init) refers to usbd_core.o(.text.usbd_core_init) for usbd_core_init
    usb_app.o(.text.wk_usb_app_init) refers to usbd_core.o(.text.usbd_connect) for usbd_connect
    usb_app.o(.ARM.exidx.text.wk_usb_app_init) refers to usb_app.o(.text.wk_usb_app_init) for [Anonymous Symbol]
    usb_app.o(.text.wk_usb_app_task) refers to usb_app.o(.bss.usb_core_dev) for usb_core_dev
    usb_app.o(.text.wk_usb_app_task) refers to usb_app.o(.bss.usbd_app_buffer_fs1) for usbd_app_buffer_fs1
    usb_app.o(.text.wk_usb_app_task) refers to cdc_class.o(.text.usb_vcp_get_rxdata) for usb_vcp_get_rxdata
    usb_app.o(.text.wk_usb_app_task) refers to hwinterface.o(.text.AnoPTv8HwRecvBytes) for AnoPTv8HwRecvBytes
    usb_app.o(.ARM.exidx.text.wk_usb_app_task) refers to usb_app.o(.text.wk_usb_app_task) for [Anonymous Symbol]
    usb_app.o(.text.wk_usbfs_irq_handler) refers to usb_app.o(.bss.usb_core_dev) for usb_core_dev
    usb_app.o(.text.wk_usbfs_irq_handler) refers to usbd_int.o(.text.usbd_irq_handler) for usbd_irq_handler
    usb_app.o(.ARM.exidx.text.wk_usbfs_irq_handler) refers to usb_app.o(.text.wk_usbfs_irq_handler) for [Anonymous Symbol]
    usb_app.o(.text.usb_delay_ms) refers to wk_system.o(.text.wk_delay_ms) for wk_delay_ms
    usb_app.o(.ARM.exidx.text.usb_delay_ms) refers to usb_app.o(.text.usb_delay_ms) for [Anonymous Symbol]
    usb_app.o(.text.usb_send_data) refers to usb_app.o(.bss.usb_core_dev) for usb_core_dev
    usb_app.o(.text.usb_send_data) refers to cdc_class.o(.text.usb_vcp_send_data) for usb_vcp_send_data
    usb_app.o(.ARM.exidx.text.usb_send_data) refers to usb_app.o(.text.usb_send_data) for [Anonymous Symbol]
    wk_system.o(.text.wk_delay_us) refers to wk_system.o(.bss.ticks_count_us) for ticks_count_us
    wk_system.o(.ARM.exidx.text.wk_delay_us) refers to wk_system.o(.text.wk_delay_us) for [Anonymous Symbol]
    wk_system.o(.text.wk_delay_ms) refers to wk_system.o(.text.wk_delay_us) for wk_delay_us
    wk_system.o(.ARM.exidx.text.wk_delay_ms) refers to wk_system.o(.text.wk_delay_ms) for [Anonymous Symbol]
    wk_system.o(.text.wk_timebase_init) refers to at32a403a_crm.o(.text.crm_clocks_freq_get) for crm_clocks_freq_get
    wk_system.o(.text.wk_timebase_init) refers to at32a403a_misc.o(.text.systick_clock_source_config) for systick_clock_source_config
    wk_system.o(.text.wk_timebase_init) refers to wk_system.o(.bss.ticks_count_us) for ticks_count_us
    wk_system.o(.ARM.exidx.text.wk_timebase_init) refers to wk_system.o(.text.wk_timebase_init) for [Anonymous Symbol]
    algorithm.o(.ARM.exidx.text.fnSysVoltBaseInit) refers to algorithm.o(.text.fnSysVoltBaseInit) for [Anonymous Symbol]
    algorithm.o(.text.fnSysVoltBaseReset) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    algorithm.o(.ARM.exidx.text.fnSysVoltBaseReset) refers to algorithm.o(.text.fnSysVoltBaseReset) for [Anonymous Symbol]
    algorithm.o(.ARM.exidx.text.fnSysVoltBaseCalc) refers to algorithm.o(.text.fnSysVoltBaseCalc) for [Anonymous Symbol]
    delay.o(.text.delay_init) refers to at32a403a_misc.o(.text.systick_clock_source_config) for systick_clock_source_config
    delay.o(.text.delay_init) refers to system_at32a403a.o(.data.system_core_clock) for system_core_clock
    delay.o(.text.delay_init) refers to delay.o(.bss.fac_us) for fac_us
    delay.o(.text.delay_init) refers to delay.o(.bss.fac_ms) for fac_ms
    delay.o(.ARM.exidx.text.delay_init) refers to delay.o(.text.delay_init) for [Anonymous Symbol]
    delay.o(.text.delay_us) refers to delay.o(.bss.fac_us) for fac_us
    delay.o(.ARM.exidx.text.delay_us) refers to delay.o(.text.delay_us) for [Anonymous Symbol]
    delay.o(.text.delay_ms) refers to delay.o(.bss.fac_ms) for fac_ms
    delay.o(.ARM.exidx.text.delay_ms) refers to delay.o(.text.delay_ms) for [Anonymous Symbol]
    delay.o(.text.delay_sec) refers to delay.o(.bss.fac_ms) for fac_ms
    delay.o(.ARM.exidx.text.delay_sec) refers to delay.o(.text.delay_sec) for [Anonymous Symbol]
    mathbasic.o(.ARM.exidx.text.Filter_calc) refers to mathbasic.o(.text.Filter_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.Para_derive) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_vectorcontrol.o(.text.Para_derive) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    motor_vectorcontrol.o(.text.Para_derive) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    motor_vectorcontrol.o(.text.Para_derive) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    motor_vectorcontrol.o(.ARM.exidx.text.Para_derive) refers to motor_vectorcontrol.o(.text.Para_derive) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.park_calc) refers to motor_vectorcontrol.o(.text.park_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.clarke_calc) refers to motor_vectorcontrol.o(.text.clarke_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.VSDclarke_calc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_vectorcontrol.o(.text.VSDclarke_calc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    motor_vectorcontrol.o(.text.VSDclarke_calc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    motor_vectorcontrol.o(.text.VSDclarke_calc) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    motor_vectorcontrol.o(.text.VSDclarke_calc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    motor_vectorcontrol.o(.ARM.exidx.text.VSDclarke_calc) refers to motor_vectorcontrol.o(.text.VSDclarke_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.ipark_calc) refers to motor_vectorcontrol.o(.text.ipark_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.iclarke_calc) refers to motor_vectorcontrol.o(.text.iclarke_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.lpfl_calc) refers to motor_vectorcontrol.o(.text.lpfl_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.pi_speed_const_calc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_vectorcontrol.o(.text.pi_speed_const_calc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    motor_vectorcontrol.o(.text.pi_speed_const_calc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    motor_vectorcontrol.o(.text.pi_speed_const_calc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    motor_vectorcontrol.o(.text.pi_speed_const_calc) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    motor_vectorcontrol.o(.ARM.exidx.text.pi_speed_const_calc) refers to motor_vectorcontrol.o(.text.pi_speed_const_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.pi_current_const_calc) refers to motor_vectorcontrol.o(.text.pi_current_const_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.pi_speed_calc) refers to motor_vectorcontrol.o(.text.pi_speed_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.pi_voltage_calc) refers to motor_vectorcontrol.o(.text.pi_voltage_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.pi_fun_calc) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.pi_flux_calc) refers to motor_vectorcontrol.o(.text.pi_flux_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.para) for para
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.bss.para2) for para2
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_w) for lpf_w
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_w_ref) for lpf_w_ref
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_isd) for lpf_isd
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_isd1) for lpf_isd1
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_isd2) for lpf_isd2
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_isd_ref) for lpf_isd_ref
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_isq) for lpf_isq
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_isq1) for lpf_isq1
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_isq2) for lpf_isq2
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_isq_ref) for lpf_isq_ref
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.lpf_ekf_w) for lpf_ekf_w
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.pi_speed) for pi_speed
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.pi_isd) for pi_isd
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.pi_isq) for pi_isq
    motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.data.pi_flux_w) for pi_flux_w
    motor_vectorcontrol.o(.ARM.exidx.text.Vector_ctrl_ResetTs) refers to motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_w) for lpf_w
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_isd) for lpf_isd
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_isd1) for lpf_isd1
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_isd2) for lpf_isd2
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_isq) for lpf_isq
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_isq1) for lpf_isq1
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_isq2) for lpf_isq2
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_isq_ref) for lpf_isq_ref
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_usd1) for lpf_usd1
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.para) for para
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to sysctl_globalvar.o(.bss.SysRatedParameter) for SysRatedParameter
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_usd2) for lpf_usd2
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_usq1) for lpf_usq1
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_usq2) for lpf_usq2
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.bss.para2) for para2
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_w_ref) for lpf_w_ref
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.lpf_ekf_w) for lpf_ekf_w
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.pi_current_const) for pi_current_const
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to sysctl_globalvar.o(.bss.HMIBuffer) for HMIBuffer
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.pi_speed) for pi_speed
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.pi_flux_w) for pi_flux_w
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.pi_isd1) for pi_isd1
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.data.pi_isq1) for pi_isq1
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    motor_vectorcontrol.o(.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.bss.gOpenLoopFreqCtrl) for gOpenLoopFreqCtrl
    motor_vectorcontrol.o(.ARM.exidx.text.Vector_ctrl_init) refers to motor_vectorcontrol.o(.text.Vector_ctrl_init) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.OpenLoopFreqCtrl_Init) refers to motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Init) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_speed) for pi_speed
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_isd) for pi_isd
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_isq) for pi_isq
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_isd1) for pi_isd1
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_isq1) for pi_isq1
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_isd2) for pi_isd2
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_isq2) for pi_isq2
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_isdz) for pi_isdz
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_isqz) for pi_isqz
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to sysctl_globalvar.o(.bss.HMIBuffer) for HMIBuffer
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.pi_isxz) for pi_isxz
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to sysctl_globalvar.o(.data.SysEnviConfg) for SysEnviConfg
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.bss.Ifoward) for Ifoward
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.lpf_isq1) for lpf_isq1
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.lpf_isq2) for lpf_isq2
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.lpf_isd1) for lpf_isd1
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.lpf_isd2) for lpf_isd2
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.lpf_usq1) for lpf_usq1
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.lpf_usq2) for lpf_usq2
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.lpf_usd1) for lpf_usd1
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.data.lpf_usd2) for lpf_usd2
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.bss.FeedforwordFlag) for FeedforwordFlag
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    motor_vectorcontrol.o(.text.Vector_ctrl_reset) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    motor_vectorcontrol.o(.ARM.exidx.text.Vector_ctrl_reset) refers to motor_vectorcontrol.o(.text.Vector_ctrl_reset) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.pi_speed) for pi_speed
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.ipark1) for ipark1
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.iclarke2) for iclarke2
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.bss.gOpenLoopFreqCtrl) for gOpenLoopFreqCtrl
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update) for OpenLoopFreqCtrl_Update
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to arm_sin_f32.o(.text.arm_sin_f32) for arm_sin_f32
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.text.SVPWM) for SVPWM
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.Ia_flt) for Ia_flt
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.clarke1) for clarke1
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.Ib_flt) for Ib_flt
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.Ic_flt) for Ic_flt
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.park1) for park1
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.pi_isd1) for pi_isd1
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.pi_isq1) for pi_isq1
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.bss.FeedforwordFlag) for FeedforwordFlag
    motor_vectorcontrol.o(.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.data.para) for para
    motor_vectorcontrol.o(.ARM.exidx.text.Vector_ctrl_calc) refers to motor_vectorcontrol.o(.text.Vector_ctrl_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.pi_speed_inc_fun_calc) refers to motor_vectorcontrol.o(.text.pi_speed_inc_fun_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update) refers to dcmp.o(x$fpl$fcmp) for __aeabi_dcmpge
    motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    motor_vectorcontrol.o(.ARM.exidx.text.OpenLoopFreqCtrl_Update) refers to motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.SVPWM) refers to motor_vectorcontrol.o(.bss.Tc1) for Tc1
    motor_vectorcontrol.o(.text.SVPWM) refers to motor_vectorcontrol.o(.bss.Tc2) for Tc2
    motor_vectorcontrol.o(.text.SVPWM) refers to motor_vectorcontrol.o(.bss.Tc3) for Tc3
    motor_vectorcontrol.o(.text.SVPWM) refers to motordata.o(.data.gMotorData) for gMotorData
    motor_vectorcontrol.o(.ARM.exidx.text.SVPWM) refers to motor_vectorcontrol.o(.text.SVPWM) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.lpf_filter) refers to motor_vectorcontrol.o(.text.lpf_filter) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.lpfi_calc) refers to motor_vectorcontrol.o(.text.lpfi_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.IVSDclarke_calc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_vectorcontrol.o(.text.IVSDclarke_calc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    motor_vectorcontrol.o(.text.IVSDclarke_calc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    motor_vectorcontrol.o(.text.IVSDclarke_calc) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    motor_vectorcontrol.o(.ARM.exidx.text.IVSDclarke_calc) refers to motor_vectorcontrol.o(.text.IVSDclarke_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.UpdatePwm) refers to motor_vectorcontrol.o(.text.UpdatePwm) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.ResolverInitialAngleCalibration) refers to motor_vectorcontrol.o(.bss.ResolverInitialAngleCalibration.cnt) for ResolverInitialAngleCalibration.cnt
    motor_vectorcontrol.o(.text.ResolverInitialAngleCalibration) refers to motor_vectorcontrol.o(.text.SVPWM) for SVPWM
    motor_vectorcontrol.o(.ARM.exidx.text.ResolverInitialAngleCalibration) refers to motor_vectorcontrol.o(.text.ResolverInitialAngleCalibration) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.FixedAngle) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    motor_vectorcontrol.o(.text.FixedAngle) refers to arm_sin_f32.o(.text.arm_sin_f32) for arm_sin_f32
    motor_vectorcontrol.o(.ARM.exidx.text.FixedAngle) refers to motor_vectorcontrol.o(.text.FixedAngle) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.CalcTRAP) refers to motor_vectorcontrol.o(.text.CalcTRAP) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.TrapeAccelerationCurve) refers to motor_vectorcontrol.o(.bss.TrapeAccelerationCurve.time_cnt) for TrapeAccelerationCurve.time_cnt
    motor_vectorcontrol.o(.ARM.exidx.text.TrapeAccelerationCurve) refers to motor_vectorcontrol.o(.text.TrapeAccelerationCurve) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.update_wave) refers to motordata.o(.data.gMotorData) for gMotorData
    motor_vectorcontrol.o(.text.update_wave) refers to motor_vectorcontrol.o(.data.clarke1) for clarke1
    motor_vectorcontrol.o(.text.update_wave) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    motor_vectorcontrol.o(.text.update_wave) refers to sysctl_globalvar.o(.bss.machineAngle) for machineAngle
    motor_vectorcontrol.o(.text.update_wave) refers to sys_isr_controller.o(.bss.faultId) for faultId
    motor_vectorcontrol.o(.text.update_wave) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    motor_vectorcontrol.o(.text.update_wave) refers to motordata.o(.text.MotorDataSendFrame) for MotorDataSendFrame
    motor_vectorcontrol.o(.ARM.exidx.text.update_wave) refers to motor_vectorcontrol.o(.text.update_wave) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.VoltageBalance_calc) refers to motor_vectorcontrol.o(.text.VoltageBalance_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.marsest_calc) refers to motor_vectorcontrol.o(.text.marsest_calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.text.WeakFluxCtr) refers to motor_vectorcontrol.o(.bss.WeakFluxCtr.out) for WeakFluxCtr.out
    motor_vectorcontrol.o(.ARM.exidx.text.WeakFluxCtr) refers to motor_vectorcontrol.o(.text.WeakFluxCtr) for [Anonymous Symbol]
    motor_vectorcontrol.o(.ARM.exidx.text.PI_Speed_Incremental_Calc) refers to motor_vectorcontrol.o(.text.PI_Speed_Incremental_Calc) for [Anonymous Symbol]
    motor_vectorcontrol.o(.data.para) refers to motor_vectorcontrol.o(.text.Para_derive) for Para_derive
    motor_vectorcontrol.o(.data.park1) refers to motor_vectorcontrol.o(.text.park_calc) for park_calc
    motor_vectorcontrol.o(.data.clarke1) refers to motor_vectorcontrol.o(.text.clarke_calc) for clarke_calc
    motor_vectorcontrol.o(.data.vsdclarke) refers to motor_vectorcontrol.o(.text.VSDclarke_calc) for VSDclarke_calc
    motor_vectorcontrol.o(.data.ipark1) refers to motor_vectorcontrol.o(.text.ipark_calc) for ipark_calc
    motor_vectorcontrol.o(.data.ipark2) refers to motor_vectorcontrol.o(.text.ipark_calc) for ipark_calc
    motor_vectorcontrol.o(.data.iclarke1) refers to motor_vectorcontrol.o(.text.iclarke_calc) for iclarke_calc
    motor_vectorcontrol.o(.data.iclarke2) refers to motor_vectorcontrol.o(.text.iclarke_calc) for iclarke_calc
    motor_vectorcontrol.o(.data.lpf_isd) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_isd1) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_isd2) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_isd_ref) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_isq) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_isq1) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_isq2) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_usd1) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_usd2) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_usq1) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_usq2) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_isq_ref) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_ekf_w) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_w) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.lpf_w_ref) refers to motor_vectorcontrol.o(.text.lpfl_calc) for lpfl_calc
    motor_vectorcontrol.o(.data.pi_speed_const) refers to motor_vectorcontrol.o(.text.pi_speed_const_calc) for pi_speed_const_calc
    motor_vectorcontrol.o(.data.pi_current_const) refers to motor_vectorcontrol.o(.text.pi_current_const_calc) for pi_current_const_calc
    motor_vectorcontrol.o(.data.pi_speed) refers to motor_vectorcontrol.o(.text.pi_speed_calc) for pi_speed_calc
    motor_vectorcontrol.o(.data.pi_voltage) refers to motor_vectorcontrol.o(.text.pi_voltage_calc) for pi_voltage_calc
    motor_vectorcontrol.o(.data.pi_isd) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for pi_fun_calc
    motor_vectorcontrol.o(.data.pi_isq) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for pi_fun_calc
    motor_vectorcontrol.o(.data.pi_isd1) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for pi_fun_calc
    motor_vectorcontrol.o(.data.pi_isq1) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for pi_fun_calc
    motor_vectorcontrol.o(.data.pi_isd2) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for pi_fun_calc
    motor_vectorcontrol.o(.data.pi_isq2) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for pi_fun_calc
    motor_vectorcontrol.o(.data.pi_isqz) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for pi_fun_calc
    motor_vectorcontrol.o(.data.pi_isdz) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for pi_fun_calc
    motor_vectorcontrol.o(.data.pi_isxz) refers to motor_vectorcontrol.o(.text.pi_fun_calc) for pi_fun_calc
    motor_vectorcontrol.o(.data.pi_flux_w) refers to motor_vectorcontrol.o(.text.pi_flux_calc) for pi_flux_calc
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to at32a403a_adc.o(.text.adc_interrupt_flag_get) for adc_interrupt_flag_get
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to at32a403a_adc.o(.text.adc_flag_clear) for adc_flag_clear
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to adc_pmsm.o(.text.ADC_PMSM_ProcessCurrents) for ADC_PMSM_ProcessCurrents
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to sysctl_globalvar.o(.data.SysMoore) for SysMoore
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to at32a403a_tmr.o(.text.tmr_output_enable) for tmr_output_enable
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to sysctl_globalvar.o(.bss.SysErrIndexReg) for SysErrIndexReg
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to sys_isr_controller.o(.bss.I_overCru) for I_overCru
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to sys_isr_controller.o(.bss.faultId) for faultId
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to motor_vectorcontrol.o(.data.pi_isd) for pi_isd
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to motor_vectorcontrol.o(.data.pi_isq) for pi_isq
    sys_isr_controller.o(.text.ADC1_IRQHandler) refers to enc_speed.o(.text.GetElectricalAngle_ENC) for GetElectricalAngle_ENC
    sys_isr_controller.o(.ARM.exidx.text.ADC1_IRQHandler) refers to sys_isr_controller.o(.text.ADC1_IRQHandler) for [Anonymous Symbol]
    sys_isr_controller.o(.text.SysCurProtect) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sys_isr_controller.o(.text.SysCurProtect) refers to at32a403a_tmr.o(.text.tmr_output_enable) for tmr_output_enable
    sys_isr_controller.o(.text.SysCurProtect) refers to sysctl_globalvar.o(.bss.SysErrIndexReg) for SysErrIndexReg
    sys_isr_controller.o(.text.SysCurProtect) refers to sysctl_globalvar.o(.data.SysMoore) for SysMoore
    sys_isr_controller.o(.text.SysCurProtect) refers to sys_isr_controller.o(.bss.I_overCru) for I_overCru
    sys_isr_controller.o(.ARM.exidx.text.SysCurProtect) refers to sys_isr_controller.o(.text.SysCurProtect) for [Anonymous Symbol]
    sys_isr_controller.o(.text.DATAPeriodDSPtoARM) refers to sysctl_globalvar.o(.bss.SysErrIndexReg) for SysErrIndexReg
    sys_isr_controller.o(.text.DATAPeriodDSPtoARM) refers to sysctl_globalvar.o(.bss.DMABuf1) for DMABuf1
    sys_isr_controller.o(.text.DATAPeriodDSPtoARM) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sys_isr_controller.o(.text.DATAPeriodDSPtoARM) refers to sysctl_globalvar.o(.data.pRunSpeed) for pRunSpeed
    sys_isr_controller.o(.text.DATAPeriodDSPtoARM) refers to sysctl_globalvar.o(.data.pEstSpeed) for pEstSpeed
    sys_isr_controller.o(.ARM.exidx.text.DATAPeriodDSPtoARM) refers to sys_isr_controller.o(.text.DATAPeriodDSPtoARM) for [Anonymous Symbol]
    sys_isr_controller.o(.text.DATAPeriodARMtoDSP) refers to sysctl_globalvar.o(.data.SysMoore) for SysMoore
    sys_isr_controller.o(.text.DATAPeriodARMtoDSP) refers to sysctl_globalvar.o(.bss.DMABuf1) for DMABuf1
    sys_isr_controller.o(.text.DATAPeriodARMtoDSP) refers to sysctl_globalvar.o(.data.SysEnviConfg) for SysEnviConfg
    sys_isr_controller.o(.text.DATAPeriodARMtoDSP) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sys_isr_controller.o(.ARM.exidx.text.DATAPeriodARMtoDSP) refers to sys_isr_controller.o(.text.DATAPeriodARMtoDSP) for [Anonymous Symbol]
    sys_isr_controller.o(.ARM.exidx.text.flux_thetaChange) refers to sys_isr_controller.o(.text.flux_thetaChange) for [Anonymous Symbol]
    sys_isr_controller.o(.data.Filter_udch) refers to mathbasic.o(.text.Filter_calc) for Filter_calc
    sys_isr_controller.o(.data.Filter_udcl) refers to mathbasic.o(.text.Filter_calc) for Filter_calc
    sys_isr_controller.o(.data.Filter_idc) refers to mathbasic.o(.text.Filter_calc) for Filter_calc
    sys_isr_controller.o(.data.Filter_idc1) refers to mathbasic.o(.text.Filter_calc) for Filter_calc
    sys_isr_controller.o(.data.Filter_idc2) refers to mathbasic.o(.text.Filter_calc) for Filter_calc
    sys_isr_controller.o(.data.Filter_speed) refers to mathbasic.o(.text.Filter_calc) for Filter_calc
    sysctl_analogprocess.o(.text.fnAISample) refers to sysctl_globalvar.o(.data.SysSampOffset) for SysSampOffset
    sysctl_analogprocess.o(.ARM.exidx.text.fnAISample) refers to sysctl_analogprocess.o(.text.fnAISample) for [Anonymous Symbol]
    sysctl_analogprocess.o(.text.fnParaUpdateSysSamScaParameter) refers to sysctl_globalvar.o(.data.SysBaseValue) for SysBaseValue
    sysctl_analogprocess.o(.text.fnParaUpdateSysSamScaParameter) refers to sysctl_globalvar.o(.data.SysSamScaParameter) for SysSamScaParameter
    sysctl_analogprocess.o(.ARM.exidx.text.fnParaUpdateSysSamScaParameter) refers to sysctl_analogprocess.o(.text.fnParaUpdateSysSamScaParameter) for [Anonymous Symbol]
    sysctl_analogprocess.o(.text.fnSysBaseValueCal) refers to sysctl_globalvar.o(.bss.SysRatedParameter) for SysRatedParameter
    sysctl_analogprocess.o(.text.fnSysBaseValueCal) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    sysctl_analogprocess.o(.text.fnSysBaseValueCal) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sysctl_analogprocess.o(.text.fnSysBaseValueCal) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    sysctl_analogprocess.o(.text.fnSysBaseValueCal) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    sysctl_analogprocess.o(.ARM.exidx.text.fnSysBaseValueCal) refers to sysctl_analogprocess.o(.text.fnSysBaseValueCal) for [Anonymous Symbol]
    sysctl_analogprocess.o(.text.fnSysOffsetInit) refers to sysctl_globalvar.o(.data.pOffsetRam) for pOffsetRam
    sysctl_analogprocess.o(.text.fnSysOffsetInit) refers to sysctl_globalvar.o(.data.SysSampOffset) for SysSampOffset
    sysctl_analogprocess.o(.ARM.exidx.text.fnSysOffsetInit) refers to sysctl_analogprocess.o(.text.fnSysOffsetInit) for [Anonymous Symbol]
    sysctl_analogprocess.o(.text.fnSysOffsetParameterCal) refers to sysctl_globalvar.o(.data.AnalogInput) for AnalogInput
    sysctl_analogprocess.o(.ARM.exidx.text.fnSysOffsetParameterCal) refers to sysctl_analogprocess.o(.text.fnSysOffsetParameterCal) for [Anonymous Symbol]
    sysctl_globalvar.o(.data.pOffsetRam) refers to sysctl_globalvar.o(.bss.HMIBuffer) for HMIBuffer
    sysctl_globalvar.o(.data.pWaveParamStart) refers to sysctl_globalvar.o(.bss.DMABuf1) for DMABuf1
    sysctl_globalvar.o(.data.pRunSpeed) refers to sysctl_globalvar.o(.bss.DMABuf1) for DMABuf1
    sysctl_globalvar.o(.data.pEstSpeed) refers to sysctl_globalvar.o(.bss.DMABuf1) for DMABuf1
    sysctl_globalvar.o(.data.pFaultWaveStart) refers to sysctl_globalvar.o(.bss.DMABuf1) for DMABuf1
    sysctl_globalvar.o(.data.SysMoore) refers to sysctl_sysmoore.o(.text.fnSysMooreCal) for fnSysMooreCal
    sysctl_globalvar.o(.data.ScopeDATAUpLoad) refers to sysctl_globalvar.o(.bss.HMIBuffer) for HMIBuffer
    sysctl_globalvar.o(.data.SysSampOffset) refers to sysctl_analogprocess.o(.text.fnSysOffsetInit) for fnSysOffsetInit
    sysctl_globalvar.o(.data.SysSampOffset) refers to sysctl_analogprocess.o(.text.fnSysOffsetParameterCal) for fnSysOffsetParameterCal
    sysctl_globalvar.o(.data.SysBaseValue) refers to sysctl_analogprocess.o(.text.fnSysBaseValueCal) for fnSysBaseValueCal
    sysctl_globalvar.o(.data.AnalogInput) refers to sysctl_analogprocess.o(.text.fnAISample) for fnAISample
    sysctl_globalvar.o(.data.RotorSpeedclc) refers to sysctl_rotorget.o(.text.fnRotorSpeedclc) for fnRotorSpeedclc
    sysctl_globalvar.o(.data.SysVoltBase) refers to algorithm.o(.text.fnSysVoltBaseCalc) for fnSysVoltBaseCalc
    sysctl_globalvar.o(.data.SysVoltBase) refers to algorithm.o(.text.fnSysVoltBaseReset) for fnSysVoltBaseReset
    sysctl_globalvar.o(.data.SysVoltBase) refers to algorithm.o(.text.fnSysVoltBaseInit) for fnSysVoltBaseInit
    sysctl_globalvar.o(.data.SynMotorVc) refers to motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs) for Vector_ctrl_ResetTs
    sysctl_globalvar.o(.data.SynMotorVc) refers to motor_vectorcontrol.o(.text.Vector_ctrl_init) for Vector_ctrl_init
    sysctl_globalvar.o(.data.SynMotorVc) refers to motor_vectorcontrol.o(.text.Vector_ctrl_reset) for Vector_ctrl_reset
    sysctl_globalvar.o(.data.SynMotorVc) refers to motor_vectorcontrol.o(.text.Vector_ctrl_calc) for Vector_ctrl_calc
    sysctl_globalvar.o(.data.SynMotorVc) refers to motor_vectorcontrol.o(.text.VoltageBalance_calc) for VoltageBalance_calc
    sysctl_globalvar.o(.data.SynMotorVc) refers to motor_vectorcontrol.o(.text.marsest_calc) for marsest_calc
    sysctl_rotorget.o(.text.fnRotorSpeedclc) refers to sysctl_globalvar.o(.data.RotorSpeedclc) for RotorSpeedclc
    sysctl_rotorget.o(.text.fnRotorSpeedclc) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sysctl_rotorget.o(.text.fnRotorSpeedclc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    sysctl_rotorget.o(.text.fnRotorSpeedclc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sysctl_rotorget.o(.text.fnRotorSpeedclc) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    sysctl_rotorget.o(.text.fnRotorSpeedclc) refers to dcmp.o(x$fpl$fcmp) for __aeabi_dcmplt
    sysctl_rotorget.o(.ARM.exidx.text.fnRotorSpeedclc) refers to sysctl_rotorget.o(.text.fnRotorSpeedclc) for [Anonymous Symbol]
    sysctl_rotorget.o(.text.GetElectricalAngle) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sysctl_rotorget.o(.text.GetElectricalAngle) refers to sysctl_rotorget.o(.bss.resolver_angle) for resolver_angle
    sysctl_rotorget.o(.ARM.exidx.text.GetElectricalAngle) refers to sysctl_rotorget.o(.text.GetElectricalAngle) for [Anonymous Symbol]
    sysctl_rotorget.o(.text.GetRotorAngle) refers to sysctl_rotorget.o(.bss.GetRotorAngle.start) for GetRotorAngle.start
    sysctl_rotorget.o(.text.GetRotorAngle) refers to sysctl_rotorget.o(.bss.GetRotorAngle.last_angle) for GetRotorAngle.last_angle
    sysctl_rotorget.o(.text.GetRotorAngle) refers to sysctl_rotorget.o(.bss.GetRotorAngle.cycler_counter) for GetRotorAngle.cycler_counter
    sysctl_rotorget.o(.text.GetRotorAngle) refers to sysctl_rotorget.o(.bss.rotor_cont) for rotor_cont
    sysctl_rotorget.o(.text.GetRotorAngle) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    sysctl_rotorget.o(.text.GetRotorAngle) refers to dcmp.o(x$fpl$fcmp) for __aeabi_dcmplt
    sysctl_rotorget.o(.text.GetRotorAngle) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sysctl_rotorget.o(.text.GetRotorAngle) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    sysctl_rotorget.o(.ARM.exidx.text.GetRotorAngle) refers to sysctl_rotorget.o(.text.GetRotorAngle) for [Anonymous Symbol]
    sysctl_rotorget.o(.text.GetRotorSpeed) refers to sysctl_rotorget.o(.bss.GetRotorSpeed.last_angle) for GetRotorSpeed.last_angle
    sysctl_rotorget.o(.text.GetRotorSpeed) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    sysctl_rotorget.o(.text.GetRotorSpeed) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sysctl_rotorget.o(.text.GetRotorSpeed) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    sysctl_rotorget.o(.ARM.exidx.text.GetRotorSpeed) refers to sysctl_rotorget.o(.text.GetRotorSpeed) for [Anonymous Symbol]
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.SysErrIndexReg) for SysErrIndexReg
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to at32a403a_tmr.o(.text.tmr_output_enable) for tmr_output_enable
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysfsm.o(.bss.g_SystemStatus) for g_SystemStatus
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.HMIBuffer) for HMIBuffer
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.SysCtlReg) for SysCtlReg
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.SysProParamReg) for SysProParamReg
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.ARMFaultReg) for ARMFaultReg
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.DSPFaultCodeReg) for DSPFaultCodeReg
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.SysCtlModeREG) for SysCtlModeREG
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.DMABuf1) for DMABuf1
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.SysRatedParameter) for SysRatedParameter
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.data.SysBaseValue) for SysBaseValue
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_analogprocess.o(.text.fnParaUpdateSysSamScaParameter) for fnParaUpdateSysSamScaParameter
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.bss.SysCtlParameter) for SysCtlParameter
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to motor_vectorcontrol.o(.data.para) for para
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to sysctl_globalvar.o(.data.SysEnviConfg) for SysEnviConfg
    sysctl_sysmoore.o(.text.fnSysMooreCal) refers to hwinterface.o(.text.Usb_printf) for Usb_printf
    sysctl_sysmoore.o(.ARM.exidx.text.fnSysMooreCal) refers to sysctl_sysmoore.o(.text.fnSysMooreCal) for [Anonymous Symbol]
    sysctl_sysmoore.o(.text.fnSystemResetParam) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sysctl_sysmoore.o(.ARM.exidx.text.fnSystemResetParam) refers to sysctl_sysmoore.o(.text.fnSystemResetParam) for [Anonymous Symbol]
    sysctl_sysmoore.o(.text.fnSystemRUNParam) refers to sysctl_globalvar.o(.bss.HMIBuffer) for HMIBuffer
    sysctl_sysmoore.o(.text.fnSystemRUNParam) refers to sysctl_globalvar.o(.bss.SysCtlReg) for SysCtlReg
    sysctl_sysmoore.o(.text.fnSystemRUNParam) refers to sysctl_globalvar.o(.bss.SysProParamReg) for SysProParamReg
    sysctl_sysmoore.o(.text.fnSystemRUNParam) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    sysctl_sysmoore.o(.text.fnSystemRUNParam) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sysctl_sysmoore.o(.ARM.exidx.text.fnSystemRUNParam) refers to sysctl_sysmoore.o(.text.fnSystemRUNParam) for [Anonymous Symbol]
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to sysctl_globalvar.o(.bss.DMABuf1) for DMABuf1
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to sysctl_globalvar.o(.bss.SysRatedParameter) for SysRatedParameter
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to sysctl_globalvar.o(.data.SysBaseValue) for SysBaseValue
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to sysctl_analogprocess.o(.text.fnParaUpdateSysSamScaParameter) for fnParaUpdateSysSamScaParameter
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to sysctl_globalvar.o(.bss.HMIBuffer) for HMIBuffer
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to sysctl_globalvar.o(.bss.SysCtlParameter) for SysCtlParameter
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to sysctl_globalvar.o(.data.SysEnviConfg) for SysEnviConfg
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to motor_vectorcontrol.o(.data.para) for para
    sysctl_sysmoore.o(.text.fnSystemReadyParam) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sysctl_sysmoore.o(.ARM.exidx.text.fnSystemReadyParam) refers to sysctl_sysmoore.o(.text.fnSystemReadyParam) for [Anonymous Symbol]
    sysctl_sysmoore.o(.text.fnSysFaultReset) refers to sysctl_globalvar.o(.bss.SysErrIndexReg) for SysErrIndexReg
    sysctl_sysmoore.o(.text.fnSysFaultReset) refers to sysctl_globalvar.o(.bss.ARMFaultReg) for ARMFaultReg
    sysctl_sysmoore.o(.text.fnSysFaultReset) refers to sysctl_globalvar.o(.bss.DSPFaultCodeReg) for DSPFaultCodeReg
    sysctl_sysmoore.o(.text.fnSysFaultReset) refers to sysctl_globalvar.o(.bss.SysCtlModeREG) for SysCtlModeREG
    sysctl_sysmoore.o(.ARM.exidx.text.fnSysFaultReset) refers to sysctl_sysmoore.o(.text.fnSysFaultReset) for [Anonymous Symbol]
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sysctl_globalvar.o(.data.RotorSpeedclc) for RotorSpeedclc
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sysctl_globalvar.o(.bss.SysErrIndexReg) for SysErrIndexReg
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sysctl_globalvar.o(.bss.ARMFaultReg) for ARMFaultReg
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sysctl_globalvar.o(.bss.DSPFaultCodeReg) for DSPFaultCodeReg
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sysctl_globalvar.o(.bss.SysCtlModeREG) for SysCtlModeREG
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sysctl_globalvar.o(.bss.SysCtlReg) for SysCtlReg
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sysctl_globalvar.o(.data.SysSampOffset) for SysSampOffset
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sysctl_globalvar.o(.data.SynMotorVc) for SynMotorVc
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sys_isr_controller.o(.data.Filter_idc) for Filter_idc
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sys_isr_controller.o(.data.Filter_idc1) for Filter_idc1
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sys_isr_controller.o(.data.Filter_idc2) for Filter_idc2
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sys_isr_controller.o(.data.Filter_udch) for Filter_udch
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sys_isr_controller.o(.data.Filter_udcl) for Filter_udcl
    sysctl_sysmoore.o(.text.fnSystemInitParam) refers to sys_isr_controller.o(.data.Filter_speed) for Filter_speed
    sysctl_sysmoore.o(.ARM.exidx.text.fnSystemInitParam) refers to sysctl_sysmoore.o(.text.fnSystemInitParam) for [Anonymous Symbol]
    sysctl_sysmoore.o(.text.fnSysRatedParameter) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    sysctl_sysmoore.o(.text.fnSysRatedParameter) refers to sysctl_globalvar.o(.bss.SysRatedParameter) for SysRatedParameter
    sysctl_sysmoore.o(.ARM.exidx.text.fnSysRatedParameter) refers to sysctl_sysmoore.o(.text.fnSysRatedParameter) for [Anonymous Symbol]
    sysctl_sysmoore.o(.text.fnSysParamterRef) refers to sysctl_globalvar.o(.data.pARMParamRdFlag) for pARMParamRdFlag
    sysctl_sysmoore.o(.text.fnSysParamterRef) refers to sysctl_globalvar.o(.data.SysSampOffset) for SysSampOffset
    sysctl_sysmoore.o(.text.fnSysParamterRef) refers to sysctl_globalvar.o(.data.RdDSPtoARM) for RdDSPtoARM
    sysctl_sysmoore.o(.ARM.exidx.text.fnSysParamterRef) refers to sysctl_sysmoore.o(.text.fnSysParamterRef) for [Anonymous Symbol]
    sysctl_sysmoore.o(.ARM.exidx.text.fnVarCopyToRam) refers to sysctl_sysmoore.o(.text.fnVarCopyToRam) for [Anonymous Symbol]
    sysctl_sysmoore.o(.ARM.exidx.text.fnSysHMICommInit) refers to sysctl_sysmoore.o(.text.fnSysHMICommInit) for [Anonymous Symbol]
    anoptv8cmd.o(.text.AnoPTv8CmdFrameAnl) refers to anoptv8cmd.o(.bss.cmdCount) for cmdCount
    anoptv8cmd.o(.text.AnoPTv8CmdFrameAnl) refers to anoptv8cmd.o(.bss.pCmdInfoList) for pCmdInfoList
    anoptv8cmd.o(.text.AnoPTv8CmdFrameAnl) refers to anoptv8framefactory.o(.text.AnoPTv8SendCheck) for AnoPTv8SendCheck
    anoptv8cmd.o(.text.AnoPTv8CmdFrameAnl) refers to anoptv8framefactory.o(.text.AnoPTv8SendCmdNum) for AnoPTv8SendCmdNum
    anoptv8cmd.o(.text.AnoPTv8CmdFrameAnl) refers to anoptv8framefactory.o(.text.AnoPTv8SendCmdInfo) for AnoPTv8SendCmdInfo
    anoptv8cmd.o(.ARM.exidx.text.AnoPTv8CmdFrameAnl) refers to anoptv8cmd.o(.text.AnoPTv8CmdFrameAnl) for [Anonymous Symbol]
    anoptv8cmd.o(.text.AnoPTv8CmdGetCount) refers to anoptv8cmd.o(.bss.cmdCount) for cmdCount
    anoptv8cmd.o(.ARM.exidx.text.AnoPTv8CmdGetCount) refers to anoptv8cmd.o(.text.AnoPTv8CmdGetCount) for [Anonymous Symbol]
    anoptv8cmd.o(.text.AnoPTv8CmdGetInfo) refers to anoptv8cmd.o(.bss.cmdCount) for cmdCount
    anoptv8cmd.o(.text.AnoPTv8CmdGetInfo) refers to anoptv8cmd.o(.bss.pCmdInfoList) for pCmdInfoList
    anoptv8cmd.o(.ARM.exidx.text.AnoPTv8CmdGetInfo) refers to anoptv8cmd.o(.text.AnoPTv8CmdGetInfo) for [Anonymous Symbol]
    anoptv8cmd.o(.text.AnoPTv8CmdRegister) refers to anoptv8cmd.o(.bss.cmdCount) for cmdCount
    anoptv8cmd.o(.text.AnoPTv8CmdRegister) refers to anoptv8cmd.o(.bss.pCmdInfoList) for pCmdInfoList
    anoptv8cmd.o(.ARM.exidx.text.AnoPTv8CmdRegister) refers to anoptv8cmd.o(.text.AnoPTv8CmdRegister) for [Anonymous Symbol]
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8CalFrameCheck) refers to anoptv8framefactory.o(.text.AnoPTv8CalFrameCheck) for [Anonymous Symbol]
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8CalFrameCheckInBuffer) refers to anoptv8framefactory.o(.text.AnoPTv8CalFrameCheckInBuffer) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendBuf) refers to anoptv8run.o(.bss.AnoPTv8LargeTxBuf) for AnoPTv8LargeTxBuf
    anoptv8framefactory.o(.text.AnoPTv8SendBuf) refers to hwinterface.o(.text.AnoPTv8HwSendBytes) for AnoPTv8HwSendBytes
    anoptv8framefactory.o(.text.AnoPTv8SendBuf) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendBuf) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendCheck) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendCheck) refers to anoptv8framefactory.o(.text.AnoPTv8SendCheck) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendDevInfo) refers to anoptv8framefactory.o(.bss.AnoPTv8SendDevInfo.dev_info_send_count) for AnoPTv8SendDevInfo.dev_info_send_count
    anoptv8framefactory.o(.text.AnoPTv8SendDevInfo) refers to anoptv8framefactory.o(.rodata.str1.1) for .L.str
    anoptv8framefactory.o(.text.AnoPTv8SendDevInfo) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendDevInfo) refers to anoptv8framefactory.o(.text.AnoPTv8SendDevInfo) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendStr) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendStr) refers to anoptv8framefactory.o(.text.AnoPTv8SendStr) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendValStr) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendValStr) refers to anoptv8framefactory.o(.text.AnoPTv8SendValStr) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendParNum) refers to anoptv8par.o(.text.AnoPTv8ParGetCount) for AnoPTv8ParGetCount
    anoptv8framefactory.o(.text.AnoPTv8SendParNum) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendParNum) refers to anoptv8framefactory.o(.text.AnoPTv8SendParNum) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendParVal) refers to anoptv8par.o(.text.AnoPTv8GetParamType) for AnoPTv8GetParamType
    anoptv8framefactory.o(.text.AnoPTv8SendParVal) refers to anoptv8par.o(.text.AnoPTv8ParGetInfo) for AnoPTv8ParGetInfo
    anoptv8framefactory.o(.text.AnoPTv8SendParVal) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendParVal) refers to anoptv8framefactory.o(.text.AnoPTv8SendParVal) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendParInfo) refers to anoptv8par.o(.text.AnoPTv8ParGetCount) for AnoPTv8ParGetCount
    anoptv8framefactory.o(.text.AnoPTv8SendParInfo) refers to anoptv8par.o(.text.AnoPTv8ParGetInfo) for AnoPTv8ParGetInfo
    anoptv8framefactory.o(.text.AnoPTv8SendParInfo) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    anoptv8framefactory.o(.text.AnoPTv8SendParInfo) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendParInfo) refers to anoptv8framefactory.o(.text.AnoPTv8SendParInfo) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendCmdNum) refers to anoptv8cmd.o(.text.AnoPTv8CmdGetCount) for AnoPTv8CmdGetCount
    anoptv8framefactory.o(.text.AnoPTv8SendCmdNum) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendCmdNum) refers to anoptv8framefactory.o(.text.AnoPTv8SendCmdNum) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendCmdInfo) refers to anoptv8cmd.o(.text.AnoPTv8CmdGetCount) for AnoPTv8CmdGetCount
    anoptv8framefactory.o(.text.AnoPTv8SendCmdInfo) refers to anoptv8cmd.o(.text.AnoPTv8CmdGetInfo) for AnoPTv8CmdGetInfo
    anoptv8framefactory.o(.text.AnoPTv8SendCmdInfo) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    anoptv8framefactory.o(.text.AnoPTv8SendCmdInfo) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendCmdInfo) refers to anoptv8framefactory.o(.text.AnoPTv8SendCmdInfo) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendAnyFrame) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendAnyFrame) refers to anoptv8framefactory.o(.text.AnoPTv8SendAnyFrame) for [Anonymous Symbol]
    anoptv8framefactory.o(.text.AnoPTv8SendIapCmd) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    anoptv8framefactory.o(.text.AnoPTv8SendIapCmd) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendIapCmd) refers to anoptv8framefactory.o(.text.AnoPTv8SendIapCmd) for [Anonymous Symbol]
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to anoptv8par.o(.bss.AnoPTv8ParFrameAnl.dev_info_req_count) for AnoPTv8ParFrameAnl.dev_info_req_count
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to anoptv8framefactory.o(.text.AnoPTv8SendDevInfo) for AnoPTv8SendDevInfo
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to anoptv8par.o(.bss.parCount) for parCount
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to anoptv8par.o(.bss.pParInfoList) for pParInfoList
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to anoptv8framefactory.o(.text.AnoPTv8SendParVal) for AnoPTv8SendParVal
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to anoptv8framefactory.o(.text.AnoPTv8SendParNum) for AnoPTv8SendParNum
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to anoptv8framefactory.o(.text.AnoPTv8SendParInfo) for AnoPTv8SendParInfo
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to hwinterface.o(.text.AnoPTv8HwParCmdResetParameter) for AnoPTv8HwParCmdResetParameter
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to anoptv8framefactory.o(.text.AnoPTv8SendCheck) for AnoPTv8SendCheck
    anoptv8par.o(.text.AnoPTv8ParFrameAnl) refers to hwinterface.o(.text.AnoPTv8HwParCmdRecvCallback) for AnoPTv8HwParCmdRecvCallback
    anoptv8par.o(.ARM.exidx.text.AnoPTv8ParFrameAnl) refers to anoptv8par.o(.text.AnoPTv8ParFrameAnl) for [Anonymous Symbol]
    anoptv8par.o(.text.AnoPTv8GetParamType) refers to anoptv8par.o(.bss.parCount) for parCount
    anoptv8par.o(.text.AnoPTv8GetParamType) refers to anoptv8par.o(.bss.pParInfoList) for pParInfoList
    anoptv8par.o(.ARM.exidx.text.AnoPTv8GetParamType) refers to anoptv8par.o(.text.AnoPTv8GetParamType) for [Anonymous Symbol]
    anoptv8par.o(.text.anoPTv8ParSetVal) refers to anoptv8par.o(.bss.parCount) for parCount
    anoptv8par.o(.text.anoPTv8ParSetVal) refers to anoptv8par.o(.bss.pParInfoList) for pParInfoList
    anoptv8par.o(.ARM.exidx.text.anoPTv8ParSetVal) refers to anoptv8par.o(.text.anoPTv8ParSetVal) for [Anonymous Symbol]
    anoptv8par.o(.text.AnoPTv8ParGetCount) refers to anoptv8par.o(.bss.parCount) for parCount
    anoptv8par.o(.ARM.exidx.text.AnoPTv8ParGetCount) refers to anoptv8par.o(.text.AnoPTv8ParGetCount) for [Anonymous Symbol]
    anoptv8par.o(.text.AnoPTv8ParRegister) refers to anoptv8par.o(.bss.parCount) for parCount
    anoptv8par.o(.text.AnoPTv8ParRegister) refers to anoptv8par.o(.bss.pParInfoList) for pParInfoList
    anoptv8par.o(.ARM.exidx.text.AnoPTv8ParRegister) refers to anoptv8par.o(.text.AnoPTv8ParRegister) for [Anonymous Symbol]
    anoptv8par.o(.text.AnoPTv8ParGetInfo) refers to anoptv8par.o(.bss.parCount) for parCount
    anoptv8par.o(.text.AnoPTv8ParGetInfo) refers to anoptv8par.o(.bss.pParInfoList) for pParInfoList
    anoptv8par.o(.ARM.exidx.text.AnoPTv8ParGetInfo) refers to anoptv8par.o(.text.AnoPTv8ParGetInfo) for [Anonymous Symbol]
    anoptv8par.o(.text.AnoPTv8ParGetVal) refers to anoptv8par.o(.bss.parCount) for parCount
    anoptv8par.o(.text.AnoPTv8ParGetVal) refers to anoptv8par.o(.bss.pParInfoList) for pParInfoList
    anoptv8par.o(.ARM.exidx.text.AnoPTv8ParGetVal) refers to anoptv8par.o(.text.AnoPTv8ParGetVal) for [Anonymous Symbol]
    anoptv8run.o(.text.AnoPTv8TxRunThread1ms) refers to anoptv8run.o(.bss.AnoPTv8LargeTxBuf) for AnoPTv8LargeTxBuf
    anoptv8run.o(.text.AnoPTv8TxRunThread1ms) refers to hwinterface.o(.text.AnoPTv8HwSendBytes) for AnoPTv8HwSendBytes
    anoptv8run.o(.ARM.exidx.text.AnoPTv8TxRunThread1ms) refers to anoptv8run.o(.text.AnoPTv8TxRunThread1ms) for [Anonymous Symbol]
    anoptv8run.o(.text.AnoPTv8TxLargeBufSend) refers to anoptv8run.o(.bss.AnoPTv8LargeTxBuf) for AnoPTv8LargeTxBuf
    anoptv8run.o(.text.AnoPTv8TxLargeBufSend) refers to hwinterface.o(.text.AnoPTv8HwSendBytes) for AnoPTv8HwSendBytes
    anoptv8run.o(.ARM.exidx.text.AnoPTv8TxLargeBufSend) refers to anoptv8run.o(.text.AnoPTv8TxLargeBufSend) for [Anonymous Symbol]
    anoptv8run.o(.text.AnoPTv8TxMainLoopSingle) refers to anoptv8run.o(.bss.AnoPTv8LargeTxBuf) for AnoPTv8LargeTxBuf
    anoptv8run.o(.text.AnoPTv8TxMainLoopSingle) refers to hwinterface.o(.text.AnoPTv8HwSendBytes) for AnoPTv8HwSendBytes
    anoptv8run.o(.ARM.exidx.text.AnoPTv8TxMainLoopSingle) refers to anoptv8run.o(.text.AnoPTv8TxMainLoopSingle) for [Anonymous Symbol]
    anoptv8run.o(.text.AnoPTv8TxMainLoopAll) refers to anoptv8run.o(.bss.AnoPTv8LargeTxBuf) for AnoPTv8LargeTxBuf
    anoptv8run.o(.text.AnoPTv8TxMainLoopAll) refers to hwinterface.o(.text.AnoPTv8HwSendBytes) for AnoPTv8HwSendBytes
    anoptv8run.o(.ARM.exidx.text.AnoPTv8TxMainLoopAll) refers to anoptv8run.o(.text.AnoPTv8TxMainLoopAll) for [Anonymous Symbol]
    anoptv8run.o(.text.AnoPTv8TxMainLoopBatch) refers to anoptv8run.o(.bss.AnoPTv8LargeTxBuf) for AnoPTv8LargeTxBuf
    anoptv8run.o(.text.AnoPTv8TxMainLoopBatch) refers to hwinterface.o(.text.AnoPTv8HwSendBytes) for AnoPTv8HwSendBytes
    anoptv8run.o(.ARM.exidx.text.AnoPTv8TxMainLoopBatch) refers to anoptv8run.o(.text.AnoPTv8TxMainLoopBatch) for [Anonymous Symbol]
    anoptv8run.o(.text.AnoPTv8TxLargeBufFlush) refers to anoptv8run.o(.bss.AnoPTv8LargeTxBuf) for AnoPTv8LargeTxBuf
    anoptv8run.o(.text.AnoPTv8TxLargeBufFlush) refers to hwinterface.o(.text.AnoPTv8HwSendBytes) for AnoPTv8HwSendBytes
    anoptv8run.o(.ARM.exidx.text.AnoPTv8TxLargeBufFlush) refers to anoptv8run.o(.text.AnoPTv8TxLargeBufFlush) for [Anonymous Symbol]
    anoptv8run.o(.text.AnoPTv8RecvBytes) refers to anoptv8run.o(.bss.recvBuf) for recvBuf
    anoptv8run.o(.text.AnoPTv8RecvBytes) refers to anoptv8run.o(.bss.AnoPTv8CurrentAnlFrame) for AnoPTv8CurrentAnlFrame
    anoptv8run.o(.text.AnoPTv8RecvBytes) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    anoptv8run.o(.text.AnoPTv8RecvBytes) refers to anoptv8cmd.o(.text.AnoPTv8CmdFrameAnl) for AnoPTv8CmdFrameAnl
    anoptv8run.o(.text.AnoPTv8RecvBytes) refers to anoptv8par.o(.text.AnoPTv8ParFrameAnl) for AnoPTv8ParFrameAnl
    anoptv8run.o(.ARM.exidx.text.AnoPTv8RecvBytes) refers to anoptv8run.o(.text.AnoPTv8RecvBytes) for [Anonymous Symbol]
    anoptv8run.o(.text.AnoPTv8RecvOneByte) refers to anoptv8run.o(.text.AnoPTv8RecvBytes) for AnoPTv8RecvBytes
    anoptv8run.o(.ARM.exidx.text.AnoPTv8RecvOneByte) refers to anoptv8run.o(.text.AnoPTv8RecvOneByte) for [Anonymous Symbol]
    hwinterface.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.text.AnoPTv8HwInit) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwInit) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwInit) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.text.AnoPTv8HwInit) refers to motorparams.o(.text.MotorParamsInit) for MotorParamsInit
    hwinterface.o(.text.AnoPTv8HwInit) refers to motorcmd.o(.text.MotorCmdInit) for MotorCmdInit
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwInit) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwInit) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwInit) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwInit) refers to hwinterface.o(.text.AnoPTv8HwInit) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8HwSendFrame) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwSendFrame) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwSendFrame) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendFrame) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendFrame) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendFrame) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendFrame) refers to hwinterface.o(.text.AnoPTv8HwSendFrame) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8HwDataTest) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwDataTest) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwDataTest) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.text.AnoPTv8HwDataTest) refers to sys_timerevent.o(.bss.sys_runtime_ms) for sys_runtime_ms
    hwinterface.o(.text.AnoPTv8HwDataTest) refers to hwinterface.o(.bss.AnoPTv8HwDataTest.last_test_time) for AnoPTv8HwDataTest.last_test_time
    hwinterface.o(.text.AnoPTv8HwDataTest) refers to hwinterface.o(.bss.AnoPTv8HwDataTest.test_count) for AnoPTv8HwDataTest.test_count
    hwinterface.o(.text.AnoPTv8HwDataTest) refers to __2sprintf.o(.text) for __2sprintf
    hwinterface.o(.text.AnoPTv8HwDataTest) refers to anoptv8framefactory.o(.text.AnoPTv8SendStr) for AnoPTv8SendStr
    hwinterface.o(.text.AnoPTv8HwDataTest) refers to anoptv8framefactory.o(.text.AnoPTv8SendDevInfo) for AnoPTv8SendDevInfo
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwDataTest) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwDataTest) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwDataTest) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwDataTest) refers to hwinterface.o(.text.AnoPTv8HwDataTest) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8HwSendBytes) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwSendBytes) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwSendBytes) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.text.AnoPTv8HwSendBytes) refers to usb_app.o(.text.usb_send_data) for usb_send_data
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendBytes) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendBytes) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendBytes) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendBytes) refers to hwinterface.o(.text.AnoPTv8HwSendBytes) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8HwRecvByte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwRecvByte) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwRecvByte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.text.AnoPTv8HwRecvByte) refers to anoptv8run.o(.text.AnoPTv8RecvOneByte) for AnoPTv8RecvOneByte
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvByte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvByte) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvByte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvByte) refers to hwinterface.o(.text.AnoPTv8HwRecvByte) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8HwRecvBytes) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwRecvBytes) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwRecvBytes) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.text.AnoPTv8HwRecvBytes) refers to anoptv8run.o(.text.AnoPTv8RecvBytes) for AnoPTv8RecvBytes
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvBytes) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvBytes) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvBytes) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvBytes) refers to hwinterface.o(.text.AnoPTv8HwRecvBytes) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8HwTrigger1ms) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwTrigger1ms) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwTrigger1ms) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.text.AnoPTv8HwTrigger1ms) refers to anoptv8run.o(.text.AnoPTv8TxLargeBufSend) for AnoPTv8TxLargeBufSend
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwTrigger1ms) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwTrigger1ms) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwTrigger1ms) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwTrigger1ms) refers to hwinterface.o(.text.AnoPTv8HwTrigger1ms) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8HwParValRecvCallback) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwParValRecvCallback) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwParValRecvCallback) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParValRecvCallback) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParValRecvCallback) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParValRecvCallback) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParValRecvCallback) refers to hwinterface.o(.text.AnoPTv8HwParValRecvCallback) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8HwParCmdRecvCallback) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwParCmdRecvCallback) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwParCmdRecvCallback) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdRecvCallback) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdRecvCallback) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdRecvCallback) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdRecvCallback) refers to hwinterface.o(.text.AnoPTv8HwParCmdRecvCallback) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8HwParCmdResetParameter) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8HwParCmdResetParameter) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8HwParCmdResetParameter) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdResetParameter) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdResetParameter) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdResetParameter) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdResetParameter) refers to hwinterface.o(.text.AnoPTv8HwParCmdResetParameter) for [Anonymous Symbol]
    hwinterface.o(.text.Usb_printf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.Usb_printf) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.Usb_printf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.text.Usb_printf) refers to vsnprintf.o(.text) for vsnprintf
    hwinterface.o(.text.Usb_printf) refers to anoptv8framefactory.o(.text.AnoPTv8SendStr) for AnoPTv8SendStr
    hwinterface.o(.ARM.exidx.text.Usb_printf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.Usb_printf) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.Usb_printf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.Usb_printf) refers to hwinterface.o(.text.Usb_printf) for [Anonymous Symbol]
    hwinterface.o(.text.AnoPTv8CalFrameCheck01) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.AnoPTv8CalFrameCheck01) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.AnoPTv8CalFrameCheck01) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8CalFrameCheck01) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.AnoPTv8CalFrameCheck01) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.AnoPTv8CalFrameCheck01) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.AnoPTv8CalFrameCheck01) refers to hwinterface.o(.text.AnoPTv8CalFrameCheck01) for [Anonymous Symbol]
    hwinterface.o(.text.Usb_printf_direct) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.text.Usb_printf_direct) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.text.Usb_printf_direct) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.text.Usb_printf_direct) refers to vsnprintf.o(.text) for vsnprintf
    hwinterface.o(.text.Usb_printf_direct) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    hwinterface.o(.ARM.exidx.text.Usb_printf_direct) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.ARM.exidx.text.Usb_printf_direct) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.ARM.exidx.text.Usb_printf_direct) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.ARM.exidx.text.Usb_printf_direct) refers to hwinterface.o(.text.Usb_printf_direct) for [Anonymous Symbol]
    hwinterface.o(.bss.AnoPTv8HwDataTest.test_count) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.bss.AnoPTv8HwDataTest.test_count) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.bss.AnoPTv8HwDataTest.test_count) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    hwinterface.o(.bss.AnoPTv8HwDataTest.last_test_time) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwinterface.o(.bss.AnoPTv8HwDataTest.last_test_time) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    hwinterface.o(.bss.AnoPTv8HwDataTest.last_test_time) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    motorcmd.o(.text.SetFrameSendEnable) refers to motorcmd.o(.bss.gFrameFlags) for gFrameFlags
    motorcmd.o(.ARM.exidx.text.SetFrameSendEnable) refers to motorcmd.o(.text.SetFrameSendEnable) for [Anonymous Symbol]
    motorcmd.o(.text.GetFrameSendEnable) refers to motorcmd.o(.bss.gFrameFlags) for gFrameFlags
    motorcmd.o(.ARM.exidx.text.GetFrameSendEnable) refers to motorcmd.o(.text.GetFrameSendEnable) for [Anonymous Symbol]
    motorcmd.o(.text.MotorCmdInit) refers to motorcmd.o(.rodata._pCmdInfoMotorStart) for _pCmdInfoMotorStart
    motorcmd.o(.text.MotorCmdInit) refers to anoptv8cmd.o(.text.AnoPTv8CmdRegister) for AnoPTv8CmdRegister
    motorcmd.o(.text.MotorCmdInit) refers to motorcmd.o(.rodata._pCmdInfoMotorStop) for _pCmdInfoMotorStop
    motorcmd.o(.text.MotorCmdInit) refers to motorcmd.o(.rodata._pCmdInfoMotorEStop) for _pCmdInfoMotorEStop
    motorcmd.o(.text.MotorCmdInit) refers to motorcmd.o(.rodata._pCmdInfoMotorSelfTest) for _pCmdInfoMotorSelfTest
    motorcmd.o(.text.MotorCmdInit) refers to motorcmd.o(.rodata._pCmdInfoMotorGetFault) for _pCmdInfoMotorGetFault
    motorcmd.o(.text.MotorCmdInit) refers to motorcmd.o(.rodata._pCmdInfoMotorClearFault) for _pCmdInfoMotorClearFault
    motorcmd.o(.text.MotorCmdInit) refers to motorcmd.o(.rodata._pCmdInfoWaveCtrl) for _pCmdInfoWaveCtrl
    motorcmd.o(.text.MotorCmdInit) refers to motorcmd.o(.rodata._pCmdInfoWaveStopAll) for _pCmdInfoWaveStopAll
    motorcmd.o(.ARM.exidx.text.MotorCmdInit) refers to motorcmd.o(.text.MotorCmdInit) for [Anonymous Symbol]
    motorcmd.o(.text.AnoPTv8_Init) refers to motorcmd.o(.rodata._pCmdInfoMotorStart) for _pCmdInfoMotorStart
    motorcmd.o(.text.AnoPTv8_Init) refers to anoptv8cmd.o(.text.AnoPTv8CmdRegister) for AnoPTv8CmdRegister
    motorcmd.o(.text.AnoPTv8_Init) refers to motorcmd.o(.rodata._pCmdInfoMotorStop) for _pCmdInfoMotorStop
    motorcmd.o(.text.AnoPTv8_Init) refers to motorcmd.o(.rodata._pCmdInfoMotorEStop) for _pCmdInfoMotorEStop
    motorcmd.o(.text.AnoPTv8_Init) refers to motorcmd.o(.rodata._pCmdInfoMotorSelfTest) for _pCmdInfoMotorSelfTest
    motorcmd.o(.text.AnoPTv8_Init) refers to motorcmd.o(.rodata._pCmdInfoMotorGetFault) for _pCmdInfoMotorGetFault
    motorcmd.o(.text.AnoPTv8_Init) refers to motorcmd.o(.rodata._pCmdInfoMotorClearFault) for _pCmdInfoMotorClearFault
    motorcmd.o(.text.AnoPTv8_Init) refers to motorcmd.o(.rodata._pCmdInfoWaveCtrl) for _pCmdInfoWaveCtrl
    motorcmd.o(.text.AnoPTv8_Init) refers to motorcmd.o(.rodata._pCmdInfoWaveStopAll) for _pCmdInfoWaveStopAll
    motorcmd.o(.ARM.exidx.text.AnoPTv8_Init) refers to motorcmd.o(.text.AnoPTv8_Init) for [Anonymous Symbol]
    motorcmd.o(.ARM.exidx.text.Print_System_Status_Distributed) refers to motorcmd.o(.text.Print_System_Status_Distributed) for [Anonymous Symbol]
    motorcmd.o(.text.AnoPTv8CmdFun_MotorStart) refers to sysfsm.o(.bss.g_SystemStatus) for g_SystemStatus
    motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorStart) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorStart) for [Anonymous Symbol]
    motorcmd.o(.text.AnoPTv8CmdFun_MotorStop) refers to sysfsm.o(.bss.g_SystemStatus) for g_SystemStatus
    motorcmd.o(.text.AnoPTv8CmdFun_MotorStop) refers to motordata.o(.data.gMotorData) for gMotorData
    motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorStop) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorStop) for [Anonymous Symbol]
    motorcmd.o(.text.AnoPTv8CmdFun_MotorReset) refers to sysfsm.o(.bss.g_SystemStatus) for g_SystemStatus
    motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorReset) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorReset) for [Anonymous Symbol]
    motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorSelfTest) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorSelfTest) for [Anonymous Symbol]
    motorcmd.o(.text.AnoPTv8CmdFun_MotorGetFault) refers to sysfsm.o(.bss.g_SystemStatus) for g_SystemStatus
    motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorGetFault) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorGetFault) for [Anonymous Symbol]
    motorcmd.o(.text.AnoPTv8CmdFun_MotorClearFault) refers to sysfsm.o(.bss.g_SystemStatus) for g_SystemStatus
    motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorClearFault) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorClearFault) for [Anonymous Symbol]
    motorcmd.o(.text.AnoPTv8CmdFun_WaveCtrl) refers to motorcmd.o(.bss.gFrameFlags) for gFrameFlags
    motorcmd.o(.text.AnoPTv8CmdFun_WaveCtrl) refers to anoptv8run.o(.bss.AnoPTv8CurrentAnlFrame) for AnoPTv8CurrentAnlFrame
    motorcmd.o(.text.AnoPTv8CmdFun_WaveCtrl) refers to anoptv8framefactory.o(.text.AnoPTv8SendStr) for AnoPTv8SendStr
    motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_WaveCtrl) refers to motorcmd.o(.text.AnoPTv8CmdFun_WaveCtrl) for [Anonymous Symbol]
    motorcmd.o(.text.AnoPTv8CmdFun_WaveStopAll) refers to motorcmd.o(.bss.gFrameFlags) for gFrameFlags
    motorcmd.o(.text.AnoPTv8CmdFun_WaveStopAll) refers to anoptv8framefactory.o(.text.AnoPTv8SendStr) for AnoPTv8SendStr
    motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_WaveStopAll) refers to motorcmd.o(.text.AnoPTv8CmdFun_WaveStopAll) for [Anonymous Symbol]
    motorcmd.o(.rodata._pCmdInfoMotorStart) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorStart) for AnoPTv8CmdFun_MotorStart
    motorcmd.o(.rodata._pCmdInfoMotorStop) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorStop) for AnoPTv8CmdFun_MotorStop
    motorcmd.o(.rodata._pCmdInfoMotorEStop) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorReset) for AnoPTv8CmdFun_MotorReset
    motorcmd.o(.rodata._pCmdInfoMotorSelfTest) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorSelfTest) for AnoPTv8CmdFun_MotorSelfTest
    motorcmd.o(.rodata._pCmdInfoMotorGetFault) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorGetFault) for AnoPTv8CmdFun_MotorGetFault
    motorcmd.o(.rodata._pCmdInfoMotorClearFault) refers to motorcmd.o(.text.AnoPTv8CmdFun_MotorClearFault) for AnoPTv8CmdFun_MotorClearFault
    motorcmd.o(.rodata._pCmdInfoWaveCtrl) refers to motorcmd.o(.text.AnoPTv8CmdFun_WaveCtrl) for AnoPTv8CmdFun_WaveCtrl
    motorcmd.o(.rodata._pCmdInfoWaveStopAll) refers to motorcmd.o(.text.AnoPTv8CmdFun_WaveStopAll) for AnoPTv8CmdFun_WaveStopAll
    motordata.o(.text.MotorDataSendFrame) refers to motordata.o(.data.gMotorData) for gMotorData
    motordata.o(.text.MotorDataSendFrame) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    motordata.o(.ARM.exidx.text.MotorDataSendFrame) refers to motordata.o(.text.MotorDataSendFrame) for [Anonymous Symbol]
    motordata.o(.ARM.exidx.text.UpdateF5ADCData) refers to motordata.o(.text.UpdateF5ADCData) for [Anonymous Symbol]
    motordata.o(.text.UpdateF2SensorData) refers to motorcmd.o(.bss.gFrameFlags) for gFrameFlags
    motordata.o(.text.UpdateF2SensorData) refers to motordata.o(.data.gMotorData) for gMotorData
    motordata.o(.text.UpdateF2SensorData) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    motordata.o(.text.UpdateF2SensorData) refers to adc_pmsm.o(.bss.g_adc_current_data) for g_adc_current_data
    motordata.o(.text.UpdateF2SensorData) refers to protection_monitor.o(.text.Protection_Monitor_GetValue) for Protection_Monitor_GetValue
    motordata.o(.text.UpdateF2SensorData) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    motordata.o(.ARM.exidx.text.UpdateF2SensorData) refers to motordata.o(.text.UpdateF2SensorData) for [Anonymous Symbol]
    motordata.o(.text.UpdateF3TempData) refers to motorcmd.o(.bss.gFrameFlags) for gFrameFlags
    motordata.o(.text.UpdateF3TempData) refers to motordata.o(.data.gMotorData) for gMotorData
    motordata.o(.text.UpdateF3TempData) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    motordata.o(.ARM.exidx.text.UpdateF3TempData) refers to motordata.o(.text.UpdateF3TempData) for [Anonymous Symbol]
    motordata.o(.text.UpdateF1FocData) refers to motorcmd.o(.bss.gFrameFlags) for gFrameFlags
    motordata.o(.text.UpdateF1FocData) refers to motordata.o(.data.gMotorData) for gMotorData
    motordata.o(.text.UpdateF1FocData) refers to anoptv8framefactory.o(.text.AnoPTv8SendBuf) for AnoPTv8SendBuf
    motordata.o(.ARM.exidx.text.UpdateF1FocData) refers to motordata.o(.text.UpdateF1FocData) for [Anonymous Symbol]
    motordata.o(.text.MotorDataTest) refers to sys_timerevent.o(.bss.sys_runtime_ms) for sys_runtime_ms
    motordata.o(.text.MotorDataTest) refers to motordata.o(.bss.MotorDataTest.last_test_time) for MotorDataTest.last_test_time
    motordata.o(.text.MotorDataTest) refers to motorcmd.o(.bss.gFrameFlags) for gFrameFlags
    motordata.o(.text.MotorDataTest) refers to motordata.o(.text.UpdateF2SensorData) for UpdateF2SensorData
    motordata.o(.ARM.exidx.text.MotorDataTest) refers to motordata.o(.text.MotorDataTest) for [Anonymous Symbol]
    motorparams.o(.text.MotorParamsInit) refers to motorparams.o(.bss.MotorParamsInit._alreadyInit) for MotorParamsInit._alreadyInit
    motorparams.o(.text.MotorParamsInit) refers to motorparams.o(.rodata._motorParInfoList) for _motorParInfoList
    motorparams.o(.text.MotorParamsInit) refers to anoptv8par.o(.text.AnoPTv8ParRegister) for AnoPTv8ParRegister
    motorparams.o(.ARM.exidx.text.MotorParamsInit) refers to motorparams.o(.text.MotorParamsInit) for [Anonymous Symbol]
    motorparams.o(.rodata._motorParInfoList) refers to motorparams.o(.data.gMotorParams) for gMotorParams
    ad2s1212_spi.o(.text.AD2S2S1210_ModeCfg) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    ad2s1212_spi.o(.text.AD2S2S1210_ModeCfg) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    ad2s1212_spi.o(.ARM.exidx.text.AD2S2S1210_ModeCfg) refers to ad2s1212_spi.o(.text.AD2S2S1210_ModeCfg) for [Anonymous Symbol]
    ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_Delay) refers to ad2s1212_spi.o(.text.AD2S1210_Delay) for [Anonymous Symbol]
    ad2s1212_spi.o(.text.AD2S1210_Init) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    ad2s1212_spi.o(.text.AD2S1210_Init) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    ad2s1212_spi.o(.text.AD2S1210_Init) refers to ad2s1212_spi.o(.text.AD2S1210_WRITE) for AD2S1210_WRITE
    ad2s1212_spi.o(.text.AD2S1210_Init) refers to ad2s1212_spi.o(.text.AD2S1210_READ) for AD2S1210_READ
    ad2s1212_spi.o(.text.AD2S1210_Init) refers to ad2s1212_spi.o(.bss.readValue) for readValue
    ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_Init) refers to ad2s1212_spi.o(.text.AD2S1210_Init) for [Anonymous Symbol]
    ad2s1212_spi.o(.text.AD2S1210_WRITE) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    ad2s1212_spi.o(.text.AD2S1210_WRITE) refers to at32a403a_spi.o(.text.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ad2s1212_spi.o(.text.AD2S1210_WRITE) refers to at32a403a_spi.o(.text.spi_i2s_flag_get) for spi_i2s_flag_get
    ad2s1212_spi.o(.text.AD2S1210_WRITE) refers to at32a403a_spi.o(.text.spi_i2s_data_receive) for spi_i2s_data_receive
    ad2s1212_spi.o(.text.AD2S1210_WRITE) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_WRITE) refers to ad2s1212_spi.o(.text.AD2S1210_WRITE) for [Anonymous Symbol]
    ad2s1212_spi.o(.text.AD2S1210_READ) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    ad2s1212_spi.o(.text.AD2S1210_READ) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    ad2s1212_spi.o(.text.AD2S1210_READ) refers to at32a403a_spi.o(.text.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ad2s1212_spi.o(.text.AD2S1210_READ) refers to at32a403a_spi.o(.text.spi_i2s_flag_get) for spi_i2s_flag_get
    ad2s1212_spi.o(.text.AD2S1210_READ) refers to at32a403a_spi.o(.text.spi_i2s_data_receive) for spi_i2s_data_receive
    ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_READ) refers to ad2s1212_spi.o(.text.AD2S1210_READ) for [Anonymous Symbol]
    ad2s1212_spi.o(.text.AD2S1210_REFAULT) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    ad2s1212_spi.o(.text.AD2S1210_REFAULT) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    ad2s1212_spi.o(.text.AD2S1210_REFAULT) refers to ad2s1212_spi.o(.text.AD2S1210_READ) for AD2S1210_READ
    ad2s1212_spi.o(.text.AD2S1210_REFAULT) refers to ad2s1212_spi.o(.bss.readValue) for readValue
    ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_REFAULT) refers to ad2s1212_spi.o(.text.AD2S1210_REFAULT) for [Anonymous Symbol]
    ad2s1212_spi.o(.text.AD2S1210_RESET) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    ad2s1212_spi.o(.text.AD2S1210_RESET) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_RESET) refers to ad2s1212_spi.o(.text.AD2S1210_RESET) for [Anonymous Symbol]
    ad2s1212_spi.o(.text.AD2S1210_CommRead) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    ad2s1212_spi.o(.text.AD2S1210_CommRead) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_CommRead) refers to ad2s1212_spi.o(.text.AD2S1210_CommRead) for [Anonymous Symbol]
    ad2s1212_spi.o(.text.AD2S1210_ReadVelocity) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    ad2s1212_spi.o(.text.AD2S1210_ReadVelocity) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_ReadVelocity) refers to ad2s1212_spi.o(.text.AD2S1210_ReadVelocity) for [Anonymous Symbol]
    ad2s1212_spi.o(.text.AD2S1210_GetRotationSpeed) refers to ad2s1212_spi.o(.text.AD2S1210_ReadVelocity) for AD2S1210_ReadVelocity
    ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_GetRotationSpeed) refers to ad2s1212_spi.o(.text.AD2S1210_GetRotationSpeed) for [Anonymous Symbol]
    enc_speed.o(.text.InitKalmanFilter) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.text.InitKalmanFilter) refers to arm_mat_init_f32.o(.text.arm_mat_init_f32) for arm_mat_init_f32
    enc_speed.o(.ARM.exidx.text.InitKalmanFilter) refers to enc_speed.o(.text.InitKalmanFilter) for [Anonymous Symbol]
    enc_speed.o(.text.SetKalmanFilterParams) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.SetKalmanFilterParams) refers to enc_speed.o(.text.SetKalmanFilterParams) for [Anonymous Symbol]
    enc_speed.o(.text.EncoderSpeed_Init) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.text.EncoderSpeed_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    enc_speed.o(.text.EncoderSpeed_Init) refers to enc_speed.o(.bss.speed_buffer) for speed_buffer
    enc_speed.o(.text.EncoderSpeed_Init) refers to enc_speed.o(.text.InitKalmanFilter) for InitKalmanFilter
    enc_speed.o(.text.EncoderSpeed_Init) refers to ad2s1212_spi.o(.text.AD2S1210_CommRead) for AD2S1210_CommRead
    enc_speed.o(.ARM.exidx.text.EncoderSpeed_Init) refers to enc_speed.o(.text.EncoderSpeed_Init) for [Anonymous Symbol]
    enc_speed.o(.text.SetLowPassFilterCoeff) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.SetLowPassFilterCoeff) refers to enc_speed.o(.text.SetLowPassFilterCoeff) for [Anonymous Symbol]
    enc_speed.o(.text.SetMovingAvgFilterSize) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.text.SetMovingAvgFilterSize) refers to enc_speed.o(.bss.speed_buffer) for speed_buffer
    enc_speed.o(.text.SetMovingAvgFilterSize) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    enc_speed.o(.ARM.exidx.text.SetMovingAvgFilterSize) refers to enc_speed.o(.text.SetMovingAvgFilterSize) for [Anonymous Symbol]
    enc_speed.o(.text.InitFhanFilter) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.InitFhanFilter) refers to enc_speed.o(.text.InitFhanFilter) for [Anonymous Symbol]
    enc_speed.o(.text.InitSpeedPI) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.InitSpeedPI) refers to enc_speed.o(.text.InitSpeedPI) for [Anonymous Symbol]
    enc_speed.o(.text.CalculateTimerCountFromSPI) refers to ad2s1212_spi.o(.text.AD2S1210_CommRead) for AD2S1210_CommRead
    enc_speed.o(.text.CalculateTimerCountFromSPI) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.CalculateTimerCountFromSPI) refers to enc_speed.o(.text.CalculateTimerCountFromSPI) for [Anonymous Symbol]
    enc_speed.o(.text.SpeedLoopSystem_Init) refers to enc_speed.o(.text.EncoderSpeed_Init) for EncoderSpeed_Init
    enc_speed.o(.text.SpeedLoopSystem_Init) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.text.SpeedLoopSystem_Init) refers to enc_speed.o(.bss.speed_buffer) for speed_buffer
    enc_speed.o(.ARM.exidx.text.SpeedLoopSystem_Init) refers to enc_speed.o(.text.SpeedLoopSystem_Init) for [Anonymous Symbol]
    enc_speed.o(.text.SetSpeedFilterType) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.SetSpeedFilterType) refers to enc_speed.o(.text.SetSpeedFilterType) for [Anonymous Symbol]
    enc_speed.o(.text.EnableFhanFilter) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.EnableFhanFilter) refers to enc_speed.o(.text.EnableFhanFilter) for [Anonymous Symbol]
    enc_speed.o(.text.EnableSpeedPI) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.EnableSpeedPI) refers to enc_speed.o(.text.EnableSpeedPI) for [Anonymous Symbol]
    enc_speed.o(.text.EnablePIFeedforward) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.EnablePIFeedforward) refers to enc_speed.o(.text.EnablePIFeedforward) for [Anonymous Symbol]
    enc_speed.o(.text.EncoderSpeed_Reset) refers to at32a403a_tmr.o(.text.tmr_counter_enable) for tmr_counter_enable
    enc_speed.o(.text.EncoderSpeed_Reset) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.text.EncoderSpeed_Reset) refers to enc_speed.o(.text.EncoderSpeed_Init) for EncoderSpeed_Init
    enc_speed.o(.ARM.exidx.text.EncoderSpeed_Reset) refers to enc_speed.o(.text.EncoderSpeed_Reset) for [Anonymous Symbol]
    enc_speed.o(.text.ZPulseDetectedCallback) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.ZPulseDetectedCallback) refers to enc_speed.o(.text.ZPulseDetectedCallback) for [Anonymous Symbol]
    enc_speed.o(.text.GetZPulseCounter) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.GetZPulseCounter) refers to enc_speed.o(.text.GetZPulseCounter) for [Anonymous Symbol]
    enc_speed.o(.text.ResetZPulseCounter) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.ResetZPulseCounter) refers to enc_speed.o(.text.ResetZPulseCounter) for [Anonymous Symbol]
    enc_speed.o(.text.ResetOverflowCounter) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.ResetOverflowCounter) refers to enc_speed.o(.text.ResetOverflowCounter) for [Anonymous Symbol]
    enc_speed.o(.text.GetElectricalAngle_ENC) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.GetElectricalAngle_ENC) refers to enc_speed.o(.text.GetElectricalAngle_ENC) for [Anonymous Symbol]
    enc_speed.o(.text.GetSignedSpeed_RPS) refers to enc_speed.o(.bss.signed_speed_rps) for signed_speed_rps
    enc_speed.o(.ARM.exidx.text.GetSignedSpeed_RPS) refers to enc_speed.o(.text.GetSignedSpeed_RPS) for [Anonymous Symbol]
    enc_speed.o(.text.GetSignedSpeed_RPM) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.GetSignedSpeed_RPM) refers to enc_speed.o(.text.GetSignedSpeed_RPM) for [Anonymous Symbol]
    enc_speed.o(.text.CalculateSpeed_4KHz) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.text.CalculateSpeed_4KHz) refers to enc_speed.o(.bss.signed_speed_rps) for signed_speed_rps
    enc_speed.o(.text.CalculateSpeed_4KHz) refers to enc_speed.o(.bss.AdaptiveKalmanParams.last_state) for AdaptiveKalmanParams.last_state
    enc_speed.o(.text.CalculateSpeed_4KHz) refers to arm_mat_init_f32.o(.text.arm_mat_init_f32) for arm_mat_init_f32
    enc_speed.o(.text.CalculateSpeed_4KHz) refers to arm_mat_mult_f32.o(.text.arm_mat_mult_f32) for arm_mat_mult_f32
    enc_speed.o(.text.CalculateSpeed_4KHz) refers to arm_mat_add_f32.o(.text.arm_mat_add_f32) for arm_mat_add_f32
    enc_speed.o(.text.CalculateSpeed_4KHz) refers to arm_copy_f32.o(.text.arm_copy_f32) for arm_copy_f32
    enc_speed.o(.ARM.exidx.text.CalculateSpeed_4KHz) refers to enc_speed.o(.text.CalculateSpeed_4KHz) for [Anonymous Symbol]
    enc_speed.o(.text.SetFhanParams) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.SetFhanParams) refers to enc_speed.o(.text.SetFhanParams) for [Anonymous Symbol]
    enc_speed.o(.text.IsFhanEnabled) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.IsFhanEnabled) refers to enc_speed.o(.text.IsFhanEnabled) for [Anonymous Symbol]
    enc_speed.o(.text.ProcessFhan) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.ProcessFhan) refers to enc_speed.o(.text.ProcessFhan) for [Anonymous Symbol]
    enc_speed.o(.text.GetFhanSpeed_RPS) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.GetFhanSpeed_RPS) refers to enc_speed.o(.text.GetFhanSpeed_RPS) for [Anonymous Symbol]
    enc_speed.o(.text.GetFhanAcceleration_RPS) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.GetFhanAcceleration_RPS) refers to enc_speed.o(.text.GetFhanAcceleration_RPS) for [Anonymous Symbol]
    enc_speed.o(.text.SetSpeedPIParams) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.SetSpeedPIParams) refers to enc_speed.o(.text.SetSpeedPIParams) for [Anonymous Symbol]
    enc_speed.o(.text.ResetSpeedPIIntegral) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.ResetSpeedPIIntegral) refers to enc_speed.o(.text.ResetSpeedPIIntegral) for [Anonymous Symbol]
    enc_speed.o(.text.IsSpeedPIEnabled) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.IsSpeedPIEnabled) refers to enc_speed.o(.text.IsSpeedPIEnabled) for [Anonymous Symbol]
    enc_speed.o(.text.GetSpeedPIOutput) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.GetSpeedPIOutput) refers to enc_speed.o(.text.GetSpeedPIOutput) for [Anonymous Symbol]
    enc_speed.o(.text.GetCurrentCommandSafe) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.GetCurrentCommandSafe) refers to enc_speed.o(.text.GetCurrentCommandSafe) for [Anonymous Symbol]
    enc_speed.o(.text.GetCurrentCommandValue) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.GetCurrentCommandValue) refers to enc_speed.o(.text.GetCurrentCommandValue) for [Anonymous Symbol]
    enc_speed.o(.text.IsCurrentCommandUpdated) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.IsCurrentCommandUpdated) refers to enc_speed.o(.text.IsCurrentCommandUpdated) for [Anonymous Symbol]
    enc_speed.o(.text.ClearCurrentCommandFlag) refers to enc_speed.o(.bss.g_encoder) for g_encoder
    enc_speed.o(.ARM.exidx.text.ClearCurrentCommandFlag) refers to enc_speed.o(.text.ClearCurrentCommandFlag) for [Anonymous Symbol]
    enc_speed.o(.text.UpdateF4CustomDataWithTarget) refers to motordata.o(.text.MotorDataSendFrame) for MotorDataSendFrame
    enc_speed.o(.ARM.exidx.text.UpdateF4CustomDataWithTarget) refers to enc_speed.o(.text.UpdateF4CustomDataWithTarget) for [Anonymous Symbol]
    enc_speed.o(.text.SimulateABZEncoder) refers to enc_speed.o(.bss.SimulateABZEncoder.current_sim_speed) for SimulateABZEncoder.current_sim_speed
    enc_speed.o(.text.SimulateABZEncoder) refers to enc_speed.o(.bss.SimulateABZEncoder.harmonic_phase_accumulator) for SimulateABZEncoder.harmonic_phase_accumulator
    enc_speed.o(.text.SimulateABZEncoder) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    enc_speed.o(.text.SimulateABZEncoder) refers to arm_sin_f32.o(.text.arm_sin_f32) for arm_sin_f32
    enc_speed.o(.text.SimulateABZEncoder) refers to enc_speed.o(.bss.SimulateABZEncoder.accumulated_revolutions) for SimulateABZEncoder.accumulated_revolutions
    enc_speed.o(.ARM.exidx.text.SimulateABZEncoder) refers to enc_speed.o(.text.SimulateABZEncoder) for [Anonymous Symbol]
    adc_pmsm.o(.text.ADC_FilterManager_Init) refers to adc_pmsm.o(.bss.g_adc_filter_manager) for g_adc_filter_manager
    adc_pmsm.o(.text.ADC_FilterManager_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc_pmsm.o(.ARM.exidx.text.ADC_FilterManager_Init) refers to adc_pmsm.o(.text.ADC_FilterManager_Init) for [Anonymous Symbol]
    adc_pmsm.o(.text.ADC_FilterManager_Reset) refers to adc_pmsm.o(.bss.g_adc_filter_manager) for g_adc_filter_manager
    adc_pmsm.o(.ARM.exidx.text.ADC_FilterManager_Reset) refers to adc_pmsm.o(.text.ADC_FilterManager_Reset) for [Anonymous Symbol]
    adc_pmsm.o(.text.ADC_PMSM_Init) refers to adc_pmsm.o(.bss.g_adc_current_data) for g_adc_current_data
    adc_pmsm.o(.text.ADC_PMSM_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc_pmsm.o(.text.ADC_PMSM_Init) refers to adc_pmsm.o(.bss.g_adc_filter_manager) for g_adc_filter_manager
    adc_pmsm.o(.text.ADC_PMSM_Init) refers to adc_pmsm.o(.text.ADC_PMSM_CalibrateOffset) for ADC_PMSM_CalibrateOffset
    adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_Init) refers to adc_pmsm.o(.text.ADC_PMSM_Init) for [Anonymous Symbol]
    adc_pmsm.o(.text.ADC_PMSM_CalibrateOffset) refers to at32a403a_adc.o(.text.adc_preempt_conversion_trigger_set) for adc_preempt_conversion_trigger_set
    adc_pmsm.o(.text.ADC_PMSM_CalibrateOffset) refers to at32a403a_adc.o(.text.adc_preempt_software_trigger_enable) for adc_preempt_software_trigger_enable
    adc_pmsm.o(.text.ADC_PMSM_CalibrateOffset) refers to at32a403a_adc.o(.text.adc_flag_get) for adc_flag_get
    adc_pmsm.o(.text.ADC_PMSM_CalibrateOffset) refers to at32a403a_adc.o(.text.adc_flag_clear) for adc_flag_clear
    adc_pmsm.o(.text.ADC_PMSM_CalibrateOffset) refers to adc_pmsm.o(.bss.g_adc_current_data) for g_adc_current_data
    adc_pmsm.o(.text.ADC_PMSM_CalibrateOffset) refers to adc_pmsm.o(.bss.calibration_completed) for calibration_completed
    adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_CalibrateOffset) refers to adc_pmsm.o(.text.ADC_PMSM_CalibrateOffset) for [Anonymous Symbol]
    adc_pmsm.o(.text.ADC_PMSM_IsCalibrationCompleted) refers to adc_pmsm.o(.bss.calibration_completed) for calibration_completed
    adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_IsCalibrationCompleted) refers to adc_pmsm.o(.text.ADC_PMSM_IsCalibrationCompleted) for [Anonymous Symbol]
    adc_pmsm.o(.text.ADC_PMSM_ProcessCurrents) refers to adc_pmsm.o(.bss.g_adc_current_data) for g_adc_current_data
    adc_pmsm.o(.text.ADC_PMSM_ProcessCurrents) refers to adc_pmsm.o(.bss.g_adc_filter_manager) for g_adc_filter_manager
    adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_ProcessCurrents) refers to adc_pmsm.o(.text.ADC_PMSM_ProcessCurrents) for [Anonymous Symbol]
    adc_pmsm.o(.text.ADC_PMSM_ProcessRectifiedAverage) refers to adc_pmsm.o(.bss.g_adc_current_data) for g_adc_current_data
    adc_pmsm.o(.text.ADC_PMSM_ProcessRectifiedAverage) refers to adc_pmsm.o(.bss.g_adc_filter_manager) for g_adc_filter_manager
    adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_ProcessRectifiedAverage) refers to adc_pmsm.o(.text.ADC_PMSM_ProcessRectifiedAverage) for [Anonymous Symbol]
    pt1000_table.o(.text.PT1000_ADC_to_Temperature) refers to pt1000_table.o(.rodata.pt1000_adc_table) for pt1000_adc_table
    pt1000_table.o(.ARM.exidx.text.PT1000_ADC_to_Temperature) refers to pt1000_table.o(.text.PT1000_ADC_to_Temperature) for [Anonymous Symbol]
    pt1000_table.o(.text.PT1000_Temperature_to_ADC) refers to pt1000_table.o(.rodata.pt1000_adc_table) for pt1000_adc_table
    pt1000_table.o(.ARM.exidx.text.PT1000_Temperature_to_ADC) refers to pt1000_table.o(.text.PT1000_Temperature_to_ADC) for [Anonymous Symbol]
    sensor_proc.o(.text.sensor_proc_init) refers to sensor_proc.o(.bss.adc_dma_buffer) for adc_dma_buffer
    sensor_proc.o(.text.sensor_proc_init) refers to sensor_proc.o(.rodata.sensor_configs) for sensor_configs
    sensor_proc.o(.text.sensor_proc_init) refers to sensor_proc.o(.bss.sensor_states) for sensor_states
    sensor_proc.o(.text.sensor_proc_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    sensor_proc.o(.text.sensor_proc_init) refers to sensor_proc.o(.bss.total_error_count) for total_error_count
    sensor_proc.o(.text.sensor_proc_init) refers to sensor_proc.o(.bss.dma_data_ready) for dma_data_ready
    sensor_proc.o(.text.sensor_proc_init) refers to sensor_proc.o(.bss.module_initialized) for module_initialized
    sensor_proc.o(.ARM.exidx.text.sensor_proc_init) refers to sensor_proc.o(.text.sensor_proc_init) for [Anonymous Symbol]
    sensor_proc.o(.text.sensor_proc_task) refers to sensor_proc.o(.bss.module_initialized) for module_initialized
    sensor_proc.o(.text.sensor_proc_task) refers to sensor_proc.o(.bss.adc_dma_buffer) for adc_dma_buffer
    sensor_proc.o(.text.sensor_proc_task) refers to sensor_proc.o(.bss.dma_data_ready) for dma_data_ready
    sensor_proc.o(.text.sensor_proc_task) refers to sensor_proc.o(.rodata.sensor_configs) for sensor_configs
    sensor_proc.o(.text.sensor_proc_task) refers to sensor_proc.o(.bss.sensor_states) for sensor_states
    sensor_proc.o(.text.sensor_proc_task) refers to sensor_proc.o(.bss.total_error_count) for total_error_count
    sensor_proc.o(.text.sensor_proc_task) refers to pt1000_table.o(.text.PT1000_ADC_to_Temperature) for PT1000_ADC_to_Temperature
    sensor_proc.o(.text.sensor_proc_task) refers to motordata.o(.text.UpdateF2SensorData) for UpdateF2SensorData
    sensor_proc.o(.ARM.exidx.text.sensor_proc_task) refers to sensor_proc.o(.text.sensor_proc_task) for [Anonymous Symbol]
    sensor_proc.o(.text.sensor_get_result) refers to sensor_proc.o(.bss.sensor_states) for sensor_states
    sensor_proc.o(.ARM.exidx.text.sensor_get_result) refers to sensor_proc.o(.text.sensor_get_result) for [Anonymous Symbol]
    sensor_proc.o(.text.sensor_get_value) refers to sensor_proc.o(.bss.sensor_states) for sensor_states
    sensor_proc.o(.ARM.exidx.text.sensor_get_value) refers to sensor_proc.o(.text.sensor_get_value) for [Anonymous Symbol]
    sensor_proc.o(.text.sensor_get_status) refers to sensor_proc.o(.bss.sensor_states) for sensor_states
    sensor_proc.o(.ARM.exidx.text.sensor_get_status) refers to sensor_proc.o(.text.sensor_get_status) for [Anonymous Symbol]
    sensor_proc.o(.text.sensor_get_raw_adc) refers to sensor_proc.o(.bss.sensor_states) for sensor_states
    sensor_proc.o(.ARM.exidx.text.sensor_get_raw_adc) refers to sensor_proc.o(.text.sensor_get_raw_adc) for [Anonymous Symbol]
    sensor_proc.o(.text.sensor_reset_error_count) refers to sensor_proc.o(.bss.sensor_states) for sensor_states
    sensor_proc.o(.ARM.exidx.text.sensor_reset_error_count) refers to sensor_proc.o(.text.sensor_reset_error_count) for [Anonymous Symbol]
    sensor_proc.o(.text.sensor_get_debug_info) refers to sensor_proc.o(.bss.total_error_count) for total_error_count
    sensor_proc.o(.ARM.exidx.text.sensor_get_debug_info) refers to sensor_proc.o(.text.sensor_get_debug_info) for [Anonymous Symbol]
    sensor_proc.o(.text.sensor_set_dma_ready) refers to sensor_proc.o(.bss.dma_data_ready) for dma_data_ready
    sensor_proc.o(.ARM.exidx.text.sensor_set_dma_ready) refers to sensor_proc.o(.text.sensor_set_dma_ready) for [Anonymous Symbol]
    sys_timerevent.o(.text.TimerEvent_Init) refers to sys_timerevent.o(.bss.gTimerFlag) for gTimerFlag
    sys_timerevent.o(.text.TimerEvent_Init) refers to sys_timerevent.o(.bss.sTimerCount) for sTimerCount
    sys_timerevent.o(.text.TimerEvent_Init) refers to sys_timerevent.o(.bss.sys_runtime_ms) for sys_runtime_ms
    sys_timerevent.o(.ARM.exidx.text.TimerEvent_Init) refers to sys_timerevent.o(.text.TimerEvent_Init) for [Anonymous Symbol]
    sys_timerevent.o(.text.TimerEvent_Handler) refers to sys_timerevent.o(.bss.sTimerCount) for sTimerCount
    sys_timerevent.o(.text.TimerEvent_Handler) refers to sys_timerevent.o(.bss.gTimerFlag) for gTimerFlag
    sys_timerevent.o(.text.TimerEvent_Handler) refers to sys_timerevent.o(.bss.sys_runtime_ms) for sys_runtime_ms
    sys_timerevent.o(.ARM.exidx.text.TimerEvent_Handler) refers to sys_timerevent.o(.text.TimerEvent_Handler) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_Init) refers to low_speed_alarm.o(.bss.g_alarm_data) for g_alarm_data
    low_speed_alarm.o(.text.Low_Speed_Alarm_Init) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    low_speed_alarm.o(.text.Low_Speed_Alarm_Init) refers to sys_timerevent.o(.bss.sys_runtime_ms) for sys_runtime_ms
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_Init) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_Init) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_Task) refers to sys_timerevent.o(.bss.sys_runtime_ms) for sys_runtime_ms
    low_speed_alarm.o(.text.Low_Speed_Alarm_Task) refers to low_speed_alarm.o(.bss.g_alarm_data) for g_alarm_data
    low_speed_alarm.o(.text.Low_Speed_Alarm_Task) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    low_speed_alarm.o(.text.Low_Speed_Alarm_Task) refers to protection_monitor.o(.text.Protection_Monitor_GetStatus) for Protection_Monitor_GetStatus
    low_speed_alarm.o(.text.Low_Speed_Alarm_Task) refers to at32a403a_gpio.o(.text.gpio_bits_reset) for gpio_bits_reset
    low_speed_alarm.o(.text.Low_Speed_Alarm_Task) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_Task) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_Task) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_Reset) refers to low_speed_alarm.o(.bss.g_alarm_data) for g_alarm_data
    low_speed_alarm.o(.text.Low_Speed_Alarm_Reset) refers to sys_timerevent.o(.bss.sys_runtime_ms) for sys_runtime_ms
    low_speed_alarm.o(.text.Low_Speed_Alarm_Reset) refers to at32a403a_gpio.o(.text.gpio_bits_set) for gpio_bits_set
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_Reset) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_Reset) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_IsActive) refers to low_speed_alarm.o(.bss.g_alarm_data) for g_alarm_data
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_IsActive) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_IsActive) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_GetSystemState) refers to low_speed_alarm.o(.bss.g_alarm_data) for g_alarm_data
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetSystemState) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_GetSystemState) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_GetFaultReason) refers to low_speed_alarm.o(.bss.g_alarm_data) for g_alarm_data
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetFaultReason) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_GetFaultReason) for [Anonymous Symbol]
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetMotorSpeed) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_GetMotorSpeed) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_GetMotorTemp) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetMotorTemp) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_GetMotorTemp) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_GetBoardTemp) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetBoardTemp) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_GetBoardTemp) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_Get270VVoltage) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_Get270VVoltage) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_Get270VVoltage) for [Anonymous Symbol]
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_IsMotorRunning) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_IsMotorRunning) for [Anonymous Symbol]
    low_speed_alarm.o(.text.Low_Speed_Alarm_HasProtectionFault) refers to protection_monitor.o(.text.Protection_Monitor_GetStatus) for Protection_Monitor_GetStatus
    low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_HasProtectionFault) refers to low_speed_alarm.o(.text.Low_Speed_Alarm_HasProtectionFault) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_Init) refers to protection_monitor.o(.bss.event_handler) for event_handler
    protection_monitor.o(.text.Protection_Monitor_Init) refers to protection_monitor.o(.bss.protection_states) for protection_states
    protection_monitor.o(.text.Protection_Monitor_Init) refers to protection_monitor.o(.bss.total_trigger_count) for total_trigger_count
    protection_monitor.o(.text.Protection_Monitor_Init) refers to protection_monitor.o(.bss.fault_record_count) for fault_record_count
    protection_monitor.o(.text.Protection_Monitor_Init) refers to protection_monitor.o(.bss.fault_record_index) for fault_record_index
    protection_monitor.o(.text.Protection_Monitor_Init) refers to protection_monitor.o(.bss.fault_records) for fault_records
    protection_monitor.o(.text.Protection_Monitor_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    protection_monitor.o(.text.Protection_Monitor_Init) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_Init) refers to protection_monitor.o(.text.Protection_Monitor_Init) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_Tick) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_Tick) refers to protection_monitor.o(.rodata.param_configs) for param_configs
    protection_monitor.o(.text.Protection_Monitor_Tick) refers to protection_monitor.o(.bss.protection_states) for protection_states
    protection_monitor.o(.text.Protection_Monitor_Tick) refers to sys_timerevent.o(.bss.sys_runtime_ms) for sys_runtime_ms
    protection_monitor.o(.text.Protection_Monitor_Tick) refers to protection_monitor.o(.bss.total_trigger_count) for total_trigger_count
    protection_monitor.o(.text.Protection_Monitor_Tick) refers to protection_monitor.o(.bss.fault_record_index) for fault_record_index
    protection_monitor.o(.text.Protection_Monitor_Tick) refers to protection_monitor.o(.bss.fault_records) for fault_records
    protection_monitor.o(.text.Protection_Monitor_Tick) refers to protection_monitor.o(.bss.fault_record_count) for fault_record_count
    protection_monitor.o(.text.Protection_Monitor_Tick) refers to protection_monitor.o(.bss.event_handler) for event_handler
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_Tick) refers to protection_monitor.o(.text.Protection_Monitor_Tick) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_GetStatus) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_GetStatus) refers to protection_monitor.o(.bss.protection_states) for protection_states
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetStatus) refers to protection_monitor.o(.text.Protection_Monitor_GetStatus) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_Reset) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_Reset) refers to protection_monitor.o(.bss.protection_states) for protection_states
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_Reset) refers to protection_monitor.o(.text.Protection_Monitor_Reset) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_ResetAll) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_ResetAll) refers to protection_monitor.o(.bss.protection_states) for protection_states
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_ResetAll) refers to protection_monitor.o(.text.Protection_Monitor_ResetAll) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_GetValue) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_GetValue) refers to protection_monitor.o(.bss.protection_states) for protection_states
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetValue) refers to protection_monitor.o(.text.Protection_Monitor_GetValue) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_GetStats) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_GetStats) refers to protection_monitor.o(.bss.total_trigger_count) for total_trigger_count
    protection_monitor.o(.text.Protection_Monitor_GetStats) refers to protection_monitor.o(.bss.protection_states) for protection_states
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetStats) refers to protection_monitor.o(.text.Protection_Monitor_GetStats) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_GetFaultRecordCount) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_GetFaultRecordCount) refers to protection_monitor.o(.bss.fault_record_count) for fault_record_count
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetFaultRecordCount) refers to protection_monitor.o(.text.Protection_Monitor_GetFaultRecordCount) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_GetFaultRecord) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_GetFaultRecord) refers to protection_monitor.o(.bss.fault_record_count) for fault_record_count
    protection_monitor.o(.text.Protection_Monitor_GetFaultRecord) refers to protection_monitor.o(.bss.fault_record_index) for fault_record_index
    protection_monitor.o(.text.Protection_Monitor_GetFaultRecord) refers to protection_monitor.o(.bss.fault_records) for fault_records
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetFaultRecord) refers to protection_monitor.o(.text.Protection_Monitor_GetFaultRecord) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_ClearFaultRecords) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_ClearFaultRecords) refers to protection_monitor.o(.bss.fault_record_count) for fault_record_count
    protection_monitor.o(.text.Protection_Monitor_ClearFaultRecords) refers to protection_monitor.o(.bss.fault_record_index) for fault_record_index
    protection_monitor.o(.text.Protection_Monitor_ClearFaultRecords) refers to protection_monitor.o(.bss.fault_records) for fault_records
    protection_monitor.o(.text.Protection_Monitor_ClearFaultRecords) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_ClearFaultRecords) refers to protection_monitor.o(.text.Protection_Monitor_ClearFaultRecords) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_GetLatestFaultRecord) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_GetLatestFaultRecord) refers to protection_monitor.o(.bss.fault_record_count) for fault_record_count
    protection_monitor.o(.text.Protection_Monitor_GetLatestFaultRecord) refers to protection_monitor.o(.bss.fault_record_index) for fault_record_index
    protection_monitor.o(.text.Protection_Monitor_GetLatestFaultRecord) refers to protection_monitor.o(.bss.fault_records) for fault_records
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetLatestFaultRecord) refers to protection_monitor.o(.text.Protection_Monitor_GetLatestFaultRecord) for [Anonymous Symbol]
    protection_monitor.o(.text.Protection_Monitor_FindFaultRecord) refers to protection_monitor.o(.bss.module_initialized) for module_initialized
    protection_monitor.o(.text.Protection_Monitor_FindFaultRecord) refers to protection_monitor.o(.bss.fault_record_count) for fault_record_count
    protection_monitor.o(.text.Protection_Monitor_FindFaultRecord) refers to protection_monitor.o(.bss.fault_record_index) for fault_record_index
    protection_monitor.o(.text.Protection_Monitor_FindFaultRecord) refers to protection_monitor.o(.bss.fault_records) for fault_records
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_FindFaultRecord) refers to protection_monitor.o(.text.Protection_Monitor_FindFaultRecord) for [Anonymous Symbol]
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetMotorSpeed) refers to protection_monitor.o(.text.Protection_Monitor_GetMotorSpeed) for [Anonymous Symbol]
    protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetTorqueCurrent) refers to protection_monitor.o(.text.Protection_Monitor_GetTorqueCurrent) for [Anonymous Symbol]
    protection_monitor.o(.text.get_bus_voltage) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    protection_monitor.o(.ARM.exidx.text.get_bus_voltage) refers to protection_monitor.o(.text.get_bus_voltage) for [Anonymous Symbol]
    protection_monitor.o(.ARM.exidx.text.get_bus_current) refers to protection_monitor.o(.text.get_bus_current) for [Anonymous Symbol]
    protection_monitor.o(.text.get_motor_temperature) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    protection_monitor.o(.ARM.exidx.text.get_motor_temperature) refers to protection_monitor.o(.text.get_motor_temperature) for [Anonymous Symbol]
    protection_monitor.o(.text.get_driver_temperature) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    protection_monitor.o(.ARM.exidx.text.get_driver_temperature) refers to protection_monitor.o(.text.get_driver_temperature) for [Anonymous Symbol]
    protection_monitor.o(.text.get_current_imbalance) refers to adc_pmsm.o(.bss.g_adc_current_data) for g_adc_current_data
    protection_monitor.o(.ARM.exidx.text.get_current_imbalance) refers to protection_monitor.o(.text.get_current_imbalance) for [Anonymous Symbol]
    protection_monitor.o(.ARM.exidx.text.get_stall_detection) refers to protection_monitor.o(.text.get_stall_detection) for [Anonymous Symbol]
    protection_monitor.o(.ARM.exidx.text.get_motor_speed) refers to protection_monitor.o(.text.get_motor_speed) for [Anonymous Symbol]
    protection_monitor.o(.ARM.exidx.text.get_stall_dynamic_delay_warn) refers to protection_monitor.o(.text.get_stall_dynamic_delay_warn) for [Anonymous Symbol]
    protection_monitor.o(.ARM.exidx.text.get_stall_dynamic_delay_fault) refers to protection_monitor.o(.text.get_stall_dynamic_delay_fault) for [Anonymous Symbol]
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.text.get_bus_voltage) for get_bus_voltage
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.rodata.bus_overvolt_rules) for [Anonymous Symbol]
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.rodata.bus_undervolt_rules) for [Anonymous Symbol]
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.text.get_bus_current) for get_bus_current
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.rodata.bus_overcurrent_rules) for [Anonymous Symbol]
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.text.get_motor_temperature) for get_motor_temperature
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.rodata.motor_temp_rules) for [Anonymous Symbol]
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.text.get_driver_temperature) for get_driver_temperature
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.rodata.driver_temp_rules) for [Anonymous Symbol]
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.text.get_current_imbalance) for get_current_imbalance
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.rodata.current_imbalance_rules) for [Anonymous Symbol]
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.text.get_stall_detection) for get_stall_detection
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.rodata.stall_rules) for [Anonymous Symbol]
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.text.get_motor_speed) for get_motor_speed
    protection_monitor.o(.rodata.param_configs) refers to protection_monitor.o(.rodata.overspeed_rules) for [Anonymous Symbol]
    protection_monitor.o(.rodata.stall_rules) refers to protection_monitor.o(.text.get_stall_dynamic_delay_warn) for get_stall_dynamic_delay_warn
    protection_monitor.o(.rodata.stall_rules) refers to protection_monitor.o(.text.get_stall_dynamic_delay_fault) for get_stall_dynamic_delay_fault
    power_stage_test.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to sys_timerevent.o(.bss.sys_runtime_ms) for sys_runtime_ms
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to adc_pmsm.o(.bss.g_adc_current_data) for g_adc_current_data
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to at32a403a_tmr.o(.text.tmr_flag_clear) for tmr_flag_clear
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to power_stage_test.o(.text.pst_set_device_pwm) for pst_set_device_pwm
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to at32a403a_tmr.o(.text.tmr_flag_get) for tmr_flag_get
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to at32a403a_tmr.o(.text.tmr_channel_value_set) for tmr_channel_value_set
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to at32a403a_tmr.o(.text.tmr_counter_enable) for tmr_counter_enable
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to at32a403a_tmr.o(.text.tmr_output_enable) for tmr_output_enable
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to power_stage_test.o(.bss.g_adc_sampling_active) for g_adc_sampling_active
    power_stage_test.o(.text.Power_Stage_Test_Execute) refers to power_stage_test.o(.bss.g_adc_sample_count) for g_adc_sample_count
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute) refers to power_stage_test.o(.text.Power_Stage_Test_Execute) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.text.Power_Stage_Test_ResetResult) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult) refers to power_stage_test.o(.text.Power_Stage_Test_ResetResult) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult) refers to power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers to sensor_proc.o(.text.sensor_get_value) for sensor_get_value
    power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage) refers to power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers to power_stage_test.o(.bss.g_adc_sampling_active) for g_adc_sampling_active
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers to power_stage_test.o(.bss.g_adc_sample_count) for g_adc_sample_count
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers to power_stage_test.o(.bss.g_current_test_phase) for g_current_test_phase
    power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) refers to adc_pmsm.o(.text.ADC_PMSM_IsCalibrationCompleted) for ADC_PMSM_IsCalibrationCompleted
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling) refers to power_stage_test.o(.text.Power_Stage_Test_StartADCSampling) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers to power_stage_test.o(.bss.g_adc_sampling_active) for g_adc_sampling_active
    power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) refers to power_stage_test.o(.bss.g_adc_sample_count) for g_adc_sample_count
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling) refers to power_stage_test.o(.text.Power_Stage_Test_StopADCSampling) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples) refers to power_stage_test.o(.text.Power_Stage_Test_GetADCSamples) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency) refers to power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers to __2snprintf.o(.text) for __2snprintf
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers to power_stage_test.o(.rodata.str1.1) for .L.str.3
    power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo) refers to power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault) refers to power_stage_test.o(.text.Power_Stage_Test_IsSoftFault) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers to __2snprintf.o(.text) for __2snprintf
    power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData) refers to power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers to __2snprintf.o(.text) for __2snprintf
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers to power_stage_test.o(.rodata.str1.1) for .L.str.16
    power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis) refers to power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers to power_stage_test.o(.rodata.str1.1) for .L.str.18
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers to __2snprintf.o(.text) for __2snprintf
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers to power_stage_test.o(.bss.g_test_result) for g_test_result
    power_stage_test.o(.text.Power_Stage_Test_GetStatistics) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics) refers to power_stage_test.o(.text.Power_Stage_Test_GetStatistics) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers to adc_pmsm.o(.bss.g_adc_current_data) for g_adc_current_data
    power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) refers to at32a403a_tmr.o(.text.tmr_flag_get) for tmr_flag_get
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming) refers to power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig) refers to power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig) for [Anonymous Symbol]
    power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig) refers to power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig) for [Anonymous Symbol]
    power_stage_test.o(.text.pst_set_device_pwm) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.text.pst_set_device_pwm) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.text.pst_set_device_pwm) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.text.pst_set_device_pwm) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.text.pst_set_device_pwm) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.text.pst_set_device_pwm) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.text.pst_set_device_pwm) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.text.pst_set_device_pwm) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.text.pst_set_device_pwm) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.text.pst_set_device_pwm) refers to at32a403a_tmr.o(.text.tmr_channel_value_set) for tmr_channel_value_set
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm) refers to power_stage_test.o(.text.pst_set_device_pwm) for [Anonymous Symbol]
    power_stage_test.o(.bss.g_test_result) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.bss.g_test_result) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.bss.g_test_result) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.bss.g_test_result) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.bss.g_test_result) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.bss.g_test_result) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.bss.g_test_result) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.bss.g_test_result) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.bss.g_test_result) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.bss.g_adc_sampling_active) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.bss.g_adc_sampling_active) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.bss.g_adc_sampling_active) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.bss.g_adc_sampling_active) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.bss.g_adc_sampling_active) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.bss.g_adc_sampling_active) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.bss.g_adc_sampling_active) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.bss.g_adc_sampling_active) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.bss.g_adc_sampling_active) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.rodata.str1.1) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.rodata.str1.1) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.rodata.str1.1) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.rodata.str1.1) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.rodata.str1.1) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.rodata.str1.1) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.bss.g_adc_sample_count) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.bss.g_adc_sample_count) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.bss.g_adc_sample_count) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.bss.g_adc_sample_count) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.bss.g_adc_sample_count) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.bss.g_adc_sample_count) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.bss.g_adc_sample_count) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.bss.g_adc_sample_count) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.bss.g_adc_sample_count) refers (Special) to _printf_str.o(.text) for _printf_str
    power_stage_test.o(.bss.g_current_test_phase) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    power_stage_test.o(.bss.g_current_test_phase) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    power_stage_test.o(.bss.g_current_test_phase) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    power_stage_test.o(.bss.g_current_test_phase) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    power_stage_test.o(.bss.g_current_test_phase) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    power_stage_test.o(.bss.g_current_test_phase) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    power_stage_test.o(.bss.g_current_test_phase) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    power_stage_test.o(.bss.g_current_test_phase) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    power_stage_test.o(.bss.g_current_test_phase) refers (Special) to _printf_str.o(.text) for _printf_str
    arm_cos_f32.o(.text.arm_cos_f32) refers to arm_common_tables.o(.rodata.sinTable_f32) for sinTable_f32
    arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32) refers to arm_cos_f32.o(.text.arm_cos_f32) for [Anonymous Symbol]
    arm_sin_f32.o(.text.arm_sin_f32) refers to arm_common_tables.o(.rodata.sinTable_f32) for sinTable_f32
    arm_sin_f32.o(.ARM.exidx.text.arm_sin_f32) refers to arm_sin_f32.o(.text.arm_sin_f32) for [Anonymous Symbol]
    arm_mat_add_f32.o(.ARM.exidx.text.arm_mat_add_f32) refers to arm_mat_add_f32.o(.text.arm_mat_add_f32) for [Anonymous Symbol]
    arm_mat_init_f32.o(.ARM.exidx.text.arm_mat_init_f32) refers to arm_mat_init_f32.o(.text.arm_mat_init_f32) for [Anonymous Symbol]
    arm_mat_mult_f32.o(.ARM.exidx.text.arm_mat_mult_f32) refers to arm_mat_mult_f32.o(.text.arm_mat_mult_f32) for [Anonymous Symbol]
    arm_copy_f32.o(.ARM.exidx.text.arm_copy_f32) refers to arm_copy_f32.o(.text.arm_copy_f32) for [Anonymous Symbol]
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(x$fpl$fcmp) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(x$fpl$fcmp) refers to dgeqf.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(x$fpl$fcmp) refers to dleqf.o(x$fpl$dleqf) for _dcmple
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    fmodf.o(i.__hardfp_fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.__hardfp_fmodf) refers to frem_clz.o(x$fpl$frem) for _frem
    fmodf.o(i.__hardfp_fmodf) refers to _rserrno.o(.text) for __set_errno
    fmodf.o(i.__hardfp_fmodf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    fmodf.o(i.__softfp_fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.__softfp_fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf.o(i.fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dgeqf.o(x$fpl$dgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dgeqf.o(x$fpl$dgeqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dgeqf.o(x$fpl$dgeqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frem_clz.o(x$fpl$frem) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frem_clz.o(x$fpl$frem) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7em.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_at32a403a.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7em.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing sysfsm.o(.text), (0 bytes).
    Removing sysfsm.o(.ARM.exidx.text.Timer_Tasks_Execute), (8 bytes).
    Removing sysfsm.o(.bss.g_state_machine_ctx), (104 bytes).
    Removing at32a403a_adc.o(.text), (0 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_reset), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_enable), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_combine_mode_select), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_base_default_para_init), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_base_config), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_dma_mode_enable), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_interrupt_enable), (26 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_interrupt_enable), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_calibration_init), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_calibration_init_status_get), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_calibration_start), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_calibration_status_get), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_voltage_monitor_enable), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_voltage_monitor_threshold_value_set), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_voltage_monitor_single_channel_select), (10 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_voltage_monitor_single_channel_select), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_channel_set), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_preempt_channel_length_set), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_preempt_channel_set), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_conversion_trigger_set), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_preempt_conversion_trigger_set), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_preempt_offset_value_set), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_ordinary_part_count_set), (20 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_part_count_set), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_part_mode_enable), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_preempt_part_mode_enable), (18 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_preempt_part_mode_enable), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_preempt_auto_mode_enable), (18 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_preempt_auto_mode_enable), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_tempersensor_vintrv_enable), (26 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_tempersensor_vintrv_enable), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_software_trigger_enable), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_ordinary_software_trigger_status_get), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_software_trigger_status_get), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_preempt_software_trigger_enable), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_preempt_software_trigger_status_get), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_preempt_software_trigger_status_get), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_ordinary_conversion_data_get), (6 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_ordinary_conversion_data_get), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_combine_ordinary_conversion_data_get), (12 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_combine_ordinary_conversion_data_get), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_preempt_conversion_data_get), (20 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_preempt_conversion_data_get), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_flag_get), (8 bytes).
    Removing at32a403a_adc.o(.text.adc_interrupt_flag_get), (58 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_interrupt_flag_get), (8 bytes).
    Removing at32a403a_adc.o(.ARM.exidx.text.adc_flag_clear), (8 bytes).
    Removing at32a403a_crm.o(.text), (0 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_reset), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_lext_bypass), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_lext_bypass), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_hext_bypass), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_hext_bypass), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_flag_get), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_interrupt_flag_get), (164 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_interrupt_flag_get), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_hext_stable_wait), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_hick_clock_trimming_set), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_hick_clock_trimming_set), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_hick_clock_calibration_set), (44 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_hick_clock_calibration_set), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_periph_clock_enable), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_periph_reset), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_periph_sleep_mode_clock_enable), (40 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_periph_sleep_mode_clock_enable), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_clock_source_enable), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_flag_clear), (124 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_flag_clear), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_rtc_clock_select), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_rtc_clock_select), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_rtc_clock_enable), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_rtc_clock_enable), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_ahb_div_set), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_apb1_div_set), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_apb2_div_set), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_adc_clock_div_set), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_usb_clock_div_set), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_clock_failure_detection_enable), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_clock_failure_detection_enable), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_battery_powered_domain_reset), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_battery_powered_domain_reset), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_pll_config), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_sysclk_switch), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_sysclk_switch_status_get), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_clocks_freq_get), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_clock_out_set), (28 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_clock_out_set), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_interrupt_enable), (24 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_interrupt_enable), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_auto_step_mode_enable), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_usb_interrupt_remapping_set), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_hick_divider_select), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_hick_divider_select), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_hick_sclk_frequency_select), (34 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_hick_sclk_frequency_select), (8 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_usb_clock_source_select), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_clkout_to_tmr10_enable), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_clkout_to_tmr10_enable), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_hext_clock_div_set), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_hext_clock_div_set), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_clkout_div_set), (22 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_clkout_div_set), (8 bytes).
    Removing at32a403a_crm.o(.text.crm_emac_output_pulse_set), (26 bytes).
    Removing at32a403a_crm.o(.ARM.exidx.text.crm_emac_output_pulse_set), (8 bytes).
    Removing at32a403a_debug.o(.text), (0 bytes).
    Removing at32a403a_debug.o(.text.debug_device_id_get), (12 bytes).
    Removing at32a403a_debug.o(.ARM.exidx.text.debug_device_id_get), (8 bytes).
    Removing at32a403a_debug.o(.text.debug_periph_mode_set), (24 bytes).
    Removing at32a403a_debug.o(.ARM.exidx.text.debug_periph_mode_set), (8 bytes).
    Removing at32a403a_dma.o(.text), (0 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_reset), (8 bytes).
    Removing at32a403a_dma.o(.text.dma_data_number_set), (4 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_data_number_set), (8 bytes).
    Removing at32a403a_dma.o(.text.dma_data_number_get), (6 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_data_number_get), (8 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_interrupt_enable), (8 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_channel_enable), (8 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_flexible_config), (8 bytes).
    Removing at32a403a_dma.o(.text.dma_flag_get), (26 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_flag_get), (8 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_interrupt_flag_get), (8 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_flag_clear), (8 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_default_para_init), (8 bytes).
    Removing at32a403a_dma.o(.ARM.exidx.text.dma_init), (8 bytes).
    Removing at32a403a_exint.o(.text), (0 bytes).
    Removing at32a403a_exint.o(.text.exint_reset), (30 bytes).
    Removing at32a403a_exint.o(.ARM.exidx.text.exint_reset), (8 bytes).
    Removing at32a403a_exint.o(.text.exint_default_para_init), (12 bytes).
    Removing at32a403a_exint.o(.ARM.exidx.text.exint_default_para_init), (8 bytes).
    Removing at32a403a_exint.o(.text.exint_init), (114 bytes).
    Removing at32a403a_exint.o(.ARM.exidx.text.exint_init), (8 bytes).
    Removing at32a403a_exint.o(.text.exint_flag_clear), (24 bytes).
    Removing at32a403a_exint.o(.ARM.exidx.text.exint_flag_clear), (8 bytes).
    Removing at32a403a_exint.o(.text.exint_flag_get), (18 bytes).
    Removing at32a403a_exint.o(.ARM.exidx.text.exint_flag_get), (8 bytes).
    Removing at32a403a_exint.o(.text.exint_interrupt_flag_get), (22 bytes).
    Removing at32a403a_exint.o(.ARM.exidx.text.exint_interrupt_flag_get), (8 bytes).
    Removing at32a403a_exint.o(.text.exint_software_interrupt_event_generate), (16 bytes).
    Removing at32a403a_exint.o(.ARM.exidx.text.exint_software_interrupt_event_generate), (8 bytes).
    Removing at32a403a_exint.o(.text.exint_interrupt_enable), (24 bytes).
    Removing at32a403a_exint.o(.ARM.exidx.text.exint_interrupt_enable), (8 bytes).
    Removing at32a403a_exint.o(.text.exint_event_enable), (24 bytes).
    Removing at32a403a_exint.o(.ARM.exidx.text.exint_event_enable), (8 bytes).
    Removing at32a403a_flash.o(.text), (0 bytes).
    Removing at32a403a_flash.o(.text.flash_flag_get), (56 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_flag_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_flag_clear), (42 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_flag_clear), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_operation_status_get), (42 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_operation_status_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank1_operation_status_get), (42 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank1_operation_status_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank2_operation_status_get), (42 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank2_operation_status_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_spim_operation_status_get), (42 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_spim_operation_status_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_operation_wait_for), (108 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_operation_wait_for), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank1_operation_wait_for), (108 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank1_operation_wait_for), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank2_operation_wait_for), (108 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank2_operation_wait_for), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_spim_operation_wait_for), (108 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_spim_operation_wait_for), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_unlock), (34 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_unlock), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank1_unlock), (30 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank1_unlock), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank2_unlock), (30 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank2_unlock), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_spim_unlock), (40 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_spim_unlock), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_lock), (26 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_lock), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank1_lock), (18 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank1_lock), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank2_lock), (18 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank2_lock), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_spim_lock), (18 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_spim_lock), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_sector_erase), (484 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_sector_erase), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_spim_dummy_read), (28 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_spim_dummy_read), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_internal_all_erase), (254 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_internal_all_erase), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank1_erase), (130 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank1_erase), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_bank2_erase), (130 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_bank2_erase), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_spim_all_erase), (152 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_spim_all_erase), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_user_system_data_erase), (314 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_user_system_data_erase), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_fap_status_get), (16 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_fap_status_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_word_program), (456 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_word_program), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_halfword_program), (456 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_halfword_program), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_byte_program), (266 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_byte_program), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_user_system_data_program), (174 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_user_system_data_program), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_epp_set), (514 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_epp_set), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_epp_status_get), (14 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_epp_status_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_fap_enable), (298 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_fap_enable), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_ssb_set), (162 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_ssb_set), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_ssb_status_get), (16 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_ssb_status_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_interrupt_enable), (158 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_interrupt_enable), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_spim_model_select), (48 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_spim_model_select), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_spim_encryption_range_set), (12 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_spim_encryption_range_set), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_spim_mass_program), (328 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_spim_mass_program), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_slib_enable), (314 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_slib_enable), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_slib_disable), (132 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_slib_disable), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_slib_remaining_count_get), (16 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_slib_remaining_count_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_slib_state_get), (16 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_slib_state_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_slib_start_sector_get), (16 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_slib_start_sector_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_slib_datastart_sector_get), (16 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_slib_datastart_sector_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_slib_end_sector_get), (14 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_slib_end_sector_get), (8 bytes).
    Removing at32a403a_flash.o(.text.flash_crc_calibrate), (98 bytes).
    Removing at32a403a_flash.o(.ARM.exidx.text.flash_crc_calibrate), (8 bytes).
    Removing at32a403a_gpio.o(.text), (0 bytes).
    Removing at32a403a_gpio.o(.text.gpio_reset), (46 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_reset), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_iomux_reset), (26 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_iomux_reset), (8 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_init), (8 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_default_para_init), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_input_data_bit_read), (14 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_input_data_bit_read), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_input_data_read), (6 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_input_data_read), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_output_data_bit_read), (10 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_output_data_bit_read), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_output_data_read), (6 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_output_data_read), (8 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_bits_set), (8 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_bits_reset), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_bits_toggle), (8 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_bits_toggle), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_bits_write), (12 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_bits_write), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_port_write), (4 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_port_write), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_pin_wp_config), (16 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_pin_wp_config), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_pins_huge_driven_config), (16 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_pins_huge_driven_config), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_event_output_config), (22 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_event_output_config), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_event_output_enable), (24 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_event_output_enable), (8 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_pin_remap_config), (8 bytes).
    Removing at32a403a_gpio.o(.text.gpio_exint_line_config), (156 bytes).
    Removing at32a403a_gpio.o(.ARM.exidx.text.gpio_exint_line_config), (8 bytes).
    Removing at32a403a_misc.o(.text), (0 bytes).
    Removing at32a403a_misc.o(.text.nvic_system_reset), (4 bytes).
    Removing at32a403a_misc.o(.ARM.exidx.text.nvic_system_reset), (8 bytes).
    Removing at32a403a_misc.o(.text.__NVIC_SystemReset), (36 bytes).
    Removing at32a403a_misc.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing at32a403a_misc.o(.ARM.exidx.text.nvic_irq_enable), (8 bytes).
    Removing at32a403a_misc.o(.text.nvic_irq_disable), (42 bytes).
    Removing at32a403a_misc.o(.ARM.exidx.text.nvic_irq_disable), (8 bytes).
    Removing at32a403a_misc.o(.ARM.exidx.text.nvic_priority_group_config), (8 bytes).
    Removing at32a403a_misc.o(.text.nvic_vector_table_set), (24 bytes).
    Removing at32a403a_misc.o(.ARM.exidx.text.nvic_vector_table_set), (8 bytes).
    Removing at32a403a_misc.o(.text.nvic_lowpower_mode_config), (24 bytes).
    Removing at32a403a_misc.o(.ARM.exidx.text.nvic_lowpower_mode_config), (8 bytes).
    Removing at32a403a_misc.o(.ARM.exidx.text.systick_clock_source_config), (8 bytes).
    Removing at32a403a_pwc.o(.text), (0 bytes).
    Removing at32a403a_pwc.o(.text.pwc_reset), (28 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_reset), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_battery_powered_domain_access), (26 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_battery_powered_domain_access), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_pvm_level_select), (26 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_pvm_level_select), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_power_voltage_monitor_enable), (26 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_power_voltage_monitor_enable), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_wakeup_pin_enable), (24 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_wakeup_pin_enable), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_flag_clear), (36 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_flag_clear), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_flag_get), (18 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_flag_get), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_sleep_mode_enter), (36 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_sleep_mode_enter), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_deep_sleep_mode_enter), (42 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_deep_sleep_mode_enter), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_voltage_regulate_set), (18 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_voltage_regulate_set), (8 bytes).
    Removing at32a403a_pwc.o(.text.pwc_standby_mode_enter), (44 bytes).
    Removing at32a403a_pwc.o(.ARM.exidx.text.pwc_standby_mode_enter), (8 bytes).
    Removing at32a403a_spi.o(.text), (0 bytes).
    Removing at32a403a_spi.o(.text.spi_i2s_reset), (98 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_i2s_reset), (8 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_default_para_init), (8 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_init), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_crc_next_transmit), (10 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_crc_next_transmit), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_crc_polynomial_set), (14 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_crc_polynomial_set), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_crc_polynomial_get), (6 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_crc_polynomial_get), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_crc_enable), (18 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_crc_enable), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_crc_value_get), (12 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_crc_value_get), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_hardware_cs_output_enable), (18 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_hardware_cs_output_enable), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_software_cs_internal_level_set), (18 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_software_cs_internal_level_set), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_frame_bit_num_set), (18 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_frame_bit_num_set), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_half_duplex_direction_set), (18 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_half_duplex_direction_set), (8 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_enable), (8 bytes).
    Removing at32a403a_spi.o(.text.i2s_default_para_init), (16 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.i2s_default_para_init), (8 bytes).
    Removing at32a403a_spi.o(.text.i2s_init), (382 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.i2s_init), (8 bytes).
    Removing at32a403a_spi.o(.text.i2s_enable), (18 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.i2s_enable), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_i2s_interrupt_enable), (16 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_i2s_interrupt_enable), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_i2s_dma_transmitter_enable), (18 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_i2s_dma_transmitter_enable), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_i2s_dma_receiver_enable), (10 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_i2s_dma_receiver_enable), (8 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_i2s_data_transmit), (8 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_i2s_data_receive), (8 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_i2s_flag_get), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_i2s_interrupt_flag_get), (140 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_i2s_interrupt_flag_get), (8 bytes).
    Removing at32a403a_spi.o(.text.spi_i2s_flag_clear), (78 bytes).
    Removing at32a403a_spi.o(.ARM.exidx.text.spi_i2s_flag_clear), (8 bytes).
    Removing at32a403a_tmr.o(.text), (0 bytes).
    Removing at32a403a_tmr.o(.text.tmr_reset), (356 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_reset), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_counter_enable), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_output_default_para_init), (10 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_output_default_para_init), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_input_default_para_init), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_input_default_para_init), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_brkdt_default_para_init), (10 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_brkdt_default_para_init), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_base_init), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_clock_source_div_set), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_cnt_dir_set), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_repetition_counter_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_counter_value_set), (4 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_counter_value_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_counter_value_get), (4 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_counter_value_get), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_div_value_set), (4 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_div_value_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_div_value_get), (4 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_div_value_get), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_config), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_output_channel_mode_select), (88 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_mode_select), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_period_value_set), (4 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_period_value_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_period_value_get), (4 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_period_value_get), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_value_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_channel_value_get), (40 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_value_get), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_period_buffer_enable), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_buffer_enable), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_immediately_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_output_channel_switch_set), (88 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_switch_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_one_cycle_mode_enable), (18 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_one_cycle_mode_enable), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_32_bit_function_enable), (38 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_32_bit_function_enable), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_overflow_request_source_set), (18 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_overflow_request_source_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_overflow_event_disable), (18 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_overflow_event_disable), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_input_channel_init), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_channel_enable), (136 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_enable), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_input_channel_filter_set), (88 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_input_channel_filter_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_pwm_input_config), (276 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_pwm_input_config), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_channel1_input_select), (18 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_channel1_input_select), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_input_channel_divider_set), (88 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_input_channel_divider_set), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_primary_mode_select), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_sub_mode_select), (10 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_sub_mode_select), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_channel_dma_select), (18 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_dma_select), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_hall_select), (18 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_hall_select), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_channel_buffer_enable), (10 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_channel_buffer_enable), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_trigger_input_select), (18 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_trigger_input_select), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_sub_sync_mode_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_dma_request_enable), (26 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_dma_request_enable), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_interrupt_enable), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_interrupt_flag_get), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_flag_get), (10 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_flag_get), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_flag_clear), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_event_sw_trigger), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_event_sw_trigger), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_output_enable), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_internal_clock_set), (10 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_internal_clock_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_output_channel_polarity_set), (144 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_output_channel_polarity_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_external_clock_config), (54 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_external_clock_config), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_external_clock_mode1_config), (70 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_external_clock_mode1_config), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_external_clock_mode2_config), (62 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_external_clock_mode2_config), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_encoder_mode_config), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_force_output_set), (88 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_force_output_set), (8 bytes).
    Removing at32a403a_tmr.o(.text.tmr_dma_control_config), (26 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_dma_control_config), (8 bytes).
    Removing at32a403a_tmr.o(.ARM.exidx.text.tmr_brkdt_config), (8 bytes).
    Removing at32a403a_usb.o(.text), (0 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_dev_init), (8 bytes).
    Removing at32a403a_usb.o(.text.usb_interrupt_enable), (16 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_interrupt_enable), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_connect), (8 bytes).
    Removing at32a403a_usb.o(.text.usb_disconnect), (18 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_disconnect), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_usbbufs_enable), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_ept_open), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_ept_close), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_write_packet), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_read_packet), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_set_address), (8 bytes).
    Removing at32a403a_usb.o(.text.usb_ept_stall), (48 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_ept_stall), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_enter_suspend), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_exit_suspend), (8 bytes).
    Removing at32a403a_usb.o(.text.usb_remote_wkup_set), (10 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_remote_wkup_set), (8 bytes).
    Removing at32a403a_usb.o(.text.usb_remote_wkup_clear), (10 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_remote_wkup_clear), (8 bytes).
    Removing at32a403a_usb.o(.text.usb_buffer_malloc), (18 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_buffer_malloc), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_buffer_free), (8 bytes).
    Removing at32a403a_usb.o(.text.usb_flag_get), (10 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_flag_get), (8 bytes).
    Removing at32a403a_usb.o(.text.usb_interrupt_flag_get), (32 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_interrupt_flag_get), (8 bytes).
    Removing at32a403a_usb.o(.ARM.exidx.text.usb_flag_clear), (8 bytes).
    Removing at32a403a_wdt.o(.text), (0 bytes).
    Removing at32a403a_wdt.o(.text.wdt_enable), (16 bytes).
    Removing at32a403a_wdt.o(.ARM.exidx.text.wdt_enable), (8 bytes).
    Removing at32a403a_wdt.o(.ARM.exidx.text.wdt_counter_reload), (8 bytes).
    Removing at32a403a_wdt.o(.ARM.exidx.text.wdt_reload_value_set), (8 bytes).
    Removing at32a403a_wdt.o(.ARM.exidx.text.wdt_divider_set), (8 bytes).
    Removing at32a403a_wdt.o(.ARM.exidx.text.wdt_register_write_enable), (8 bytes).
    Removing at32a403a_wdt.o(.text.wdt_flag_get), (18 bytes).
    Removing at32a403a_wdt.o(.ARM.exidx.text.wdt_flag_get), (8 bytes).
    Removing system_at32a403a.o(.text), (0 bytes).
    Removing system_at32a403a.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_at32a403a.o(.ARM.exidx.text.system_core_clock_update), (8 bytes).
    Removing usbd_core.o(.text), (0 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_core_in_handler), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ept_send), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ept_recv), (8 bytes).
    Removing usbd_core.o(.text.usbd_ctrl_recv_status), (42 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ctrl_recv_status), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_core_out_handler), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ctrl_send_status), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_core_setup_handler), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ctrl_send), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ctrl_recv), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_clear_stall), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_set_stall), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ctrl_unsupport), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_get_recv_len), (8 bytes).
    Removing usbd_core.o(.text.usbd_ept_dbuffer_enable), (32 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ept_dbuffer_enable), (8 bytes).
    Removing usbd_core.o(.text.usbd_ept_buf_auto_define), (62 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ept_buf_auto_define), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ept_buf_custom_define), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ept_open), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ept_close), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_connect), (8 bytes).
    Removing usbd_core.o(.text.usbd_disconnect), (6 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_disconnect), (8 bytes).
    Removing usbd_core.o(.text.usbd_set_device_addr), (6 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_set_device_addr), (8 bytes).
    Removing usbd_core.o(.text.usbd_connect_state_get), (6 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_connect_state_get), (8 bytes).
    Removing usbd_core.o(.text.usbd_remote_wakeup), (40 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_remote_wakeup), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_enter_suspend), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_ept_defaut_init), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.usbd_core_init), (8 bytes).
    Removing usbd_int.o(.text), (0 bytes).
    Removing usbd_int.o(.ARM.exidx.text.usbd_irq_handler), (8 bytes).
    Removing usbd_int.o(.text.usbd_ept_loop_handler), (28 bytes).
    Removing usbd_int.o(.ARM.exidx.text.usbd_ept_loop_handler), (8 bytes).
    Removing usbd_int.o(.ARM.exidx.text.usbd_reset_handler), (8 bytes).
    Removing usbd_int.o(.text.usbd_sof_handler), (12 bytes).
    Removing usbd_int.o(.ARM.exidx.text.usbd_sof_handler), (8 bytes).
    Removing usbd_int.o(.text.usbd_suspend_handler), (44 bytes).
    Removing usbd_int.o(.ARM.exidx.text.usbd_suspend_handler), (8 bytes).
    Removing usbd_int.o(.text.usbd_wakeup_handler), (38 bytes).
    Removing usbd_int.o(.ARM.exidx.text.usbd_wakeup_handler), (8 bytes).
    Removing usbd_int.o(.ARM.exidx.text.usbd_eptn_handler), (8 bytes).
    Removing usbd_sdr.o(.text), (0 bytes).
    Removing usbd_sdr.o(.ARM.exidx.text.usbd_setup_request_parse), (8 bytes).
    Removing usbd_sdr.o(.ARM.exidx.text.usbd_device_request), (8 bytes).
    Removing usbd_sdr.o(.ARM.exidx.text.usbd_interface_request), (8 bytes).
    Removing usbd_sdr.o(.ARM.exidx.text.usbd_endpoint_request), (8 bytes).
    Removing at32a403a_int.o(.text), (0 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.DMA1_Channel1_IRQHandler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.ADC1_2_IRQHandler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.TMR1_BRK_TMR9_IRQHandler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.TMR1_OVF_TMR10_IRQHandler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.TMR6_GLOBAL_IRQHandler), (8 bytes).
    Removing at32a403a_int.o(.ARM.exidx.text.USBFS_MAPL_IRQHandler), (8 bytes).
    Removing at32a403a_wk_config.o(.text), (0 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_system_clock_config), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_periph_clock_config), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_debug_config), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_nvic_config), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_gpio_config), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_spi3_init), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_tmr1_init), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_tmr3_init), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_tmr6_init), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_usbfs_init), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_wdt_init), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_adc1_init), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_adc2_init), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_dma1_channel1_init), (8 bytes).
    Removing at32a403a_wk_config.o(.ARM.exidx.text.wk_dma_channel_config), (8 bytes).
    Removing cdc_class.o(.text), (0 bytes).
    Removing cdc_class.o(.ARM.exidx.text.class_init_handler), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.class_clear_handler), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.class_setup_handler), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.class_ept0_tx_handler), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.class_ept0_rx_handler), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.class_in_handler), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.class_out_handler), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.class_sof_handler), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.class_event_handler), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.usb_vcp_get_rxdata), (8 bytes).
    Removing cdc_class.o(.ARM.exidx.text.usb_vcp_send_data), (8 bytes).
    Removing cdc_desc.o(.text), (0 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_descriptor), (8 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_qualifier), (8 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_configuration), (8 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_other_speed), (8 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_lang_id), (8 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_manufacturer_string), (8 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_product_string), (8 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_serial_string), (8 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_interface_string), (8 bytes).
    Removing cdc_desc.o(.ARM.exidx.text.get_device_config_string), (8 bytes).
    Removing usb_app.o(.text), (0 bytes).
    Removing usb_app.o(.ARM.exidx.text.wk_usb_app_init), (8 bytes).
    Removing usb_app.o(.ARM.exidx.text.wk_usb_app_task), (8 bytes).
    Removing usb_app.o(.ARM.exidx.text.wk_usbfs_irq_handler), (8 bytes).
    Removing usb_app.o(.text.usb_delay_ms), (4 bytes).
    Removing usb_app.o(.ARM.exidx.text.usb_delay_ms), (8 bytes).
    Removing usb_app.o(.ARM.exidx.text.usb_send_data), (8 bytes).
    Removing wk_system.o(.text), (0 bytes).
    Removing wk_system.o(.ARM.exidx.text.wk_delay_us), (8 bytes).
    Removing wk_system.o(.ARM.exidx.text.wk_delay_ms), (8 bytes).
    Removing wk_system.o(.ARM.exidx.text.wk_timebase_init), (8 bytes).
    Removing algorithm.o(.text), (0 bytes).
    Removing algorithm.o(.text.fnSysVoltBaseInit), (34 bytes).
    Removing algorithm.o(.ARM.exidx.text.fnSysVoltBaseInit), (8 bytes).
    Removing algorithm.o(.text.fnSysVoltBaseReset), (26 bytes).
    Removing algorithm.o(.ARM.exidx.text.fnSysVoltBaseReset), (8 bytes).
    Removing algorithm.o(.text.fnSysVoltBaseCalc), (2 bytes).
    Removing algorithm.o(.ARM.exidx.text.fnSysVoltBaseCalc), (8 bytes).
    Removing delay.o(.text), (0 bytes).
    Removing delay.o(.text.delay_init), (62 bytes).
    Removing delay.o(.ARM.exidx.text.delay_init), (8 bytes).
    Removing delay.o(.text.delay_us), (60 bytes).
    Removing delay.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing delay.o(.text.delay_ms), (102 bytes).
    Removing delay.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing delay.o(.text.delay_sec), (186 bytes).
    Removing delay.o(.ARM.exidx.text.delay_sec), (8 bytes).
    Removing delay.o(.bss.fac_us), (4 bytes).
    Removing delay.o(.bss.fac_ms), (4 bytes).
    Removing mathbasic.o(.text), (0 bytes).
    Removing mathbasic.o(.text.Filter_calc), (46 bytes).
    Removing mathbasic.o(.ARM.exidx.text.Filter_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text), (0 bytes).
    Removing motor_vectorcontrol.o(.text.Para_derive), (592 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.Para_derive), (8 bytes).
    Removing motor_vectorcontrol.o(.text.park_calc), (50 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.park_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.clarke_calc), (36 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.clarke_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.VSDclarke_calc), (576 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.VSDclarke_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.ipark_calc), (50 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.ipark_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.iclarke_calc), (64 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.iclarke_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.lpfl_calc), (46 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.lpfl_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.pi_speed_const_calc), (128 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.pi_speed_const_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.pi_current_const_calc), (30 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.pi_current_const_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.pi_speed_calc), (112 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.pi_speed_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.pi_voltage_calc), (2 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.pi_voltage_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.pi_fun_calc), (104 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.pi_fun_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.pi_flux_calc), (112 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.pi_flux_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.Vector_ctrl_ResetTs), (234 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.Vector_ctrl_ResetTs), (8 bytes).
    Removing motor_vectorcontrol.o(.text.Vector_ctrl_init), (1028 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.Vector_ctrl_init), (8 bytes).
    Removing motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Init), (28 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.OpenLoopFreqCtrl_Init), (8 bytes).
    Removing motor_vectorcontrol.o(.text.Vector_ctrl_reset), (576 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.Vector_ctrl_reset), (8 bytes).
    Removing motor_vectorcontrol.o(.text.Vector_ctrl_calc), (944 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.Vector_ctrl_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.pi_speed_inc_fun_calc), (108 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.pi_speed_inc_fun_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.OpenLoopFreqCtrl_Update), (344 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.OpenLoopFreqCtrl_Update), (8 bytes).
    Removing motor_vectorcontrol.o(.text.SVPWM), (912 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.SVPWM), (8 bytes).
    Removing motor_vectorcontrol.o(.text.lpf_filter), (38 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.lpf_filter), (8 bytes).
    Removing motor_vectorcontrol.o(.text.lpfi_calc), (46 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.lpfi_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.IVSDclarke_calc), (540 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.IVSDclarke_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.UpdatePwm), (244 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.UpdatePwm), (8 bytes).
    Removing motor_vectorcontrol.o(.text.ResolverInitialAngleCalibration), (56 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.ResolverInitialAngleCalibration), (8 bytes).
    Removing motor_vectorcontrol.o(.text.FixedAngle), (56 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.FixedAngle), (8 bytes).
    Removing motor_vectorcontrol.o(.text.CalcTRAP), (112 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.CalcTRAP), (8 bytes).
    Removing motor_vectorcontrol.o(.text.TrapeAccelerationCurve), (164 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.TrapeAccelerationCurve), (8 bytes).
    Removing motor_vectorcontrol.o(.text.update_wave), (156 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.update_wave), (8 bytes).
    Removing motor_vectorcontrol.o(.text.VoltageBalance_calc), (2 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.VoltageBalance_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.marsest_calc), (2 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.marsest_calc), (8 bytes).
    Removing motor_vectorcontrol.o(.text.WeakFluxCtr), (80 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.WeakFluxCtr), (8 bytes).
    Removing motor_vectorcontrol.o(.text.PI_Speed_Incremental_Calc), (196 bytes).
    Removing motor_vectorcontrol.o(.ARM.exidx.text.PI_Speed_Incremental_Calc), (8 bytes).
    Removing motor_vectorcontrol.o(.data.Ia_flt), (16 bytes).
    Removing motor_vectorcontrol.o(.data.Ib_flt), (16 bytes).
    Removing motor_vectorcontrol.o(.data.Ic_flt), (16 bytes).
    Removing motor_vectorcontrol.o(.bss.Id1_flt), (16 bytes).
    Removing motor_vectorcontrol.o(.bss.Iq1_flt), (16 bytes).
    Removing motor_vectorcontrol.o(.data.laal), (4 bytes).
    Removing motor_vectorcontrol.o(.data.laad), (4 bytes).
    Removing motor_vectorcontrol.o(.data.laaq), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.v_max1), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.v_min1), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.v_max2), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.v_min2), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.v_zero1), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.v_zero2), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.limit_iq_ref), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.FeedforwordFlag), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.P), (4 bytes).
    Removing motor_vectorcontrol.o(.data.Ufor), (4 bytes).
    Removing motor_vectorcontrol.o(.data.US), (4 bytes).
    Removing motor_vectorcontrol.o(.data.power_cos), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.Isfor), (4 bytes).
    Removing motor_vectorcontrol.o(.data.trapeLine), (40 bytes).
    Removing motor_vectorcontrol.o(.data.para), (260 bytes).
    Removing motor_vectorcontrol.o(.data.park1), (32 bytes).
    Removing motor_vectorcontrol.o(.data.clarke1), (28 bytes).
    Removing motor_vectorcontrol.o(.data.vsdclarke), (44 bytes).
    Removing motor_vectorcontrol.o(.data.ipark1), (32 bytes).
    Removing motor_vectorcontrol.o(.data.ipark2), (32 bytes).
    Removing motor_vectorcontrol.o(.data.iclarke1), (28 bytes).
    Removing motor_vectorcontrol.o(.data.iclarke2), (28 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_isd), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_isd1), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_isd2), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_isd_ref), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_isq), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_isq1), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_isq2), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_usd1), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_usd2), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_usq1), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_usq2), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_isq_ref), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_ekf_w), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_w), (24 bytes).
    Removing motor_vectorcontrol.o(.data.lpf_w_ref), (24 bytes).
    Removing motor_vectorcontrol.o(.data.pi_speed_const), (24 bytes).
    Removing motor_vectorcontrol.o(.data.pi_current_const), (36 bytes).
    Removing motor_vectorcontrol.o(.data.pi_speed), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_voltage), (52 bytes).
    Removing motor_vectorcontrol.o(.data.pi_isd), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_isq), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_isd1), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_isq1), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_isd2), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_isq2), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_isqz), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_isdz), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_isxz), (56 bytes).
    Removing motor_vectorcontrol.o(.data.pi_flux_w), (52 bytes).
    Removing motor_vectorcontrol.o(.bss.gOpenLoopFreqCtrl), (32 bytes).
    Removing motor_vectorcontrol.o(.bss.para2), (72 bytes).
    Removing motor_vectorcontrol.o(.bss.Ifoward), (4 bytes).
    Removing motor_vectorcontrol.o(.rodata.ICLARK_SQRT3), (4 bytes).
    Removing motor_vectorcontrol.o(.rodata.ICLARK_RATIO), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.Tc1), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.Tc2), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.Tc3), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.ResolverInitialAngleCalibration.cnt), (2 bytes).
    Removing motor_vectorcontrol.o(.bss.TrapeAccelerationCurve.time_cnt), (2 bytes).
    Removing motor_vectorcontrol.o(.bss.WeakFluxCtr.out), (4 bytes).
    Removing motor_vectorcontrol.o(.bss.trap_data), (4 bytes).
    Removing sys_isr_controller.o(.text), (0 bytes).
    Removing sys_isr_controller.o(.text.ADC1_IRQHandler), (368 bytes).
    Removing sys_isr_controller.o(.ARM.exidx.text.ADC1_IRQHandler), (8 bytes).
    Removing sys_isr_controller.o(.text.SysCurProtect), (148 bytes).
    Removing sys_isr_controller.o(.ARM.exidx.text.SysCurProtect), (8 bytes).
    Removing sys_isr_controller.o(.text.DATAPeriodDSPtoARM), (64 bytes).
    Removing sys_isr_controller.o(.ARM.exidx.text.DATAPeriodDSPtoARM), (8 bytes).
    Removing sys_isr_controller.o(.text.DATAPeriodARMtoDSP), (140 bytes).
    Removing sys_isr_controller.o(.ARM.exidx.text.DATAPeriodARMtoDSP), (8 bytes).
    Removing sys_isr_controller.o(.text.flux_thetaChange), (2 bytes).
    Removing sys_isr_controller.o(.ARM.exidx.text.flux_thetaChange), (8 bytes).
    Removing sys_isr_controller.o(.bss.overspeedflag), (2 bytes).
    Removing sys_isr_controller.o(.bss.fTimeCount), (4 bytes).
    Removing sys_isr_controller.o(.bss.OPEN_flag), (4 bytes).
    Removing sys_isr_controller.o(.bss.C2OPEN_flag), (4 bytes).
    Removing sys_isr_controller.o(.bss.uDMAFlag), (2 bytes).
    Removing sys_isr_controller.o(.data.Filter_udch), (24 bytes).
    Removing sys_isr_controller.o(.data.Filter_udcl), (24 bytes).
    Removing sys_isr_controller.o(.data.Filter_idc), (24 bytes).
    Removing sys_isr_controller.o(.data.Filter_idc1), (24 bytes).
    Removing sys_isr_controller.o(.data.Filter_idc2), (24 bytes).
    Removing sys_isr_controller.o(.data.Filter_speed), (24 bytes).
    Removing sys_isr_controller.o(.bss.QH), (4 bytes).
    Removing sys_isr_controller.o(.bss.Ld_mras), (4 bytes).
    Removing sys_isr_controller.o(.bss.Lq_mras), (4 bytes).
    Removing sys_isr_controller.o(.bss.MRAS_w_flag), (4 bytes).
    Removing sys_isr_controller.o(.bss.power_pf), (4 bytes).
    Removing sys_isr_controller.o(.data.F_count), (4 bytes).
    Removing sys_isr_controller.o(.bss.MRAS_OPEN), (4 bytes).
    Removing sys_isr_controller.o(.bss.adc_cnt), (2 bytes).
    Removing sys_isr_controller.o(.bss.faultId), (1 bytes).
    Removing sys_isr_controller.o(.bss.I_overCru), (16 bytes).
    Removing sys_isr_controller.o(.bss.shijiWelec), (4 bytes).
    Removing sys_isr_controller.o(.bss.ChangeMarsF), (4 bytes).
    Removing sys_isr_controller.o(.bss.mid_speed), (4 bytes).
    Removing sys_isr_controller.o(.bss.high_speed), (4 bytes).
    Removing sysctl_analogprocess.o(.text), (0 bytes).
    Removing sysctl_analogprocess.o(.text.fnAISample), (140 bytes).
    Removing sysctl_analogprocess.o(.ARM.exidx.text.fnAISample), (8 bytes).
    Removing sysctl_analogprocess.o(.text.fnParaUpdateSysSamScaParameter), (116 bytes).
    Removing sysctl_analogprocess.o(.ARM.exidx.text.fnParaUpdateSysSamScaParameter), (8 bytes).
    Removing sysctl_analogprocess.o(.text.fnSysBaseValueCal), (368 bytes).
    Removing sysctl_analogprocess.o(.ARM.exidx.text.fnSysBaseValueCal), (8 bytes).
    Removing sysctl_analogprocess.o(.text.fnSysOffsetInit), (132 bytes).
    Removing sysctl_analogprocess.o(.ARM.exidx.text.fnSysOffsetInit), (8 bytes).
    Removing sysctl_analogprocess.o(.text.fnSysOffsetParameterCal), (120 bytes).
    Removing sysctl_analogprocess.o(.ARM.exidx.text.fnSysOffsetParameterCal), (8 bytes).
    Removing sysctl_analogprocess.o(.bss.error_A), (4 bytes).
    Removing sysctl_analogprocess.o(.bss.error_B), (4 bytes).
    Removing sysctl_analogprocess.o(.bss.error_C), (4 bytes).
    Removing sysctl_analogprocess.o(.bss.uCEFlag), (2 bytes).
    Removing sysctl_globalvar.o(.text), (0 bytes).
    Removing sysctl_globalvar.o(.bss.HMIBuffer), (4000 bytes).
    Removing sysctl_globalvar.o(.bss.uDMA_Addr), (4 bytes).
    Removing sysctl_globalvar.o(.data.DMADest1), (4 bytes).
    Removing sysctl_globalvar.o(.data.pARMParamRdFlag), (4 bytes).
    Removing sysctl_globalvar.o(.data.RdDSPtoARM), (4 bytes).
    Removing sysctl_globalvar.o(.data.pOffsetRam), (4 bytes).
    Removing sysctl_globalvar.o(.bss.DMABuf1), (256 bytes).
    Removing sysctl_globalvar.o(.data.pWaveParamStart), (4 bytes).
    Removing sysctl_globalvar.o(.data.pRunSpeed), (4 bytes).
    Removing sysctl_globalvar.o(.data.pEstSpeed), (4 bytes).
    Removing sysctl_globalvar.o(.data.pFaultWaveStart), (4 bytes).
    Removing sysctl_globalvar.o(.data.SysMoore), (8 bytes).
    Removing sysctl_globalvar.o(.bss.SysRatedParameter), (52 bytes).
    Removing sysctl_globalvar.o(.data.ScopeDATAUpLoad), (4 bytes).
    Removing sysctl_globalvar.o(.data.SysSampOffset), (272 bytes).
    Removing sysctl_globalvar.o(.data.SysSamScaParameter), (84 bytes).
    Removing sysctl_globalvar.o(.bss.SysProParamReg), (8 bytes).
    Removing sysctl_globalvar.o(.data.SysBaseValue), (60 bytes).
    Removing sysctl_globalvar.o(.bss.SysSamDSPtoARM), (32 bytes).
    Removing sysctl_globalvar.o(.data.AnalogInput), (312 bytes).
    Removing sysctl_globalvar.o(.data.SysEnviConfg), (4 bytes).
    Removing sysctl_globalvar.o(.bss.DAOut), (12 bytes).
    Removing sysctl_globalvar.o(.bss.SysCtlParameter), (2 bytes).
    Removing sysctl_globalvar.o(.data.RotorSpeedclc), (88 bytes).
    Removing sysctl_globalvar.o(.data.SysVoltBase), (96 bytes).
    Removing sysctl_globalvar.o(.bss.uInterCount), (2 bytes).
    Removing sysctl_globalvar.o(.bss.uSoftStartCount), (4 bytes).
    Removing sysctl_globalvar.o(.bss.uDirCount), (2 bytes).
    Removing sysctl_globalvar.o(.bss.uDirCountMax), (2 bytes).
    Removing sysctl_globalvar.o(.data.SynMotorVc), (724 bytes).
    Removing sysctl_globalvar.o(.bss.DMASource1), (4 bytes).
    Removing sysctl_globalvar.o(.bss.SysErrIndexReg), (2 bytes).
    Removing sysctl_globalvar.o(.bss.SysCtlModeREG), (2 bytes).
    Removing sysctl_globalvar.o(.bss.SysCtlReg), (2 bytes).
    Removing sysctl_globalvar.o(.bss.DSPFaultCodeReg), (2 bytes).
    Removing sysctl_globalvar.o(.bss.ARMFaultReg), (2 bytes).
    Removing sysctl_globalvar.o(.bss.machineAngle), (4 bytes).
    Removing sysctl_globalvar.o(.bss.eleAngle), (4 bytes).
    Removing sysctl_rotorget.o(.text), (0 bytes).
    Removing sysctl_rotorget.o(.text.fnRotorSpeedclc), (344 bytes).
    Removing sysctl_rotorget.o(.ARM.exidx.text.fnRotorSpeedclc), (8 bytes).
    Removing sysctl_rotorget.o(.text.GetElectricalAngle), (64 bytes).
    Removing sysctl_rotorget.o(.ARM.exidx.text.GetElectricalAngle), (8 bytes).
    Removing sysctl_rotorget.o(.text.GetRotorAngle), (328 bytes).
    Removing sysctl_rotorget.o(.ARM.exidx.text.GetRotorAngle), (8 bytes).
    Removing sysctl_rotorget.o(.text.GetRotorSpeed), (144 bytes).
    Removing sysctl_rotorget.o(.ARM.exidx.text.GetRotorSpeed), (8 bytes).
    Removing sysctl_rotorget.o(.bss.Position_count_flag), (4 bytes).
    Removing sysctl_rotorget.o(.bss.limitDelta), (4 bytes).
    Removing sysctl_rotorget.o(.bss.resolver_angle), (4 bytes).
    Removing sysctl_rotorget.o(.bss.rotor_cont), (4 bytes).
    Removing sysctl_rotorget.o(.bss.GetRotorAngle.cycler_counter), (4 bytes).
    Removing sysctl_rotorget.o(.bss.GetRotorAngle.last_angle), (4 bytes).
    Removing sysctl_rotorget.o(.bss.GetRotorAngle.start), (1 bytes).
    Removing sysctl_rotorget.o(.bss.GetRotorSpeed.last_angle), (4 bytes).
    Removing sysctl_rotorget.o(.bss.JSD), (4 bytes).
    Removing sysctl_rotorget.o(.bss.JSDtheta), (4 bytes).
    Removing sysctl_rotorget.o(.bss.Postion_Speed), (1600 bytes).
    Removing sysctl_rotorget.o(.bss.Position_count), (4 bytes).
    Removing sysctl_sysmoore.o(.text), (0 bytes).
    Removing sysctl_sysmoore.o(.text.fnSysMooreCal), (612 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnSysMooreCal), (8 bytes).
    Removing sysctl_sysmoore.o(.text.fnSystemResetParam), (14 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnSystemResetParam), (8 bytes).
    Removing sysctl_sysmoore.o(.text.fnSystemRUNParam), (76 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnSystemRUNParam), (8 bytes).
    Removing sysctl_sysmoore.o(.text.fnSystemReadyParam), (186 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnSystemReadyParam), (8 bytes).
    Removing sysctl_sysmoore.o(.text.fnSysFaultReset), (44 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnSysFaultReset), (8 bytes).
    Removing sysctl_sysmoore.o(.text.fnSystemInitParam), (200 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnSystemInitParam), (8 bytes).
    Removing sysctl_sysmoore.o(.text.fnSysRatedParameter), (44 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnSysRatedParameter), (8 bytes).
    Removing sysctl_sysmoore.o(.text.fnSysParamterRef), (50 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnSysParamterRef), (8 bytes).
    Removing sysctl_sysmoore.o(.text.fnVarCopyToRam), (8 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnVarCopyToRam), (8 bytes).
    Removing sysctl_sysmoore.o(.text.fnSysHMICommInit), (2 bytes).
    Removing sysctl_sysmoore.o(.ARM.exidx.text.fnSysHMICommInit), (8 bytes).
    Removing anoptv8cmd.o(.text), (0 bytes).
    Removing anoptv8cmd.o(.ARM.exidx.text.AnoPTv8CmdFrameAnl), (8 bytes).
    Removing anoptv8cmd.o(.ARM.exidx.text.AnoPTv8CmdGetCount), (8 bytes).
    Removing anoptv8cmd.o(.ARM.exidx.text.AnoPTv8CmdGetInfo), (8 bytes).
    Removing anoptv8cmd.o(.ARM.exidx.text.AnoPTv8CmdRegister), (8 bytes).
    Removing anoptv8framefactory.o(.text), (0 bytes).
    Removing anoptv8framefactory.o(.text.AnoPTv8CalFrameCheck), (56 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8CalFrameCheck), (8 bytes).
    Removing anoptv8framefactory.o(.text.AnoPTv8CalFrameCheckInBuffer), (56 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8CalFrameCheckInBuffer), (8 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendBuf), (8 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendCheck), (8 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendDevInfo), (8 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendStr), (8 bytes).
    Removing anoptv8framefactory.o(.text.AnoPTv8SendValStr), (78 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendValStr), (8 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendParNum), (8 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendParVal), (8 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendParInfo), (8 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendCmdNum), (8 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendCmdInfo), (8 bytes).
    Removing anoptv8framefactory.o(.text.AnoPTv8SendAnyFrame), (4 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendAnyFrame), (8 bytes).
    Removing anoptv8framefactory.o(.text.AnoPTv8SendIapCmd), (38 bytes).
    Removing anoptv8framefactory.o(.ARM.exidx.text.AnoPTv8SendIapCmd), (8 bytes).
    Removing anoptv8par.o(.text), (0 bytes).
    Removing anoptv8par.o(.ARM.exidx.text.AnoPTv8ParFrameAnl), (8 bytes).
    Removing anoptv8par.o(.ARM.exidx.text.AnoPTv8GetParamType), (8 bytes).
    Removing anoptv8par.o(.text.anoPTv8ParSetVal), (100 bytes).
    Removing anoptv8par.o(.ARM.exidx.text.anoPTv8ParSetVal), (8 bytes).
    Removing anoptv8par.o(.ARM.exidx.text.AnoPTv8ParGetCount), (8 bytes).
    Removing anoptv8par.o(.ARM.exidx.text.AnoPTv8ParRegister), (8 bytes).
    Removing anoptv8par.o(.ARM.exidx.text.AnoPTv8ParGetInfo), (8 bytes).
    Removing anoptv8par.o(.text.AnoPTv8ParGetVal), (100 bytes).
    Removing anoptv8par.o(.ARM.exidx.text.AnoPTv8ParGetVal), (8 bytes).
    Removing anoptv8run.o(.text), (0 bytes).
    Removing anoptv8run.o(.text.AnoPTv8TxRunThread1ms), (44 bytes).
    Removing anoptv8run.o(.ARM.exidx.text.AnoPTv8TxRunThread1ms), (8 bytes).
    Removing anoptv8run.o(.ARM.exidx.text.AnoPTv8TxLargeBufSend), (8 bytes).
    Removing anoptv8run.o(.text.AnoPTv8TxMainLoopSingle), (44 bytes).
    Removing anoptv8run.o(.ARM.exidx.text.AnoPTv8TxMainLoopSingle), (8 bytes).
    Removing anoptv8run.o(.text.AnoPTv8TxMainLoopAll), (44 bytes).
    Removing anoptv8run.o(.ARM.exidx.text.AnoPTv8TxMainLoopAll), (8 bytes).
    Removing anoptv8run.o(.text.AnoPTv8TxMainLoopBatch), (44 bytes).
    Removing anoptv8run.o(.ARM.exidx.text.AnoPTv8TxMainLoopBatch), (8 bytes).
    Removing anoptv8run.o(.text.AnoPTv8TxLargeBufFlush), (38 bytes).
    Removing anoptv8run.o(.ARM.exidx.text.AnoPTv8TxLargeBufFlush), (8 bytes).
    Removing anoptv8run.o(.ARM.exidx.text.AnoPTv8RecvBytes), (8 bytes).
    Removing anoptv8run.o(.text.AnoPTv8RecvOneByte), (22 bytes).
    Removing anoptv8run.o(.ARM.exidx.text.AnoPTv8RecvOneByte), (8 bytes).
    Removing hwinterface.o(.text), (0 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwInit), (8 bytes).
    Removing hwinterface.o(.text.AnoPTv8HwSendFrame), (2 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendFrame), (8 bytes).
    Removing hwinterface.o(.text.AnoPTv8HwDataTest), (128 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwDataTest), (8 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwSendBytes), (8 bytes).
    Removing hwinterface.o(.text.AnoPTv8HwRecvByte), (4 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvByte), (8 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwRecvBytes), (8 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwTrigger1ms), (8 bytes).
    Removing hwinterface.o(.text.AnoPTv8HwParValRecvCallback), (2 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwParValRecvCallback), (8 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdRecvCallback), (8 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8HwParCmdResetParameter), (8 bytes).
    Removing hwinterface.o(.text.Usb_printf), (66 bytes).
    Removing hwinterface.o(.ARM.exidx.text.Usb_printf), (8 bytes).
    Removing hwinterface.o(.text.AnoPTv8CalFrameCheck01), (56 bytes).
    Removing hwinterface.o(.ARM.exidx.text.AnoPTv8CalFrameCheck01), (8 bytes).
    Removing hwinterface.o(.text.Usb_printf_direct), (136 bytes).
    Removing hwinterface.o(.ARM.exidx.text.Usb_printf_direct), (8 bytes).
    Removing hwinterface.o(.bss.AnoPTv8HwDataTest.test_count), (4 bytes).
    Removing hwinterface.o(.bss.AnoPTv8HwDataTest.last_test_time), (4 bytes).
    Removing motorcmd.o(.text), (0 bytes).
    Removing motorcmd.o(.text.SetFrameSendEnable), (56 bytes).
    Removing motorcmd.o(.ARM.exidx.text.SetFrameSendEnable), (8 bytes).
    Removing motorcmd.o(.text.GetFrameSendEnable), (12 bytes).
    Removing motorcmd.o(.ARM.exidx.text.GetFrameSendEnable), (8 bytes).
    Removing motorcmd.o(.ARM.exidx.text.MotorCmdInit), (8 bytes).
    Removing motorcmd.o(.text.AnoPTv8_Init), (102 bytes).
    Removing motorcmd.o(.ARM.exidx.text.AnoPTv8_Init), (8 bytes).
    Removing motorcmd.o(.text.Print_System_Status_Distributed), (2 bytes).
    Removing motorcmd.o(.ARM.exidx.text.Print_System_Status_Distributed), (8 bytes).
    Removing motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorStart), (8 bytes).
    Removing motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorStop), (8 bytes).
    Removing motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorReset), (8 bytes).
    Removing motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorSelfTest), (8 bytes).
    Removing motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorGetFault), (8 bytes).
    Removing motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_MotorClearFault), (8 bytes).
    Removing motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_WaveCtrl), (8 bytes).
    Removing motorcmd.o(.ARM.exidx.text.AnoPTv8CmdFun_WaveStopAll), (8 bytes).
    Removing motordata.o(.text), (0 bytes).
    Removing motordata.o(.text.MotorDataSendFrame), (190 bytes).
    Removing motordata.o(.ARM.exidx.text.MotorDataSendFrame), (8 bytes).
    Removing motordata.o(.text.UpdateF5ADCData), (2 bytes).
    Removing motordata.o(.ARM.exidx.text.UpdateF5ADCData), (8 bytes).
    Removing motordata.o(.ARM.exidx.text.UpdateF2SensorData), (8 bytes).
    Removing motordata.o(.text.UpdateF3TempData), (50 bytes).
    Removing motordata.o(.ARM.exidx.text.UpdateF3TempData), (8 bytes).
    Removing motordata.o(.ARM.exidx.text.UpdateF1FocData), (8 bytes).
    Removing motordata.o(.text.MotorDataTest), (54 bytes).
    Removing motordata.o(.ARM.exidx.text.MotorDataTest), (8 bytes).
    Removing motordata.o(.bss.MotorDataTest.last_test_time), (4 bytes).
    Removing motorparams.o(.text), (0 bytes).
    Removing motorparams.o(.ARM.exidx.text.MotorParamsInit), (8 bytes).
    Removing ad2s1212_spi.o(.text), (0 bytes).
    Removing ad2s1212_spi.o(.text.AD2S2S1210_ModeCfg), (98 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S2S1210_ModeCfg), (8 bytes).
    Removing ad2s1212_spi.o(.text.AD2S1210_Delay), (2 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_Delay), (8 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_Init), (8 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_WRITE), (8 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_READ), (8 bytes).
    Removing ad2s1212_spi.o(.text.AD2S1210_REFAULT), (102 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_REFAULT), (8 bytes).
    Removing ad2s1212_spi.o(.text.AD2S1210_RESET), (30 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_RESET), (8 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_CommRead), (8 bytes).
    Removing ad2s1212_spi.o(.text.AD2S1210_ReadVelocity), (188 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_ReadVelocity), (8 bytes).
    Removing ad2s1212_spi.o(.text.AD2S1210_GetRotationSpeed), (56 bytes).
    Removing ad2s1212_spi.o(.ARM.exidx.text.AD2S1210_GetRotationSpeed), (8 bytes).
    Removing enc_speed.o(.text), (0 bytes).
    Removing enc_speed.o(.text.InitKalmanFilter), (336 bytes).
    Removing enc_speed.o(.ARM.exidx.text.InitKalmanFilter), (8 bytes).
    Removing enc_speed.o(.text.SetKalmanFilterParams), (72 bytes).
    Removing enc_speed.o(.ARM.exidx.text.SetKalmanFilterParams), (8 bytes).
    Removing enc_speed.o(.text.EncoderSpeed_Init), (380 bytes).
    Removing enc_speed.o(.ARM.exidx.text.EncoderSpeed_Init), (8 bytes).
    Removing enc_speed.o(.text.SetLowPassFilterCoeff), (64 bytes).
    Removing enc_speed.o(.ARM.exidx.text.SetLowPassFilterCoeff), (8 bytes).
    Removing enc_speed.o(.text.SetMovingAvgFilterSize), (90 bytes).
    Removing enc_speed.o(.ARM.exidx.text.SetMovingAvgFilterSize), (8 bytes).
    Removing enc_speed.o(.text.InitFhanFilter), (68 bytes).
    Removing enc_speed.o(.ARM.exidx.text.InitFhanFilter), (8 bytes).
    Removing enc_speed.o(.text.InitSpeedPI), (108 bytes).
    Removing enc_speed.o(.ARM.exidx.text.InitSpeedPI), (8 bytes).
    Removing enc_speed.o(.text.CalculateTimerCountFromSPI), (144 bytes).
    Removing enc_speed.o(.ARM.exidx.text.CalculateTimerCountFromSPI), (8 bytes).
    Removing enc_speed.o(.text.SpeedLoopSystem_Init), (154 bytes).
    Removing enc_speed.o(.ARM.exidx.text.SpeedLoopSystem_Init), (8 bytes).
    Removing enc_speed.o(.text.SetSpeedFilterType), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.SetSpeedFilterType), (8 bytes).
    Removing enc_speed.o(.text.EnableFhanFilter), (30 bytes).
    Removing enc_speed.o(.ARM.exidx.text.EnableFhanFilter), (8 bytes).
    Removing enc_speed.o(.text.EnableSpeedPI), (34 bytes).
    Removing enc_speed.o(.ARM.exidx.text.EnableSpeedPI), (8 bytes).
    Removing enc_speed.o(.text.EnablePIFeedforward), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.EnablePIFeedforward), (8 bytes).
    Removing enc_speed.o(.text.EncoderSpeed_Reset), (50 bytes).
    Removing enc_speed.o(.ARM.exidx.text.EncoderSpeed_Reset), (8 bytes).
    Removing enc_speed.o(.text.ZPulseDetectedCallback), (20 bytes).
    Removing enc_speed.o(.ARM.exidx.text.ZPulseDetectedCallback), (8 bytes).
    Removing enc_speed.o(.text.GetZPulseCounter), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.GetZPulseCounter), (8 bytes).
    Removing enc_speed.o(.text.ResetZPulseCounter), (16 bytes).
    Removing enc_speed.o(.ARM.exidx.text.ResetZPulseCounter), (8 bytes).
    Removing enc_speed.o(.text.ResetOverflowCounter), (26 bytes).
    Removing enc_speed.o(.ARM.exidx.text.ResetOverflowCounter), (8 bytes).
    Removing enc_speed.o(.text.GetElectricalAngle_ENC), (48 bytes).
    Removing enc_speed.o(.ARM.exidx.text.GetElectricalAngle_ENC), (8 bytes).
    Removing enc_speed.o(.text.GetSignedSpeed_RPS), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.GetSignedSpeed_RPS), (8 bytes).
    Removing enc_speed.o(.text.GetSignedSpeed_RPM), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.GetSignedSpeed_RPM), (8 bytes).
    Removing enc_speed.o(.text.CalculateSpeed_4KHz), (812 bytes).
    Removing enc_speed.o(.ARM.exidx.text.CalculateSpeed_4KHz), (8 bytes).
    Removing enc_speed.o(.text.SetFhanParams), (30 bytes).
    Removing enc_speed.o(.ARM.exidx.text.SetFhanParams), (8 bytes).
    Removing enc_speed.o(.text.IsFhanEnabled), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.IsFhanEnabled), (8 bytes).
    Removing enc_speed.o(.text.ProcessFhan), (192 bytes).
    Removing enc_speed.o(.ARM.exidx.text.ProcessFhan), (8 bytes).
    Removing enc_speed.o(.text.GetFhanSpeed_RPS), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.GetFhanSpeed_RPS), (8 bytes).
    Removing enc_speed.o(.text.GetFhanAcceleration_RPS), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.GetFhanAcceleration_RPS), (8 bytes).
    Removing enc_speed.o(.text.SetSpeedPIParams), (76 bytes).
    Removing enc_speed.o(.ARM.exidx.text.SetSpeedPIParams), (8 bytes).
    Removing enc_speed.o(.text.ResetSpeedPIIntegral), (22 bytes).
    Removing enc_speed.o(.ARM.exidx.text.ResetSpeedPIIntegral), (8 bytes).
    Removing enc_speed.o(.text.IsSpeedPIEnabled), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.IsSpeedPIEnabled), (8 bytes).
    Removing enc_speed.o(.text.GetSpeedPIOutput), (312 bytes).
    Removing enc_speed.o(.ARM.exidx.text.GetSpeedPIOutput), (8 bytes).
    Removing enc_speed.o(.text.GetCurrentCommandSafe), (20 bytes).
    Removing enc_speed.o(.ARM.exidx.text.GetCurrentCommandSafe), (8 bytes).
    Removing enc_speed.o(.text.GetCurrentCommandValue), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.GetCurrentCommandValue), (8 bytes).
    Removing enc_speed.o(.text.IsCurrentCommandUpdated), (14 bytes).
    Removing enc_speed.o(.ARM.exidx.text.IsCurrentCommandUpdated), (8 bytes).
    Removing enc_speed.o(.text.ClearCurrentCommandFlag), (16 bytes).
    Removing enc_speed.o(.ARM.exidx.text.ClearCurrentCommandFlag), (8 bytes).
    Removing enc_speed.o(.text.UpdateF4CustomDataWithTarget), (6 bytes).
    Removing enc_speed.o(.ARM.exidx.text.UpdateF4CustomDataWithTarget), (8 bytes).
    Removing enc_speed.o(.text.SimulateABZEncoder), (344 bytes).
    Removing enc_speed.o(.ARM.exidx.text.SimulateABZEncoder), (8 bytes).
    Removing enc_speed.o(.bss.g_last_z_detect_time), (4 bytes).
    Removing enc_speed.o(.bss.g_encoder), (392 bytes).
    Removing enc_speed.o(.bss.signed_speed_rps), (4 bytes).
    Removing enc_speed.o(.bss.speed_buffer), (128 bytes).
    Removing enc_speed.o(.bss.SimulateABZEncoder.current_sim_speed), (4 bytes).
    Removing enc_speed.o(.bss.SimulateABZEncoder.harmonic_phase_accumulator), (4 bytes).
    Removing enc_speed.o(.bss.SimulateABZEncoder.accumulated_revolutions), (4 bytes).
    Removing enc_speed.o(.bss.AdaptiveKalmanParams.last_state), (1 bytes).
    Removing adc_pmsm.o(.text), (0 bytes).
    Removing adc_pmsm.o(.text.ADC_FilterManager_Init), (62 bytes).
    Removing adc_pmsm.o(.ARM.exidx.text.ADC_FilterManager_Init), (8 bytes).
    Removing adc_pmsm.o(.text.ADC_FilterManager_Reset), (46 bytes).
    Removing adc_pmsm.o(.ARM.exidx.text.ADC_FilterManager_Reset), (8 bytes).
    Removing adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_Init), (8 bytes).
    Removing adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_CalibrateOffset), (8 bytes).
    Removing adc_pmsm.o(.text.ADC_PMSM_IsCalibrationCompleted), (12 bytes).
    Removing adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_IsCalibrationCompleted), (8 bytes).
    Removing adc_pmsm.o(.text.ADC_PMSM_ProcessCurrents), (384 bytes).
    Removing adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_ProcessCurrents), (8 bytes).
    Removing adc_pmsm.o(.ARM.exidx.text.ADC_PMSM_ProcessRectifiedAverage), (8 bytes).
    Removing pt1000_table.o(.text), (0 bytes).
    Removing pt1000_table.o(.ARM.exidx.text.PT1000_ADC_to_Temperature), (8 bytes).
    Removing pt1000_table.o(.text.PT1000_Temperature_to_ADC), (228 bytes).
    Removing pt1000_table.o(.ARM.exidx.text.PT1000_Temperature_to_ADC), (8 bytes).
    Removing sensor_proc.o(.text), (0 bytes).
    Removing sensor_proc.o(.ARM.exidx.text.sensor_proc_init), (8 bytes).
    Removing sensor_proc.o(.ARM.exidx.text.sensor_proc_task), (8 bytes).
    Removing sensor_proc.o(.text.sensor_get_result), (64 bytes).
    Removing sensor_proc.o(.ARM.exidx.text.sensor_get_result), (8 bytes).
    Removing sensor_proc.o(.ARM.exidx.text.sensor_get_value), (8 bytes).
    Removing sensor_proc.o(.text.sensor_get_status), (26 bytes).
    Removing sensor_proc.o(.ARM.exidx.text.sensor_get_status), (8 bytes).
    Removing sensor_proc.o(.text.sensor_get_raw_adc), (28 bytes).
    Removing sensor_proc.o(.ARM.exidx.text.sensor_get_raw_adc), (8 bytes).
    Removing sensor_proc.o(.text.sensor_reset_error_count), (28 bytes).
    Removing sensor_proc.o(.ARM.exidx.text.sensor_reset_error_count), (8 bytes).
    Removing sensor_proc.o(.text.sensor_get_debug_info), (20 bytes).
    Removing sensor_proc.o(.ARM.exidx.text.sensor_get_debug_info), (8 bytes).
    Removing sensor_proc.o(.ARM.exidx.text.sensor_set_dma_ready), (8 bytes).
    Removing sys_timerevent.o(.text), (0 bytes).
    Removing sys_timerevent.o(.ARM.exidx.text.TimerEvent_Init), (8 bytes).
    Removing sys_timerevent.o(.ARM.exidx.text.TimerEvent_Handler), (8 bytes).
    Removing low_speed_alarm.o(.text), (0 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_Init), (70 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_Init), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_Task), (596 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_Task), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_Reset), (60 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_Reset), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_IsActive), (18 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_IsActive), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_GetSystemState), (12 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetSystemState), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_GetFaultReason), (12 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetFaultReason), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_GetMotorSpeed), (12 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetMotorSpeed), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_GetMotorTemp), (32 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetMotorTemp), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_GetBoardTemp), (32 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_GetBoardTemp), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_Get270VVoltage), (36 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_Get270VVoltage), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_IsMotorRunning), (4 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_IsMotorRunning), (8 bytes).
    Removing low_speed_alarm.o(.text.Low_Speed_Alarm_HasProtectionFault), (48 bytes).
    Removing low_speed_alarm.o(.ARM.exidx.text.Low_Speed_Alarm_HasProtectionFault), (8 bytes).
    Removing low_speed_alarm.o(.bss.g_alarm_data), (28 bytes).
    Removing protection_monitor.o(.text), (0 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_Init), (116 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_Init), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_Tick), (474 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_Tick), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_GetStatus), (44 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetStatus), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_Reset), (48 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_Reset), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_ResetAll), (58 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_ResetAll), (8 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetValue), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_GetStats), (90 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetStats), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_GetFaultRecordCount), (30 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetFaultRecordCount), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_GetFaultRecord), (116 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetFaultRecord), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_ClearFaultRecords), (58 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_ClearFaultRecords), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_GetLatestFaultRecord), (92 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetLatestFaultRecord), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_FindFaultRecord), (240 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_FindFaultRecord), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_GetMotorSpeed), (12 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetMotorSpeed), (8 bytes).
    Removing protection_monitor.o(.text.Protection_Monitor_GetTorqueCurrent), (12 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.Protection_Monitor_GetTorqueCurrent), (8 bytes).
    Removing protection_monitor.o(.text.get_bus_voltage), (36 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.get_bus_voltage), (8 bytes).
    Removing protection_monitor.o(.text.get_bus_current), (12 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.get_bus_current), (8 bytes).
    Removing protection_monitor.o(.text.get_motor_temperature), (84 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.get_motor_temperature), (8 bytes).
    Removing protection_monitor.o(.text.get_driver_temperature), (32 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.get_driver_temperature), (8 bytes).
    Removing protection_monitor.o(.text.get_current_imbalance), (152 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.get_current_imbalance), (8 bytes).
    Removing protection_monitor.o(.text.get_stall_detection), (12 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.get_stall_detection), (8 bytes).
    Removing protection_monitor.o(.text.get_motor_speed), (12 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.get_motor_speed), (8 bytes).
    Removing protection_monitor.o(.text.get_stall_dynamic_delay_warn), (6 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.get_stall_dynamic_delay_warn), (8 bytes).
    Removing protection_monitor.o(.text.get_stall_dynamic_delay_fault), (6 bytes).
    Removing protection_monitor.o(.ARM.exidx.text.get_stall_dynamic_delay_fault), (8 bytes).
    Removing protection_monitor.o(.bss.event_handler), (4 bytes).
    Removing protection_monitor.o(.bss.total_trigger_count), (4 bytes).
    Removing protection_monitor.o(.bss.fault_record_count), (1 bytes).
    Removing protection_monitor.o(.bss.fault_record_index), (1 bytes).
    Removing protection_monitor.o(.bss.fault_records), (512 bytes).
    Removing protection_monitor.o(.rodata.param_configs), (128 bytes).
    Removing protection_monitor.o(.rodata.bus_overvolt_rules), (32 bytes).
    Removing protection_monitor.o(.rodata.bus_undervolt_rules), (32 bytes).
    Removing protection_monitor.o(.rodata.bus_overcurrent_rules), (32 bytes).
    Removing protection_monitor.o(.rodata.motor_temp_rules), (32 bytes).
    Removing protection_monitor.o(.rodata.driver_temp_rules), (32 bytes).
    Removing protection_monitor.o(.rodata.current_imbalance_rules), (32 bytes).
    Removing protection_monitor.o(.rodata.stall_rules), (32 bytes).
    Removing protection_monitor.o(.rodata.overspeed_rules), (32 bytes).
    Removing power_stage_test.o(.text), (0 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_Execute), (724 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_Execute), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_ResetResult), (58 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ResetResult), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_GetDetailedResult), (32 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDetailedResult), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_CheckBusVoltage), (64 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_CheckBusVoltage), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_StartADCSampling), (64 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StartADCSampling), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_StopADCSampling), (24 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_StopADCSampling), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_GetADCSamples), (54 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetADCSamples), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_AnalyzeCurrentConsistency), (82 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_AnalyzeCurrentConsistency), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_GetDiagnosisInfo), (648 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetDiagnosisInfo), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_IsSoftFault), (14 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_IsSoftFault), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_ExportRawADCData), (280 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportRawADCData), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_ExportCurrentAnalysis), (312 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ExportCurrentAnalysis), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_GetStatistics), (528 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_GetStatistics), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_PWMCycleTiming), (126 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_PWMCycleTiming), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_ShowPWMConfig), (2 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ShowPWMConfig), (8 bytes).
    Removing power_stage_test.o(.text.Power_Stage_Test_ValidatePWMConfig), (4 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.Power_Stage_Test_ValidatePWMConfig), (8 bytes).
    Removing power_stage_test.o(.text.pst_set_device_pwm), (140 bytes).
    Removing power_stage_test.o(.ARM.exidx.text.pst_set_device_pwm), (8 bytes).
    Removing power_stage_test.o(.bss.g_test_result), (640 bytes).
    Removing power_stage_test.o(.bss.g_adc_sampling_active), (1 bytes).
    Removing power_stage_test.o(.rodata.str1.1), (514 bytes).
    Removing power_stage_test.o(.bss.g_adc_sample_count), (1 bytes).
    Removing power_stage_test.o(.bss.g_current_test_phase), (1 bytes).
    Removing arm_cos_f32.o(.text), (0 bytes).
    Removing arm_cos_f32.o(.text.arm_cos_f32), (148 bytes).
    Removing arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32), (8 bytes).
    Removing arm_sin_f32.o(.text), (0 bytes).
    Removing arm_sin_f32.o(.text.arm_sin_f32), (140 bytes).
    Removing arm_sin_f32.o(.ARM.exidx.text.arm_sin_f32), (8 bytes).
    Removing arm_mat_add_f32.o(.text), (0 bytes).
    Removing arm_mat_add_f32.o(.text.arm_mat_add_f32), (178 bytes).
    Removing arm_mat_add_f32.o(.ARM.exidx.text.arm_mat_add_f32), (8 bytes).
    Removing arm_mat_init_f32.o(.text), (0 bytes).
    Removing arm_mat_init_f32.o(.text.arm_mat_init_f32), (8 bytes).
    Removing arm_mat_init_f32.o(.ARM.exidx.text.arm_mat_init_f32), (8 bytes).
    Removing arm_mat_mult_f32.o(.text), (0 bytes).
    Removing arm_mat_mult_f32.o(.text.arm_mat_mult_f32), (284 bytes).
    Removing arm_mat_mult_f32.o(.ARM.exidx.text.arm_mat_mult_f32), (8 bytes).
    Removing arm_copy_f32.o(.text), (0 bytes).
    Removing arm_copy_f32.o(.text.arm_copy_f32), (66 bytes).
    Removing arm_copy_f32.o(.ARM.exidx.text.arm_copy_f32), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevTable), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable16), (40 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable32), (96 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable128), (416 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable256), (880 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable512), (896 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable1024), (3600 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable2048), (7616 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_32), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_64), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_256), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_1024), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_4096), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_f32), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).

1403 unused section(s) (total 973342 bytes) removed from the image.
