/**
  * @file SysCtl_SysMoore.c
  * @brief 系统状态机
  * @version 0.1
  * 
  * @copyright Copyright (c) 2025
  */

/*----------------------------------include-----------------------------------*/
#include "SysCtl_AllHeaders.h"
#include "at32a403a.h"
#include "at32a403a_wk_config.h"
#include "AnoPTv8.h"
#include "SysFSM.h"
/*----------------------------------variable----------------------------------*/
extern Filter Filter_udch;
extern Filter Filter_udcl;
extern Filter Filter_idc;
extern Filter Filter_idc1;
extern Filter Filter_idc2;
extern Filter Filter_speed;
extern int Fault_recordFlag;
// extern PIREG PhrPI;
/*----------------------------------function----------------------------------*/
extern void fnSystemResetParam(void);
extern void fnSysRatedParameter(void);
void fnSysFaultReset();
//extern void fnParaUpdateHVOFF_SysSamScaParameter(void);
void fnSystemReadyParam(void);
void fnSystemRUNParam(void);
void fnVarCopyToRam(uint16_t *HeadAddr,float *pVarAddr,uint16_t AddrBias);

/**
  * @brief 状态执行 - 优化版
  * @param  p: 电机系统状态结构体
  * @version 0.3
  */
void fnSysMooreCal(TypeSysMoore *p)
{
    // 1. 故障状态处理
    if(SysErrIndexReg.bit.SysHFaultFlag == TRUE ) {
        // 故障状态处理模块
        p->SysStateNew = SysErr;                // 设置故障状态
        g_SystemStatus.cmd.bits.motor_start = FALSE; // 清除启动指令
        tmr_output_enable(TMR1, FALSE);         // 禁止PWM输出
        
        // 检查是否需要复位
        if(g_SystemStatus.cmd.bits.motor_reset == TRUE) {  // 收到复位指令
            // 故障复位程序
            fnSysFaultReset();                  // 故障复位
            fnSystemResetParam();               // 系统参数复位
            SysErrIndexReg.bit.SysHFaultFlag = FALSE; // 清除故障标志
            p->SysStateNew = SystemReady;       // 切换至就绪状态
            g_SystemStatus.cmd.bits.motor_reset = FALSE;   // 清除复位指令
            #ifdef USB_PRINTF_ENABLE
            Usb_printf(LOG_COLOR_GREEN,"电机复位状态。\n");
            #endif
        }
        return;  // 故障状态处理完直接返回
    }
    
    // 2. 正常状态处理
    if(p->SysStateNew == SystemRun) {
        // 运行状态处理模块
        tmr_output_enable(TMR1, TRUE);          // 使能PWM输出
        
        // 计算梯形加减速曲线，提高速度控制平滑性
        //CalcTRAP(&trapeLine, *pCsvParamSpeed_ref, 500);
        if(g_SystemStatus.cmd.bits.motor_stop != TRUE) 
        {  
        fnSystemRUNParam();                     // 更新运行参数       
  
        }
        // 检查是否需要停止
         else if(g_SystemStatus.cmd.bits.motor_stop == TRUE) {  // 收到停止指令（使用SystemCmd结构体）
            // 停止处理
            //*pCsvParamSpeed_ref = 0.0f;
            SynMotorVc.speed_ref = 0.0f;
            p->SysStateNew = SystemReady;       // 切换至就绪状态
            g_SystemStatus.cmd.bits.motor_start = FALSE;   // 清除启动指令（使用SystemCmd结构体）
            // if((SynMotorVc.speedback_flt <= 10.0f)&&(SynMotorVc.speedback_flt >= -10.0f))
            // {
            //   p->SysStateNew = SystemReady;       // 切换至就绪状态
            //  g_SystemStatus.cmd.bits.motor_start = FALSE;   // 清除启动指令（使用SystemCmd结构体）
            // }
            
            // #ifdef USB_PRINTF_ENABLE
            // Usb_printf(LOG_COLOR_GREEN,"电机就绪状态。\n");
            // #endif
        }
    }
    else {  // SystemReady或其他非运行状态
        // 就绪状态处理模块
        tmr_output_enable(TMR1, FALSE);         // 禁止PWM输出
        fnSystemReadyParam();                   // 更新就绪参数
        fnSystemResetParam();
        SynMotorVc.isd1=0;
        SynMotorVc.isq1=0;
        // 检查是否需要启动
        if(g_SystemStatus.cmd.bits.motor_start == TRUE) {  // 收到启动指令（使用SystemCmd结构体）
            // 启动处理
            p->SysStateNew = SystemRun;         // 切换至运行状态
            g_SystemStatus.cmd.bits.motor_stop = FALSE;    // 清除停止指令（使用SystemCmd结构体）
            
            #ifdef USB_PRINTF_ENABLE
            Usb_printf(LOG_COLOR_GREEN,"电机运行状态。\n");
            #endif
        }
    }
}

// /**
//   * @brief 状态执行 - 优化版
//   * @param  p: 电机系统状态结构体
//   * @version 0.2
//   */
// void fnSysMooreCal(TypeSysMoore *p)
// {
//     // 1. 故障状态处理
//     if(SysErrIndexReg.bit.SysHFaultFlag == TRUE ) {
//         // 故障状态处理模块
//         p->SysStateNew = SysErr;                // 设置故障状态
//         SysCtlReg.bit.StartInstr = FALSE;       // 清除启动指令
//         tmr_output_enable(TMR1, FALSE);         // 禁止PWM输出
        
//         // 检查是否需要复位
//         if(SysCtlReg.bit.ResetInstr == TRUE) {  // 收到复位指令
//             // 故障复位程序
//             fnSysFaultReset();                  // 故障复位
//             fnSystemResetParam();               // 系统参数复位
//             SysErrIndexReg.bit.SysHFaultFlag = FALSE; // 清除故障标志
//             p->SysStateNew = SystemReady;       // 切换至就绪状态
//             SysCtlReg.bit.ResetInstr = FALSE;   // 清除复位指令
//             #ifdef USB_PRINTF_ENABLE
//             Usb_printf(LOG_COLOR_GREEN,"电机复位状态。\n");
//             #endif
//         }
//         return;  // 故障状态处理完直接返回
//     }
    
//     // 2. 正常状态处理
//     if(p->SysStateNew == SystemRun) {
//         // 运行状态处理模块
//         fnSystemRUNParam();                     // 更新运行参数
//         tmr_output_enable(TMR1, TRUE);          // 使能PWM输出
//         gpio_bits_set(GPIOE, CON_RST_PIN);      // 使能光耦电平转换
        
//         // 计算梯形加减速曲线，提高速度控制平滑性
//         //CalcTRAP(&trapeLine, SynMotorVc.speed_ref, 500);
        
//         // 检查是否需要停止
//         if(SysCtlReg.bit.StopInstr == TRUE) {
//             // 停止处理
//             p->SysStateNew = SystemReady;       // 切换至就绪状态
//             SysCtlReg.bit.StartInstr = FALSE;   // 清除启动指令
            
//             #ifdef USB_PRINTF_ENABLE
//             Usb_printf(LOG_COLOR_GREEN,"电机就绪状态。\n");
//             #endif
//         }
//     }
//     else {  // SystemReady或其他非运行状态
//         // 就绪状态处理模块
//         tmr_output_enable(TMR1, FALSE);         // 禁止PWM输出
//         gpio_bits_reset(GPIOE, CON_RST_PIN);    // 禁止光耦电平转换
//         fnSystemReadyParam();                   // 更新就绪参数
        
//         // 检查是否需要启动
//         if(SysCtlReg.bit.StartInstr == TRUE) {
//             // 启动处理
//             p->SysStateNew = SystemRun;         // 切换至运行状态
//             SysCtlReg.bit.StopInstr = FALSE;    // 清除停止指令
            
//             #ifdef USB_PRINTF_ENABLE
//             Usb_printf(LOG_COLOR_GREEN,"电机运行状态。\n");
//             #endif
//         }
//     }
// }

// /**
//   * @brief 状态执行 原版
//   * @param  p: 电机系统状态结构体
//   * @version 0.1
//   */
// void fnSysMooreCal(TypeSysMoore *p)
// {
// 	if(SysErrIndexReg.bit.SysHFaultFlag == TRUE)//系统重故障
//     {
// 		p->SysStateNew = SysErr;//系统故障状态
//     //SysCtlReg.bit.StartInstr = FALSE;
// 		tmr_output_enable(TMR1, FALSE);  //禁止PWM输出
// 		if(SysCtlReg.bit.ResetInstr == TRUE)//系统复位,等效硬件复位
// 		{
// 			fnSysFaultReset();
// 			fnSystemResetParam();
// 			SysErrIndexReg.bit.SysHFaultFlag = FALSE;
// 			p->SysStateNew = SystemReady;
// 			SysCtlReg.bit.ResetInstr = FALSE;
// 		}
//     }
// 	else//非故障状态逻辑切换
//     {
// 		if(p->SysStateNew==SystemRun)
// 		{      
// 			fnSystemRUNParam();
// 			tmr_output_enable(TMR1, TRUE);  //使能PWM输出
//       gpio_bits_set(GPIOE, CON_RST_PIN); //使能PWM电平转换模块
//       CalcTRAP(&trapeLine, SynMotorVc.speed_ref, 500);
// 			if(SysCtlReg.bit.StopInstr == TRUE)
// 			{
// 				p->SysStateNew = SystemReady;   //切换系统到运行状态
//         //SysCtlReg.bit.StartInstr = FALSE;
//         Usb_printf(LOG_COLOR_GREEN,"电机就绪状态。\n");
//       }
// 		}
// 		else
// 		{
// 			tmr_output_enable(TMR1, FALSE);  //禁止PWM输出
//       gpio_bits_reset(GPIOE, CON_RST_PIN); //禁止PWM电平转换模块
// 			fnSystemReadyParam();     //Ready状态参数更新
// 			if(SysCtlReg.bit.StartInstr == TRUE)
// 			{   
// 				p->SysStateNew = SystemRun;   //切换系统到运行状态
//         SysCtlReg.bit.StopInstr = FALSE;
//         Usb_printf(LOG_COLOR_GREEN,"电机运行状态。\n");
//       }           
//     }
//  	}
// }
// /**
//   * @brief 故障状态处理接口函数
//   * @param  p: 电机系统状态结构体
//   * @version 0.1
//   */
// void MotorFaultProcess(TypeSysMoore *p)
// {
// 	p->SysStateNew = SysErr;//系统故障状态
//   SysCtlReg.bit.StartInstr = FALSE;
// 		tmr_output_enable(TMR1, FALSE);  //禁止PWM输出
// 		gpio_bits_reset(GPIOE, CON_RST_PIN);
// 		if(SysCtlReg.bit.ResetInstr == TRUE)//系统复位,等效硬件复位
// 		{
// 			fnSysFaultReset();
// 			fnSystemResetParam();
// 			SysErrIndexReg.bit.SysHFaultFlag = FALSE;
// 			p->SysStateNew = SystemReady;
// 			SysCtlReg.bit.ResetInstr = FALSE;
// 		}
// }

// /**
//   * @brief 电机就绪状态接口函数
//   * @param  p: 电机系统状态结构体
//   * @version 0.1
//   */
// void MotorReadyStatus(TypeSysMoore *p)
// {
// 	if (p->SysStateNew == SystemReady)
//     {
        
//         fnSystemReadyParam();              // Ready状态参数更新
//         if (SysCtlReg.bit.StartInstr == TRUE)
//         {
//             p->SysStateNew = SystemRun; // 切换系统到运行状态
//             SysCtlReg.bit.StopInstr = FALSE;
// 			      tmr_counter_enable(TMR1, TRUE);
//             tmr_output_enable(TMR1, TRUE);       // 使能PWM输出
//             gpio_bits_set(GPIOE, CON_RST_PIN); // 使能PWM电平转换模块
//         }
//     }
	
// }

// /**
//   * @brief 电机运行状态接口函数
//   * @param  p: 电机系统状态结构体
//   * @version 0.1
//   */
// void MotorRunStatus(TypeSysMoore *p)
// {
//     if (p->SysStateNew == SystemRun)
//     {
//         fnSystemRUNParam();
        
//         if (SysCtlReg.bit.StopInstr == TRUE)
//         {
//             p->SysStateNew = SystemReady; // 切换系统到运行状态
//             SysCtlReg.bit.StartInstr = FALSE;
//             // tmr_counter_enable(TMR1, FALSE);
//             tmr_output_enable(TMR1, FALSE);    // 禁止PWM输出
//             gpio_bits_reset(GPIOE, CON_RST_PIN); // 禁止PWM电平转换模块
//         }
//     }
//     else
//     {
//     }
// }


/**
  * @brief 故障复位，内部变量复位
  * @version 0.1
  */
void fnSysFaultReset(void)
{
   SysErrIndexReg.all = 0;
   ARMFaultReg.all = 0;
   DSPFaultCodeReg.all = 0;
   SysCtlModeREG.all=0;
}



/**
  * @brief 参数初始化
  * @param  p: 电机系统状态结构体
  * @version 0.1
  */
void fnSystemInitParam(void)
{
	/*初始参数设置*/
	RotorSpeedclc.speedclc_countmax = 2;  //转速最大采样周期数
	
	/*初始参数设置结束*/

	SysErrIndexReg.all = 0;
	ARMFaultReg.all = 0;
	DSPFaultCodeReg.all = 0;
	SysCtlModeREG.all=0;
  SysCtlReg.all = 0;
  SysSampOffset.uOffsetCounter = 0;
	SysSampOffset.uOffsetCount = 0;
	SysSampOffset.SampOffsetOnce = 0; 
	SysSampOffset.pfnSysOffsetInit(&SysSampOffset);
	SynMotorVc.init(&SynMotorVc);
	//Fault_recordFlag = DSPFaultCodeReg.all;
	// vDSP_GPWM_ENABLE->all = 0;
	// vDSP_GPWM_ENABLE1->all = 0;

//	SysVoltBase.pfnInit(&SysVoltBase);// 基波电压接口初始化
//	SysVoltBalance.init(&SysVoltBalance);               // 基波电压接口初始化
	//SysVVVFCtrl.pfnInit(&SysVVVFCtrl);                  // VVVF控制初始化
	                       //矢量控制初始化
	
	//fnInitInterg_Encode();//无速度传感程序暂时不用

	//SysVVVFCtrl.fVFPhseCoffic =  0.0157; //lcgai20171127
	//====================滤波初始化========================
    Filter_idc.wc = 4000;
    Filter_idc.Tc = 0.0001;
    Filter_idc1.wc = 4000;
    Filter_idc1.Tc = 0.0001;
    Filter_idc2.wc = 4000;
    Filter_idc2.Tc = 0.0001;
    Filter_udch.wc = 4000;
    Filter_udch.Tc = 0.00005;
    Filter_udcl.wc = 4000;
    Filter_udcl.Tc = 0.00005;
//	SysVoltBase.uCarrCount = 25000; //lcgai20171127
    Filter_speed.wc = 200;
    Filter_speed.Tc = 0.00005;
}

/**
  * @brief 参数复位
  * @version 0.1
  */
void fnSystemResetParam(void)
{
//	SysVoltBase.pfnReset(&SysVoltBase);                     // 基波电压接口复位
	// SysVVVFCtrl.pfnReset(&SysVVVFCtrl);                     // VVVF控制参数复位
    SynMotorVc.reset(&SynMotorVc);                          //矢量控制初始化

}

/**
  * @brief 系统待机状态参数更新
  * @version 0.1
  */
void fnSystemReadyParam(void)
{
    float fFreqStart = 0.0;
    FreqEn_DSPtoARM  = 0;
	  fnSysRatedParameter();//额定参数更新
    SysBaseValue.pfnSysBaseValueCal(&SysBaseValue);//系统基值计算,SysCtl_Drive.c
    fnParaUpdateSysSamScaParameter();//采样整定系数计算	
  	SysCtlParameter.uStartMode = (uint16_t)*pCsvParaStartMod; //控制方式
   
    //	fnARMFaultDetec();//ARM侧故障检测
    // SysConTest.fVoltageAmp_temp1 = 0.0;//带电抗器输出幅值清零。
    // SysConTest.fVoltageAmp_temp2 = 0.0;//带电抗器输出幅值清零。
    //===========零漂使能============
    //	SysSampOffset.uSampleOffsetEn = (uint16_t)*pCsvParaSampleOffset; //零漂使能
    //
    //	SysSampOffset.fK[0] = *pCsvParamSample1K;
    //    SysSampOffset.fB[0] = *pCsvParamSample1B;
    //    SysSampOffset.fK[1] = *pCsvParamSample2K;
    //    SysSampOffset.fB[1] = *pCsvParamSample2B;
    //    SysSampOffset.fK[2] = *pCsvParamSample3K;
    //    SysSampOffset.fB[2] = *pCsvParamSample3B;
    //    SysSampOffset.fK[3] = *pCsvParamSample4K;
    //    SysSampOffset.fB[3] = *pCsvParamSample4B;
    //    SysSampOffset.fK[4] = *pCsvParamSample5K;
    //    SysSampOffset.fB[4] = *pCsvParamSample5B;
    //    SysSampOffset.fK[5] = *pCsvParamSample6K;
    //    SysSampOffset.fB[5] = *pCsvParamSample6B;
	
	//===========运行环境参数设定============
	SysEnviConfg.uConRunType = (uint16_t)*pCsvParaConRunType;
	SysEnviConfg.uConStrat = (TypeSysControlStrat)*pCsvParaConStrategy;
	SysEnviConfg.uVFDLoadType = (TypeSysVFDLoad)*pCsvParaVFDLoadType;

	//===========VF曲线参数设定============
	fFreqStart = *pCsvParamFreqStart;
	// SysVVVFCtrl.fFreqRefMin = fFreqStart/SysRatedParameter.fMotorRatedFre;
	// SysVVVFCtrl.fVoltRefMin = *pCsvParamVF_VoltMin;
	// SysVVVFCtrl.fFreqRefPoint1 = *pCsvParamVF_FreqPoint1;
	// SysVVVFCtrl.fVoltRefPoint1 = *pCsvParamVF_VoltPoint1;
	// SysVVVFCtrl.fFreqRefPoint2 = *pCsvParamVF_FreqPoint2;
	// SysVVVFCtrl.fVoltRefPoint2 = *pCsvParamVF_VoltPoint2;
	// SysVVVFCtrl.fFreqRefBase = *pCsvParamVF_FreqMax;
	// SysVVVFCtrl.fVoltRefMax = *pCsvParamVF_VoltMax;

	//fnInitVCinput_off();  //无速度传感控制相关暂时不用

//	SysVoltBase.fUu_base =0.0;
//	SysVoltBase.fUv_base =0.0;
//	SysVoltBase.fUw_base =0.0;
	//===========运行参数更新============
   para.Udq_max = *pCsvParamSynVcUqmax;          //设置电流PI输出电压限幅
   para.Idq_max = *pCsvParamSynVcIqmax;          //设置速度PI输出限幅
   SynMotorVc.init(&SynMotorVc);                       //矢量控制初始化
	//fnSystemRUNParam();
}

/**
  * @brief 系统运行状态参数更新
  * @version 0.1
  */
void fnSystemRUNParam(void)
{
   SysCtlReg.all = (uint16_t)*pCsvParamSysCtlWord;
   SysProParamReg.fOCProtect = 120.0f;  //电机瞬时保护峰值
   SynMotorVc.speed_ref = *pCsvParamSpeed_ref;       //速度给定
   SynMotorVc.controlLoopMode = (unsigned int)*pCsvControlLoopMode;  //控制环路选择
}
/***********************************************************
//函数名称：fnSysParamterRef
//函数功能：非周期性参数刷新
//函数参数：无
************************************************************/

/**
  * @brief 非周期性参数刷新
  * @version 0.1
  */
void fnSysParamterRef(void)
{
    if(*pARMParamRdFlag == 0xce)
    {
      if(SysSampOffset.SampOffsetOnce == 0)
      {
     	//SysSampOffset.pfnSysOffsetInit(&SysSampOffset);
     	SysSampOffset.SampOffsetOnce = 1;
      }
    }
    *RdDSPtoARM = 0xfa;//DSP主循环正常运行的标志字

}

/**
  * @brief 参数传递基本函数
  * @param  HeadAddr: 目标起始地址
  * @param  pVarAddr: 被传递参数起始地址
  * @param  AddrBias: 偏移地址
  * @version 0.1
  */
void fnVarCopyToRam(uint16_t *HeadAddr,float *pVarAddr,uint16_t AddrBias)
{
	uint16_t *pParaAddr;
	uint16_t uParaL,uParaH;
	pParaAddr = (uint16_t*)pVarAddr;
	uParaL = *pParaAddr;
	uParaH = *(pParaAddr + 1);
	*(HeadAddr + AddrBias) = uParaL;
	*(HeadAddr + AddrBias + 1) = uParaH;
}

/**
  * @brief 额定参数更新
  * @version 0.1
  */
void fnSysRatedParameter(void)                                                                        
{                                                                                                      
	 SysRatedParameter.fMotorRatedVoltage = *pCsvParamMotorRatedVoltage;  //电机额定电压                        
	 SysRatedParameter.fMotorRatedCurrent = *pCsvParamMotorRatedCurrent;  //电机额定电流                        
	 SysRatedParameter.fMotorRatedFre = *pCsvParamMotorRatedFre;          //电机额定频率                        
	 SysRatedParameter.fMotorRatedPower = *pCsvParamMotorRatedPower;      //电机额定功率                        
	 SysRatedParameter.fMotorRateSpeed = *pCsvParamMotorRateSpeed;        //电机额定转速                        
	 SysRatedParameter.fMotorPoleNum = *pCsvParamMotorPoleNum;            //电机极对数                          
	 SysRatedParameter.fMotorRatedSlip = *pCsvParamMotorRatedSlip;        //电机额定滑差                           
}

/**
  * @brief HMI通讯接口初始化
  * @version 0.1
  */
void fnSysHMICommInit(void)
{
//	sInComm.u2LineNumber = 11;  //
	                            //    
//	sInComm.u2SerialNumber = 11;//
	                            //
//	sInComm.u2ProductNumber = 8;//主控程序版本号008 V06
	                            //
//	sInComm.u2FactoryNumber = 6;//
	                            //
	// sInComm.DSP_RAM = &HMIBuffer[0];//读取地址

	                           //
//	sInComm.ARMHandshakeWord=(uint16_t *)0x2803FE;
//	sInComm.DSPHandshakeWord=(uint16_t *)0x2803FF;

//	sInComm.ReceiveStartAdd=(uint16_t *)0x280000;
//	sInComm.DSPtoARMStartAdd=(uint16_t *)0x280000;
//	sInComm.WaveID_ADD=(uint16_t *)0x280000;
//	sInComm.WaveData_StartADD=(uint16_t *)0x280000;
//    sInComm.pfnInit(&sInComm);
}

/*------------------------------------test------------------------------------*/


//===========================================================================
// No more.
//===========================================================================
