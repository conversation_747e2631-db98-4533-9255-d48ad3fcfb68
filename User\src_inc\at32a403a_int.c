/* includes ------------------------------------------------------------------*/
#include "at32a403a_int.h"
#include "usb_app.h"
#include "Sys_TimerEvent.h"
#include "sensor_proc.h"

static uint16_t TIME_ADC1_cnt500us = 0;

/**
  * @brief  this function handles nmi exception.
  * @param  none
  * @retval none
  */
void NMI_Handler(void)
{

}

/**
  * @brief  this function handles hard fault exception.
  * @param  none
  * @retval none
  */
void HardFault_Handler(void)
{

  while (1)
  {

  }
}

/**
  * @brief  this function handles memory manage exception.
  * @param  none
  * @retval none
  */
void MemManage_Handler(void)
{

  while (1)
  {

  }
}

/**
  * @brief  this function handles bus fault exception.
  * @param  none
  * @retval none
  */
void BusFault_Handler(void)
{

  while (1)
  {

  }
}

/**
  * @brief  this function handles usage fault exception.
  * @param  none
  * @retval none
  */
void UsageFault_Handler(void)
{

  while (1)
  {

  }
}

/**
  * @brief  this function handles svcall exception.
  * @param  none
  * @retval none
  */
void SVC_Handler(void)
{

}

/**
  * @brief  this function handles debug monitor exception.
  * @param  none
  * @retval none
  */
void DebugMon_Handler(void)
{

}

/**
  * @brief  this function handles pendsv_handler exception.
  * @param  none
  * @retval none
  */
void PendSV_Handler(void)
{

}

/**
  * @brief  this function handles systick handler.
  * @param  none
  * @retval none
  */
void SysTick_Handler(void)
{

}

/**
  * @brief  this function handles DMA1 Channel 1 handler.
  * @param  none
  * @retval none
  */
void DMA1_Channel1_IRQHandler(void)
{
    // 检查DMA传输完成中断标志
    if (dma_interrupt_flag_get(DMA1_FDT1_FLAG) != RESET) {
        // 清除DMA传输完成中断标志
        dma_flag_clear(DMA1_FDT1_FLAG);

        // 设置传感器处理模块的DMA数据就绪标志
        sensor_set_dma_ready();
    }
}

/**
  * @brief  this function handles ADC1 & ADC2 handler.
  * @param  none
  * @retval none
  */
void ADC1_2_IRQHandler(void)
{

}

/**
  * @brief  this function handles TMR1 Brake and TMR9 handler.
  * @param  none
  * @retval none
  */
void TMR1_BRK_TMR9_IRQHandler(void)
{

}

/**
  * @brief  this function handles TMR1 Overflow and TMR10 handler.
  * @param  none
  * @retval none
  */
void TMR1_OVF_TMR10_IRQHandler(void)
{

}

/**
  * @brief  this function handles TMR6 handler.
  * @param  none
  * @retval none
  */
void TMR6_GLOBAL_IRQHandler(void)
{
  if(tmr_interrupt_flag_get(TMR6, TMR_OVF_FLAG))
  {
    tmr_flag_clear(TMR6, TMR_OVF_FLAG);
    TimerEvent_Handler();
    TIME_ADC1_cnt500us++;
    if(TIME_ADC1_cnt500us >= 20)
    {
      TIME_ADC1_cnt500us = 0;
      adc_ordinary_software_trigger_enable(ADC1, TRUE);
    }
  }

}

/**
  * @brief  this function handles USB Map Low handler.
  * @param  none
  * @retval none
  */
void USBFS_MAPL_IRQHandler(void)
{

  wk_usbfs_irq_handler();

}


