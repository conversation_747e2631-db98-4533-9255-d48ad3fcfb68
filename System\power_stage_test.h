#ifndef __POWER_STAGE_TEST_H
#define __POWER_STAGE_TEST_H

#include "at32a403a.h"
#include "system_status.h"
#include "sensor_proc.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file power_stage_test.h
 * @brief 功率级硬件自检模块 - 双重验证算法
 *
 * 功能特性：
 * 1. 双重验证机制：硬件保护检测 + ADC电流一致性分析
 * 2. 实时安全监控：TIM1硬件刹车功能始终开启
 * 3. 高精度电流采样：TMR1 CH4触发的同步ADC采样（10次）
 * 4. 软故障检测：检测轻微匝间短路、连接器接触不良等
 * 5. 双模式支持：完整系统模式和仅核心板验证模式
 *
 * 移植指南：
 * 1. 修改TMR硬件配置宏（第30-45行）以适配目标平台
 * 2. 修改PWM通道配置宏（第47-55行）以匹配目标硬件
 * 3. 配置PWM时序参数（第71-79行）：
 *    - PST_PWM_PERIOD_COUNTS: 设置PWM周期值（如4799，实际周期为4799+1=4800）
 *    - PST_PWM_PERIOD_US: 设置对应的周期时间（us，如50us）
 *    - PST_DEVICE_TEST_PULSE_US: 设置功率器件测试脉冲宽度（us）
 *    - PST_PHASE_TEST_PULSE_US: 设置相位测试脉冲宽度（us）
 * 4. 修改PWM周期检测接口宏以适配不同的周期检测方式：
 *    - 当前：基于ADC计算完成标识（ADC2+TMR1 CH4触发）
 *    - 可选：基于TMR1 CH4中断计数、DMA完成标识等
 * 5. 确保目标平台支持相应的定时器刹车功能和周期检测机制
 * 6. 集成adc_pmsm模块或提供等效的电流采样接口
 *
 * 配置示例：
 * - PWM周期值4799（4800计数），周期时间50us
 * - 0.5us脉冲宽度 = 48计数值 = 1%占空比
 * - 1.3us脉冲宽度 = 125计数值 = 2.6%占空比
 */

/********** 测试模式配置宏 **********/
/**
 * @brief 功率级自检测试模式控制宏
 * @details 此宏控制自检模块的运行模式，支持两种配置：
 *
 * PST_MODE_FULL_SYSTEM (1): 完整系统模式
 * - 适用于连接了功率驱动板和电机的完整系统
 * - 执行真实的硬件检查（母线电压、ADC电流采样等）
 * - 提供完整的功率级自检功能
 *
 * PST_MODE_CORE_BOARD_ONLY (0): 仅核心板模式
 * - 适用于独立的核心板验证和开发调试
 * - 跳过硬件依赖的检查（母线电压等）
 * - 使用模拟数据进行算法验证
 * - 安全地测试PWM波形生成和自检逻辑
 */
#define PST_MODE_FULL_SYSTEM        1    // 完整系统模式
#define PST_MODE_CORE_BOARD_ONLY    0    // 仅核心板模式

// 默认模式设置：完整系统模式
#ifndef POWER_STAGE_TEST_MODE
#define POWER_STAGE_TEST_MODE       PST_MODE_FULL_SYSTEM
#endif

/********** 硬件配置宏 **********/
// TMR硬件配置宏
// 移植说明：
// 1. TMR_INSTANCE: 根据目标平台选择合适的定时器实例
// 2. TMR_BRK_FLAG: 根据目标平台的刹车标志定义
// 3. TMR_PERIOD_VALUE: 根据目标平台的定时器配置调整
#define PST_TMR_INSTANCE            TMR1                   // 定时器实例
#define PST_TMR_BRK_FLAG            TMR_BRK_FLAG           // 刹车/故障标志
#define PST_TMR_PERIOD_VALUE        4799                   // 定时器周期值
#define PST_TMR_PRESCALER           0                      // 定时器预分频值

// TMR操作宏
#define PST_TMR_CLEAR_BRK_FLAG()    tmr_flag_clear(PST_TMR_INSTANCE, PST_TMR_BRK_FLAG)
#define PST_TMR_GET_BRK_FLAG()      tmr_flag_get(PST_TMR_INSTANCE, PST_TMR_BRK_FLAG)
#define PST_TMR_COUNTER_ENABLE()    tmr_counter_enable(PST_TMR_INSTANCE, TRUE)
#define PST_TMR_COUNTER_DISABLE()   tmr_counter_enable(PST_TMR_INSTANCE, FALSE)
#define PST_TMR_OUTPUT_ENABLE()     tmr_output_enable(PST_TMR_INSTANCE, TRUE)
#define PST_TMR_OUTPUT_DISABLE()    tmr_output_enable(PST_TMR_INSTANCE, FALSE)

// PWM通道配置宏
#define PST_PWM_CHANNEL_U_HIGH      TMR_SELECT_CHANNEL_1   // U相上管通道
#define PST_PWM_CHANNEL_V_HIGH      TMR_SELECT_CHANNEL_2   // V相上管通道
#define PST_PWM_CHANNEL_W_HIGH      TMR_SELECT_CHANNEL_3   // W相上管通道

// PWM通道操作宏
#define PST_SET_PWM_DUTY(channel, duty)    tmr_channel_value_set(PST_TMR_INSTANCE, channel, duty)
#define PST_GET_PWM_DUTY(channel)          tmr_channel_value_get(PST_TMR_INSTANCE, channel)

// PWM时序基础配置宏 - 简化配置
#define PST_PWM_PERIOD_COUNTS           4799    // PWM周期值 (4799+1=4800计数值)
#define PST_PWM_PERIOD_US               50      // PWM周期时间 (us)

// 时间转换宏：us转定时器计数值
#define PST_US_TO_COUNTS(us)            ((us) * PST_PWM_PERIOD_COUNTS / PST_PWM_PERIOD_US)

// 占空比转换宏：us转PWM比较值
#define PST_US_TO_PWM_COMPARE(us)       PST_US_TO_COUNTS(us)

// 死区配置宏
#define PST_DEAD_TIME_US                2.0f    // 死区时间 (微秒)
#define PST_MIN_PULSE_FACTOR            2.5f    // 最小脉冲倍数因子（防止窄脉冲）
                                                // 功率器件测试脉冲必须 > 死区×2.5 防止被死区吞噬

// 测试参数配置宏 - 基于时间配置
#define PST_DEVICE_TEST_PULSE_US        PST_DEAD_TIME_US*PST_MIN_PULSE_FACTOR   // 功率器件测试脉冲宽度 (us)
#define PST_PHASE_TEST_PULSE_US         1.3f    // 相位测试脉冲宽度 (us)
#define PST_TEST_DELAY_US               50      // 测试间隔延时 (us)
#define PST_BUS_VOLTAGE_MIN_THRESHOLD   180.0f  // 母线电压最低阈值 (V)

// 自动计算的PWM比较值
#define PST_DEVICE_TEST_PULSE_WIDTH     PST_US_TO_PWM_COMPARE(PST_DEVICE_TEST_PULSE_US)  // 功率器件测试脉冲宽度 (计数值)
#define PST_PHASE_TEST_PULSE_WIDTH      PST_US_TO_PWM_COMPARE(PST_PHASE_TEST_PULSE_US)   // 相位测试脉冲宽度 (计数值)

// 实用工具宏
#define PST_COUNTS_TO_US(counts)        ((float)(counts) * PST_PWM_PERIOD_US / PST_PWM_PERIOD_COUNTS)  // 计数值转us
#define PST_PWM_COMPARE_TO_US(compare)  PST_COUNTS_TO_US(compare)                // PWM比较值转us
#define PST_DUTY_CYCLE_PERCENT(us)      ((float)(us) * 100.0f / PST_PWM_PERIOD_US)  // 计算占空比百分比

// 配置信息显示宏
#define PST_CONFIG_INFO_PWM_PERIOD      PST_PWM_PERIOD_US                        // PWM周期 (us)
#define PST_CONFIG_INFO_PWM_COUNTS      PST_PWM_PERIOD_COUNTS                    // PWM周期 (计数值)
#define PST_CONFIG_INFO_DEVICE_PULSE    PST_DEVICE_TEST_PULSE_WIDTH              // 功率器件脉冲 (计数值)
#define PST_CONFIG_INFO_PHASE_PULSE     PST_PHASE_TEST_PULSE_WIDTH               // 相位脉冲 (计数值)
#define PST_CONFIG_INFO_DEVICE_DUTY     PST_DUTY_CYCLE_PERCENT(PST_DEVICE_TEST_PULSE_US)  // 功率器件占空比 (%)
#define PST_CONFIG_INFO_PHASE_DUTY      PST_DUTY_CYCLE_PERCENT(PST_PHASE_TEST_PULSE_US)   // 相位占空比 (%)

// ADC同步采样配置宏
#define PST_ADC_SAMPLE_COUNT            10      // ADC采样次数
#define PST_ADC_SAMPLE_TIMEOUT_MS       100     // ADC采样超时时间 (毫秒)
#define PST_CURRENT_CONSISTENCY_THRESHOLD 0.3f  // 电流一致性阈值 (A)
#define PST_SOFT_FAULT_THRESHOLD        0.15f   // 软故障检测阈值 (A)

// 时序优化配置宏
#define PST_PWM_STABILIZATION_US        20      // PWM稳定时间 (us)
#define PST_ADC_TRIGGER_DELAY_US        5       // ADC触发延时 (us)
#define PST_SAMPLE_INTERVAL_US          50      // 采样间隔 (us)

// PWM周期计次配置宏
#define PST_PWM_CYCLE_WAIT_COUNT        10      // 等待PWM周期完成次数
#define PST_PWM_CYCLE_TIMEOUT_MS        100     // PWM周期等待超时时间 (毫秒)
#define PST_PWM_STABILIZATION_CYCLES    5       // PWM稳定等待周期数
#define PST_SAMPLE_INTERVAL_CYCLES      3       // 采样间隔等待周期数
#define PST_DEVICE_TEST_CYCLES          2       // 功率器件测试等待周期数

/********** PWM周期完成检测接口宏定义 **********/
// 移植说明：根据目标平台修改以下宏定义
// 当前实现：基于ADC计算完成标识
// 其他平台可能使用：TMR1 CH4中断计数、DMA完成标识等
#define PST_GET_PWM_CYCLE_TICK()        ADC_GET_CALCULATION_TICK()
#define PST_IS_PWM_CYCLE_COMPLETED()    ADC_IS_CALCULATION_COMPLETED()
#define PST_CLEAR_PWM_CYCLE_FLAG()      ADC_CLEAR_CALCULATION_FLAG()

// ADC初始化检查宏
#define ADC_IS_INITIALIZED()            (ADC_PMSM_IsCalibrationCompleted())

// 功率级自检专用状态码现已移至 system_status.h 中的 sys_status_t 枚举 (600-699)

/********** 功率器件枚举定义 **********/
typedef enum {
    PST_DEVICE_U_HIGH = 0,  // U相上管
    PST_DEVICE_U_LOW  = 1,  // U相下管
    PST_DEVICE_V_HIGH = 2,  // V相上管
    PST_DEVICE_V_LOW  = 3,  // V相下管
    PST_DEVICE_W_HIGH = 4,  // W相上管
    PST_DEVICE_W_LOW  = 5,  // W相下管
    PST_DEVICE_COUNT  = 6   // 功率器件总数
} pst_device_t;

/********** 相位枚举定义 **********/
typedef enum {
    PST_PHASE_U = 0,    // U相
    PST_PHASE_V = 1,    // V相
    PST_PHASE_W = 2,    // W相
    PST_PHASE_COUNT = 3 // 相位总数
} pst_phase_t;

/********** ADC采样数据结构 **********/
typedef struct {
    float ia_samples[PST_ADC_SAMPLE_COUNT];         // A相电流采样数据
    float ib_samples[PST_ADC_SAMPLE_COUNT];         // B相电流采样数据
    float ic_samples[PST_ADC_SAMPLE_COUNT];         // C相电流采样数据
    uint8_t sample_count;                           // 实际采样次数
    uint32_t sample_tick[PST_ADC_SAMPLE_COUNT];     // 采样时的计次值
} pst_adc_samples_t;

/********** 电流一致性分析结果 **********/
typedef struct {
    float ia_avg;                                   // A相平均电流
    float ib_avg;                                   // B相平均电流
    float ic_avg;                                   // C相平均电流
    float ia_max_dev;                               // A相最大偏差
    float ib_max_dev;                               // B相最大偏差
    float ic_max_dev;                               // C相最大偏差
    float max_phase_deviation;                      // 三相间最大偏差
    float consistency_factor;                       // 一致性因子 (0-1, 1为完全一致)
    uint8_t is_consistent;                          // 是否一致 (1:一致, 0:不一致)
} pst_current_analysis_t;

/********** 测试结果结构体 **********/
typedef struct {
    sys_status_t overall_result;                    // 总体测试结果
    sys_status_t device_results[PST_DEVICE_COUNT];  // 各功率器件测试结果
    sys_status_t phase_results[PST_PHASE_COUNT];    // 各相位测试结果
    sys_status_t soft_fault_results[PST_PHASE_COUNT]; // 各相位软故障检测结果
    uint32_t test_duration_us;                      // 测试持续时间(us)
    float bus_voltage_at_test;                      // 测试时的母线电压

    // ADC采样和分析数据
    pst_adc_samples_t adc_samples[PST_PHASE_COUNT]; // 各相位的ADC采样数据
    pst_current_analysis_t current_analysis[PST_PHASE_COUNT]; // 各相位的电流分析结果
    uint8_t hardware_brake_triggered;               // 硬件刹车是否触发
    uint8_t adc_analysis_completed;                 // ADC分析是否完成
} pst_test_result_t;

/********** 公开API函数声明 **********/
/**
 * @brief 执行功率级硬件自检
 * @return 自检结果状态码
 * @note 此函数是模块的主要接口，封装了完整的自检流程
 */
sys_status_t Power_Stage_Test_Execute(void);

/**
 * @brief 获取详细的测试结果
 * @param result 输出测试结果结构体指针
 * @return 0: 成功, -1: 参数错误
 */
int Power_Stage_Test_GetDetailedResult(pst_test_result_t *result);

/**
 * @brief 检查母线电压是否满足测试条件
 * @return 1: 满足条件, 0: 不满足条件
 */
uint8_t Power_Stage_Test_CheckBusVoltage(void);

/**
 * @brief 复位测试结果
 */
void Power_Stage_Test_ResetResult(void);

/**
 * @brief 启动ADC同步采样
 * @param phase 要测试的相位
 * @return 1: 成功启动, 0: 启动失败
 */
uint8_t Power_Stage_Test_StartADCSampling(pst_phase_t phase);

/**
 * @brief 停止ADC同步采样
 */
void Power_Stage_Test_StopADCSampling(void);

/**
 * @brief 获取ADC采样数据
 * @param phase 相位
 * @param samples 输出采样数据指针
 * @return 1: 成功, 0: 失败
 */
uint8_t Power_Stage_Test_GetADCSamples(pst_phase_t phase, pst_adc_samples_t *samples);

/**
 * @brief 分析电流一致性
 * @param phase 相位
 * @param analysis 输出分析结果指针
 * @return 1: 成功, 0: 失败
 */
uint8_t Power_Stage_Test_AnalyzeCurrentConsistency(pst_phase_t phase, pst_current_analysis_t *analysis);



/**
 * @brief 检查是否为软故障
 * @param status 状态码
 * @return 1: 是软故障, 0: 不是软故障
 */
uint8_t Power_Stage_Test_IsSoftFault(sys_status_t status);

/**
 * @brief 测试PWM周期计次机制
 * @param test_cycles 测试的周期数
 * @return 1: 测试通过, 0: 测试失败
 * @note 此函数用于验证新的基于PWM周期计次的延时机制是否正常工作
 */
uint8_t Power_Stage_Test_PWMCycleTiming(uint8_t test_cycles);





/**
 * @brief 获取当前测试模式
 * @return PST_MODE_FULL_SYSTEM 或 PST_MODE_CORE_BOARD_ONLY
 * @note 用于运行时查询当前编译的测试模式
 */
uint8_t Power_Stage_Test_GetMode(void);

/**
 * @brief 获取当前测试模式的描述字符串
 * @return 模式描述字符串
 */
const char* Power_Stage_Test_GetModeString(void);

/*
 * 测试模式配置示例：
 *
 * // 方法1：在编译时通过宏定义设置模式
 * // 在项目设置或Makefile中添加：
 * // -DPOWER_STAGE_TEST_MODE=PST_MODE_CORE_BOARD_ONLY
 *
 * // 方法2：在包含此头文件之前定义模式
 * #define POWER_STAGE_TEST_MODE PST_MODE_CORE_BOARD_ONLY
 * #include "power_stage_test.h"
 *
 * 移植示例：
 *
 * // 对于STM32F4系列：
 * #define PST_TMR_INSTANCE            TIM1
 * #define PST_TMR_BRK_FLAG            TIM_FLAG_Break
 * #define PST_TMR_CLEAR_BRK_FLAG()    TIM_ClearFlag(PST_TMR_INSTANCE, PST_TMR_BRK_FLAG)
 * #define PST_TMR_GET_BRK_FLAG()      TIM_GetFlagStatus(PST_TMR_INSTANCE, PST_TMR_BRK_FLAG)
 *
 * // 对于其他AT32系列：
 * #define PST_TMR_INSTANCE            TMR8
 * #define PST_TMR_BRK_FLAG            TMR_BRK_FLAG
 * #define PST_PWM_CHANNEL_U_HIGH      TMR_SELECT_CHANNEL_1
 *
 * 使用示例：
 *
 * // 完整系统模式（默认）- 连接硬件时使用
 * sys_status_t result = Power_Stage_Test_Execute();
 * if (result == SYS_STATUS_PST_OK) {
 *     // 测试通过
 * } else if (Power_Stage_Test_IsSoftFault(result)) {
 *     // 软故障，通过调试器查看详细结果
 *     // 可以在 g_test_result 全局变量中查看所有测试数据
 * }
 *
 * // 仅核心板模式验证示例 - 独立开发调试时使用
 * #if (POWER_STAGE_TEST_MODE == PST_MODE_CORE_BOARD_ONLY)
 *     // 在此模式下，可以安全地验证：
 *     // 1. PWM波形生成逻辑
 *     // 2. 自检算法流程
 *     // 3. 数据分析功能
 *     // 4. 状态码处理
 *     sys_status_t result = Power_Stage_Test_Execute();
 *     // 结果将基于模拟数据，但算法逻辑完全相同
 * #endif
 *
 * // 获取详细测试结果（通过调试器查看）
 * pst_test_result_t detailed_result;
 * Power_Stage_Test_GetDetailedResult(&detailed_result);
 * // 在调试器中设置断点查看 detailed_result 的内容
 */

#ifdef __cplusplus
}
#endif

#endif /* __POWER_STAGE_TEST_H */
