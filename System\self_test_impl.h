/**
 * @file self_test_impl.h
 * @brief 自检模块具体实现层头文件
 * <AUTHOR> @date 2025-07-14
 */

#ifndef __SELF_TEST_IMPL_H
#define __SELF_TEST_IMPL_H

#include "self_test_manager.h"

#ifdef __cplusplus
extern "C" {
#endif

/****************************** 具体实现层 ******************************/

/**
 * @brief 初始化所有自检实现模块
 * @note 这个函数会注册所有可用的自检项目到管理器
 * @return true: 初始化成功, false: 初始化失败
 */
bool SelfTestImpl_InitAll(void);

/**
 * @brief 反初始化所有自检实现模块
 */
void SelfTestImpl_DeinitAll(void);

/****************************** ADC电流自检 ******************************/

/**
 * @brief ADC电流自检实现
 * @param result [out] 自检结果
 * @return true: 执行成功, false: 执行失败
 */
bool SelfTestImpl_ADCCurrent(self_test_item_result_t* result);

/****************************** 母线电压自检 ******************************/

/**
 * @brief 母线电压自检实现
 * @param result [out] 自检结果
 * @return true: 执行成功, false: 执行失败
 */
bool SelfTestImpl_BusVoltage(self_test_item_result_t* result);

/****************************** 电机温度自检 ******************************/

/**
 * @brief 电机温度自检实现
 * @param result [out] 自检结果
 * @return true: 执行成功, false: 执行失败
 */
bool SelfTestImpl_MotorTemp(self_test_item_result_t* result);

/****************************** 板上温度自检 ******************************/

/**
 * @brief 板上温度自检实现
 * @param result [out] 自检结果
 * @return true: 执行成功, false: 执行失败
 */
bool SelfTestImpl_BoardTemp(self_test_item_result_t* result);

/****************************** 旋变自检 ******************************/

/**
 * @brief 旋变自检实现
 * @param result [out] 自检结果
 * @return true: 执行成功, false: 执行失败
 */
bool SelfTestImpl_AD2S1210(self_test_item_result_t* result);

/****************************** 看门狗自检 ******************************/

/**
 * @brief 看门狗自检实现
 * @param result [out] 自检结果
 * @return true: 执行成功, false: 执行失败
 */
bool SelfTestImpl_Watchdog(self_test_item_result_t* result);

/****************************** 错误代码定义 ******************************/

// ADC电流自检错误代码
#define SELF_TEST_ERROR_ADC_CURRENT_CALIB_FAIL      0x1001  // ADC校准失败
#define SELF_TEST_ERROR_ADC_CURRENT_RANGE_ERROR     0x1002  // ADC读数超出范围

// 母线电压自检错误代码
#define SELF_TEST_ERROR_VOLTAGE_LOW                 0x2001  // 电压过低
#define SELF_TEST_ERROR_VOLTAGE_HIGH                0x2002  // 电压过高
#define SELF_TEST_ERROR_VOLTAGE_READ_FAIL           0x2003  // 电压读取失败

// 温度自检错误代码
#define SELF_TEST_ERROR_TEMP_OUT_OF_RANGE           0x3001  // 温度超出范围
#define SELF_TEST_ERROR_TEMP_READ_FAIL              0x3002  // 温度读取失败

// 旋变自检错误代码
#define SELF_TEST_ERROR_AD2S1210_SPI_FAIL           0x4001  // SPI通信失败
#define SELF_TEST_ERROR_AD2S1210_LOT_FAULT          0x4002  // LOT故障
#define SELF_TEST_ERROR_AD2S1210_DOS_FAULT          0x4003  // DOS故障
#define SELF_TEST_ERROR_AD2S1210_FAULT_REG          0x4004  // 故障寄存器异常

// 看门狗自检错误代码
#define SELF_TEST_ERROR_WATCHDOG_INIT_FAIL          0x5001  // 看门狗初始化失败

#ifdef __cplusplus
}
#endif

#endif /* __SELF_TEST_IMPL_H */
