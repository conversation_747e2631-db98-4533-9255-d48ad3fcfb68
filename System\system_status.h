#ifndef __SYSTEM_STATUS_H
#define __SYSTEM_STATUS_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 系统通用状态码定义
 * @note  高可靠性错误处理机制
 */
typedef enum {
    // 成功状态码
    SYS_STATUS_OK                   = 0,    // 操作成功
    
    // 通用错误状态码 (1-99)
    SYS_STATUS_ERROR                = 1,    // 通用错误
    SYS_STATUS_INVALID_PARAM        = 2,    // 参数无效
    SYS_STATUS_NULL_POINTER         = 3,    // 空指针错误
    SYS_STATUS_NOT_INITIALIZED      = 4,    // 模块未初始化
    SYS_STATUS_TIMEOUT              = 5,    // 超时错误
    SYS_STATUS_BUSY                 = 6,    // 系统忙
    SYS_STATUS_NOT_SUPPORTED        = 7,    // 不支持的操作
    SYS_STATUS_OUT_OF_RANGE         = 8,    // 超出范围
    SYS_STATUS_BUFFER_FULL          = 9,    // 缓冲区满
    SYS_STATUS_BUFFER_EMPTY         = 10,   // 缓冲区空
    
    // 传感器相关状态码 (100-199)
    SYS_STATUS_SENSOR_OK            = 100,  // 传感器正常
    SYS_STATUS_SENSOR_OPEN_CIRCUIT  = 101,  // 传感器开路
    SYS_STATUS_SENSOR_SHORT_CIRCUIT = 102,  // 传感器短路
    SYS_STATUS_SENSOR_OUT_OF_RANGE  = 103,  // 传感器超出范围
    SYS_STATUS_SENSOR_NOT_READY     = 104,  // 传感器未就绪
    SYS_STATUS_SENSOR_CALIBRATING   = 105,  // 传感器校准中
    SYS_STATUS_SENSOR_FAULT         = 106,  // 传感器故障
    
    // 保护监控相关状态码 (200-299)
    SYS_STATUS_PROTECT_NORMAL       = 200,  // 保护正常
    SYS_STATUS_PROTECT_MONITORING   = 201,  // 保护监控中
    SYS_STATUS_PROTECT_TRIGGERED    = 202,  // 保护已触发
    SYS_STATUS_PROTECT_FAULT        = 203,  // 保护故障
    SYS_STATUS_PROTECT_DISABLED     = 204,  // 保护禁用
    
    // 告警系统相关状态码 (300-399)
    SYS_STATUS_ALARM_NORMAL         = 300,  // 告警正常
    SYS_STATUS_ALARM_ACTIVE         = 301,  // 告警激活
    SYS_STATUS_ALARM_PENDING        = 302,  // 告警待定
    SYS_STATUS_ALARM_CLEARED        = 303,  // 告警清除
    
    // 电机控制相关状态码 (400-499)
    SYS_STATUS_MOTOR_STOPPED        = 400,  // 电机停止
    SYS_STATUS_MOTOR_STARTING       = 401,  // 电机启动中
    SYS_STATUS_MOTOR_RUNNING        = 402,  // 电机运行
    SYS_STATUS_MOTOR_FAULT          = 403,  // 电机故障
    SYS_STATUS_MOTOR_EMERGENCY_STOP = 404,  // 电机紧急停止
    
    // 通信相关状态码 (500-599)
    SYS_STATUS_COMM_OK              = 500,  // 通信正常
    SYS_STATUS_COMM_ERROR           = 501,  // 通信错误
    SYS_STATUS_COMM_TIMEOUT         = 502,  // 通信超时
    SYS_STATUS_COMM_CRC_ERROR       = 503,  // CRC校验错误
    SYS_STATUS_COMM_FRAME_ERROR     = 504,  // 帧错误
    
    // 系统状态码上限
    SYS_STATUS_MAX                  = 999   // 状态码最大值
} sys_status_t;

/**
 * @brief 检查状态码是否表示成功
 * @param status 状态码
 * @return 1: 成功, 0: 失败
 */
static inline uint8_t SYS_STATUS_IS_OK(sys_status_t status)
{
    return (status == SYS_STATUS_OK) ? 1 : 0;
}

/**
 * @brief 检查状态码是否表示错误
 * @param status 状态码
 * @return 1: 错误, 0: 正常
 */
static inline uint8_t SYS_STATUS_IS_ERROR(sys_status_t status)
{
    return (status != SYS_STATUS_OK) ? 1 : 0;
}

/**
 * @brief 检查状态码是否为传感器相关
 * @param status 状态码
 * @return 1: 是传感器相关, 0: 不是
 */
static inline uint8_t SYS_STATUS_IS_SENSOR(sys_status_t status)
{
    return (status >= 100 && status < 200) ? 1 : 0;
}

/**
 * @brief 检查状态码是否为保护相关
 * @param status 状态码
 * @return 1: 是保护相关, 0: 不是
 */
static inline uint8_t SYS_STATUS_IS_PROTECT(sys_status_t status)
{
    return (status >= 200 && status < 300) ? 1 : 0;
}

/**
 * @brief 检查状态码是否为告警相关
 * @param status 状态码
 * @return 1: 是告警相关, 0: 不是
 */
static inline uint8_t SYS_STATUS_IS_ALARM(sys_status_t status)
{
    return (status >= 300 && status < 400) ? 1 : 0;
}

#ifdef __cplusplus
}
#endif

#endif /* __SYSTEM_STATUS_H */
