/**
 * @file self_test_impl.c
 * @brief 自检模块具体实现层实现
 * <AUTHOR> @date 2025-07-14
 */

#include "self_test_impl.h"
#include "adc_pmsm.h"
#include "sensor_proc.h"
#include "ad2s1212_spi.h"
#include "at32a403a_wdt.h"
#include <math.h>

/****************************** 内部配置 ******************************/

// 各模块自检超时时间配置（毫秒）
#define SELF_TEST_TIMEOUT_ADC_CURRENT       500
#define SELF_TEST_TIMEOUT_BUS_VOLTAGE       200
#define SELF_TEST_TIMEOUT_MOTOR_TEMP        300
#define SELF_TEST_TIMEOUT_BOARD_TEMP        300
#define SELF_TEST_TIMEOUT_AD2S1210          1000
#define SELF_TEST_TIMEOUT_WATCHDOG          100

/****************************** 具体实现层主函数 ******************************/

/**
 * @brief 初始化所有自检实现模块
 */
bool SelfTestImpl_InitAll(void)
{
    bool success = true;
    
    // 定义所有自检项目配置
    self_test_item_config_t items[] = {
        {
            .item = SELF_TEST_ITEM_ADC_CURRENT,
            .test_func = SelfTestImpl_ADCCurrent,
            .enabled = true,
            .timeout_ms = SELF_TEST_TIMEOUT_ADC_CURRENT,
            .name = "ADC电流自检"
        },
        {
            .item = SELF_TEST_ITEM_BUS_VOLTAGE,
            .test_func = SelfTestImpl_BusVoltage,
            .enabled = true,
            .timeout_ms = SELF_TEST_TIMEOUT_BUS_VOLTAGE,
            .name = "母线电压自检"
        },
        {
            .item = SELF_TEST_ITEM_MOTOR_TEMP,
            .test_func = SelfTestImpl_MotorTemp,
            .enabled = true,
            .timeout_ms = SELF_TEST_TIMEOUT_MOTOR_TEMP,
            .name = "电机温度自检"
        },
        {
            .item = SELF_TEST_ITEM_BOARD_TEMP,
            .test_func = SelfTestImpl_BoardTemp,
            .enabled = true,
            .timeout_ms = SELF_TEST_TIMEOUT_BOARD_TEMP,
            .name = "板上温度自检"
        },
        {
            .item = SELF_TEST_ITEM_AD2S1210,
            .test_func = SelfTestImpl_AD2S1210,
            .enabled = true,
            .timeout_ms = SELF_TEST_TIMEOUT_AD2S1210,
            .name = "旋变自检"
        },
        {
            .item = SELF_TEST_ITEM_WATCHDOG,
            .test_func = SelfTestImpl_Watchdog,
            .enabled = true,
            .timeout_ms = SELF_TEST_TIMEOUT_WATCHDOG,
            .name = "看门狗自检"
        }
    };
    
    // 注册所有自检项目
    uint32_t item_count = sizeof(items) / sizeof(items[0]);
    for (uint32_t i = 0; i < item_count; i++) {
        if (!SelfTestManager_RegisterItem(&items[i])) {
            success = false;
        }
    }
    
    return success;
}

/**
 * @brief 反初始化所有自检实现模块
 */
void SelfTestImpl_DeinitAll(void)
{
    // 具体实现模块的清理工作
    // 目前为空，如有需要可在此添加清理代码
}

/****************************** AD2S1210旋变模块自检实现 ******************************/

/**
 * @brief AD2S1210旋变模块自检实现
 * @note 使用ad2s1212_spi.c的自检函数进行封装
 */
bool SelfTestImpl_AD2S1210(self_test_item_result_t* result)
{
    if (result == NULL) {
        return false;
    }

    // 初始化结果
    result->item = SELF_TEST_ITEM_AD2S1210;
    result->result = SELF_TEST_RESULT_NOT_TESTED;
    result->error_code = 0;

    // 调用AD2S1210自检函数
    AD2S1210_SelfTestResult_t ad2s_result;
    uint8_t self_test_status = AD2S1210_SelfTest(&ad2s_result);

    if (self_test_status == 0) {
        // 自检通过
        result->result = SELF_TEST_RESULT_PASS;
    } else {
        // 自检失败，分析具体原因
        result->result = SELF_TEST_RESULT_FAIL;

        // 根据具体故障设置错误代码
        if (ad2s_result.lot_status == 0) {
            result->error_code = SELF_TEST_ERROR_AD2S1210_LOT_FAULT;
        } else if (ad2s_result.dos_status == 0) {
            result->error_code = SELF_TEST_ERROR_AD2S1210_DOS_FAULT;
        } else if (ad2s_result.fault_register != 0) {
            result->error_code = SELF_TEST_ERROR_AD2S1210_FAULT_REG;
        } else {
            result->error_code = SELF_TEST_ERROR_AD2S1210_SPI_FAIL;
        }
    }

    return true;
}

/****************************** ADC电流采样模块自检实现 ******************************/

/**
 * @brief ADC电流采样模块自检实现
 * @note 检查滤波后的母线+三相电流，绝对值应小于1A
 */
bool SelfTestImpl_ADCCurrent(self_test_item_result_t* result)
{
    if (result == NULL) {
        return false;
    }

    result->item = SELF_TEST_ITEM_ADC_CURRENT;
    result->result = SELF_TEST_RESULT_NOT_TESTED;
    result->error_code = 0;

    // 检查ADC校准是否完成
    if (!ADC_PMSM_IsCalibrationCompleted()) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_ADC_CURRENT_CALIB_FAIL;
        return true;
    }

    // 获取滤波后的电流值
    float idc_filt = ADC_GET_IDC_FILTERED();    // 母线电流
    float ia_filt = ADC_GET_IA_FILTERED();      // A相电流
    float ib_filt = ADC_GET_IB_FILTERED();      // B相电流
    float ic_filt = ADC_GET_IC_FILTERED();      // C相电流

    // 检查电流绝对值是否小于1A
    const float CURRENT_THRESHOLD = 1.0f;  // 1A阈值

    if (fabsf(idc_filt) >= CURRENT_THRESHOLD) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_ADC_CURRENT_RANGE_ERROR;
        return true;
    }

    if (fabsf(ia_filt) >= CURRENT_THRESHOLD) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_ADC_CURRENT_RANGE_ERROR;
        return true;
    }

    if (fabsf(ib_filt) >= CURRENT_THRESHOLD) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_ADC_CURRENT_RANGE_ERROR;
        return true;
    }

    if (fabsf(ic_filt) >= CURRENT_THRESHOLD) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_ADC_CURRENT_RANGE_ERROR;
        return true;
    }

    // 所有电流检查通过
    result->result = SELF_TEST_RESULT_PASS;

    return true;
}

/****************************** 母线电压自检实现 ******************************/

/**
 * @brief 母线电压自检实现
 * @note 检查母线电压是否在180V-360V范围内
 */
bool SelfTestImpl_BusVoltage(self_test_item_result_t* result)
{
    if (result == NULL) {
        return false;
    }

    result->item = SELF_TEST_ITEM_BUS_VOLTAGE;
    result->result = SELF_TEST_RESULT_NOT_TESTED;
    result->error_code = 0;

    // 获取母线电压
    float bus_voltage;
    sys_status_t status = sensor_get_value(SENSOR_U_DC, &bus_voltage);

    if (status != SYS_STATUS_OK) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_VOLTAGE_READ_FAIL;
        return true;
    }

    // 检查电压范围：180V - 360V
    const float VOLTAGE_MIN = 180.0f;
    const float VOLTAGE_MAX = 360.0f;

    if (bus_voltage < VOLTAGE_MIN) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_VOLTAGE_LOW;
        return true;
    }

    if (bus_voltage > VOLTAGE_MAX) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_VOLTAGE_HIGH;
        return true;
    }

    // 电压检查通过
    result->result = SELF_TEST_RESULT_PASS;

    return true;
}

/****************************** 电机温度自检实现 ******************************/

/**
 * @brief 电机温度自检实现
 * @note 检查电机温度(-50到150°C)
 */
bool SelfTestImpl_MotorTemp(self_test_item_result_t* result)
{
    if (result == NULL) {
        return false;
    }

    result->item = SELF_TEST_ITEM_MOTOR_TEMP;
    result->result = SELF_TEST_RESULT_NOT_TESTED;
    result->error_code = 0;

    // 检查电机温度传感器
    float motor_temp;
    sys_status_t motor_status = sensor_get_value(SENSOR_M_PT0, &motor_temp);

    if (motor_status != SYS_STATUS_OK) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_TEMP_READ_FAIL;
        return true;
    }

    // 检查电机温度范围：-50°C 到 150°C
    const float MOTOR_TEMP_MIN = -50.0f;
    const float MOTOR_TEMP_MAX = 150.0f;

    if (motor_temp < MOTOR_TEMP_MIN || motor_temp > MOTOR_TEMP_MAX) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_TEMP_OUT_OF_RANGE;
        return true;
    }

    // 电机温度检查通过
    result->result = SELF_TEST_RESULT_PASS;

    return true;
}

/****************************** 板上温度自检实现 ******************************/

/**
 * @brief 板上温度自检实现
 * @note 检查板上温度(-50到105°C)
 */
bool SelfTestImpl_BoardTemp(self_test_item_result_t* result)
{
    if (result == NULL) {
        return false;
    }

    result->item = SELF_TEST_ITEM_BOARD_TEMP;
    result->result = SELF_TEST_RESULT_NOT_TESTED;
    result->error_code = 0;

    // 检查板上温度传感器
    float board_temp;
    sys_status_t board_status = sensor_get_value(SENSOR_B_PT0, &board_temp);

    if (board_status != SYS_STATUS_OK) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_TEMP_READ_FAIL;
        return true;
    }

    // 检查板上温度范围：-50°C 到 105°C
    const float BOARD_TEMP_MIN = -50.0f;
    const float BOARD_TEMP_MAX = 105.0f;

    if (board_temp < BOARD_TEMP_MIN || board_temp > BOARD_TEMP_MAX) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_TEMP_OUT_OF_RANGE;
        return true;
    }

    // 板上温度检查通过
    result->result = SELF_TEST_RESULT_PASS;

    return true;
}



/****************************** 看门狗模块自检实现 ******************************/

/**
 * @brief 看门狗模块自检实现
 * @note 使用at32a403a_wdt提供的支持包函数检查看门狗配置和状态
 */
bool SelfTestImpl_Watchdog(self_test_item_result_t* result)
{
    if (result == NULL) {
        return false;
    }

    result->item = SELF_TEST_ITEM_WATCHDOG;
    result->result = SELF_TEST_RESULT_NOT_TESTED;
    result->error_code = 0;

    // 检查看门狗寄存器状态
    // 注意：这里只检查配置，不启用看门狗以避免系统复位

    // 检查分频器更新标志
    flag_status div_flag = wdt_flag_get(WDT_DIVF_UPDATE_FLAG);

    // 检查重载值更新标志
    flag_status rld_flag = wdt_flag_get(WDT_RLDF_UPDATE_FLAG);

    // 读取看门狗寄存器值来验证配置
    uint32_t div_value = WDT->div_bit.div;      // 分频值
    uint32_t rld_value = WDT->rld_bit.rld;      // 重载值

    // 检查配置是否合理
    // 分频值应该在有效范围内 (0-6对应WDT_CLK_DIV_4到WDT_CLK_DIV_256)
    if (div_value > 6) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_WATCHDOG_INIT_FAIL;
        return true;
    }

    // 重载值应该在有效范围内 (0x000-0xFFF)
    if (rld_value > 0xFFF) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_WATCHDOG_INIT_FAIL;
        return true;
    }

    // 检查重载值是否为0（无效配置）
    if (rld_value == 0) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = SELF_TEST_ERROR_WATCHDOG_INIT_FAIL;
        return true;
    }

    // 看门狗配置检查通过
    result->result = SELF_TEST_RESULT_PASS;

    return true;
}
