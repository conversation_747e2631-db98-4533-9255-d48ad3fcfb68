#include "power_stage_test.h"
#include "Sys_TimerEvent.h"
#include "adc_pmsm.h"
#include "wk_system.h"
#include <string.h>
#include <math.h>

/********** 私有变量定义 **********/
static pst_test_result_t g_test_result = {0};  // 测试结果存储
static uint8_t g_test_completed = 0;           // 测试完成标志

// ADC采样管理变量
static volatile uint8_t g_adc_sampling_active = 0;     // ADC采样激活标志
static volatile uint8_t g_adc_sample_count = 0;        // 当前采样计数
static volatile pst_phase_t g_current_test_phase = PST_PHASE_U; // 当前测试相位
static pst_adc_samples_t g_adc_buffer = {0};           // ADC采样缓冲区

/********** 私有函数声明 **********/
static sys_status_t pst_prepare_test_environment(void);
static sys_status_t pst_test_power_devices(void);
static sys_status_t pst_test_motor_windings(void);
static sys_status_t pst_test_single_device(pst_device_t device);
static sys_status_t pst_test_single_phase(pst_phase_t phase);
static void pst_set_device_pwm(pst_device_t device, uint16_t duty);
static void pst_set_phase_test_vector(pst_phase_t test_phase);
static void pst_clear_all_pwm(void);
static void pst_delay_us(uint32_t us);
static uint8_t pst_wait_for_pwm_cycles(uint8_t count, uint32_t timeout_ms);
static sys_status_t pst_device_to_status_code(pst_device_t device);
static sys_status_t pst_phase_to_status_code(pst_phase_t phase);

// ADC采样管理函数
static uint8_t pst_init_adc_sampling(pst_phase_t phase);
static void pst_cleanup_adc_sampling(void);
static uint8_t pst_collect_adc_sample(void);
static uint8_t pst_wait_for_adc_samples(uint32_t timeout_ms);
static void pst_adc_sample_callback(void);

// 电流一致性分析函数
static uint8_t pst_analyze_current_samples(pst_phase_t phase);
static float pst_calculate_average(const float *samples, uint8_t count);
static float pst_calculate_max_deviation(const float *samples, uint8_t count, float average);
static float pst_calculate_consistency_factor(const pst_current_analysis_t *analysis);
static uint8_t pst_detect_soft_faults(const pst_current_analysis_t *analysis);

// 安全保护函数
static uint8_t pst_verify_brake_function(void);
static void pst_emergency_shutdown(void);

/********** 公开API函数实现 **********/

/**
 * @brief 执行功率级硬件自检
 * @return 自检结果状态码
 */
sys_status_t Power_Stage_Test_Execute(void)
{
    sys_status_t result;
    uint32_t start_time_us;
    
    // 重置测试结果
    Power_Stage_Test_ResetResult();
    
    // 记录开始时间
    start_time_us = Get_Runtime_Ms() * 1000;
    
    // 第一步：准备测试环境
    result = pst_prepare_test_environment();
    if (result != SYS_STATUS_OK) {
        g_test_result.overall_result = result;
        return result;
    }
    
    // 第二步：功率器件直通测试
    result = pst_test_power_devices();
    if (result != SYS_STATUS_OK) {
        g_test_result.overall_result = result;
        return result;
    }
    
    // 第三步：电机绕组短路测试
    result = pst_test_motor_windings();
    if (result != SYS_STATUS_OK) {
        g_test_result.overall_result = result;
        return result;
    }
    
    // 计算测试持续时间
    g_test_result.test_duration_us = (Get_Runtime_Ms() * 1000) - start_time_us;
    
    // 测试完成
    g_test_result.overall_result = SYS_STATUS_PST_OK;
    g_test_completed = 1;
    
    return SYS_STATUS_PST_OK;
}

/**
 * @brief 获取详细的测试结果
 */
int Power_Stage_Test_GetDetailedResult(pst_test_result_t *result)
{
    if (result == NULL) {
        return -1;
    }
    
    *result = g_test_result;
    return 0;
}

/**
 * @brief 检查母线电压是否满足测试条件
 */
uint8_t Power_Stage_Test_CheckBusVoltage(void)
{
    float bus_voltage;
    
    // 获取母线电压
    if (sensor_get_value(SENSOR_U_DC, &bus_voltage) != SYS_STATUS_OK) {
        return 0;  // 获取失败，认为不满足条件
    }
    
    // 记录测试时的母线电压
    g_test_result.bus_voltage_at_test = bus_voltage;
    
    // 检查是否满足最低电压要求
    return (bus_voltage >= PST_BUS_VOLTAGE_MIN_THRESHOLD) ? 1 : 0;
}

/**
 * @brief 复位测试结果
 */
void Power_Stage_Test_ResetResult(void)
{
    // 清零测试结果结构体
    memset(&g_test_result, 0, sizeof(pst_test_result_t));
    
    // 初始化所有器件和相位结果为OK
    for (int i = 0; i < PST_DEVICE_COUNT; i++) {
        g_test_result.device_results[i] = SYS_STATUS_PST_OK;
    }
    
    for (int i = 0; i < PST_PHASE_COUNT; i++) {
        g_test_result.phase_results[i] = SYS_STATUS_PST_OK;
    }
    
    g_test_completed = 0;
}

/**
 * @brief 启动ADC同步采样
 */
uint8_t Power_Stage_Test_StartADCSampling(pst_phase_t phase)
{
    if (g_adc_sampling_active) {
        return 0;  // 已经在采样中
    }

    return pst_init_adc_sampling(phase);
}

/**
 * @brief 停止ADC同步采样
 */
void Power_Stage_Test_StopADCSampling(void)
{
    pst_cleanup_adc_sampling();
}

/**
 * @brief 获取ADC采样数据
 */
uint8_t Power_Stage_Test_GetADCSamples(pst_phase_t phase, pst_adc_samples_t *samples)
{
    if (samples == NULL || phase >= PST_PHASE_COUNT) {
        return 0;
    }

    *samples = g_test_result.adc_samples[phase];
    return 1;
}

/**
 * @brief 分析电流一致性
 */
uint8_t Power_Stage_Test_AnalyzeCurrentConsistency(pst_phase_t phase, pst_current_analysis_t *analysis)
{
    if (analysis == NULL || phase >= PST_PHASE_COUNT) {
        return 0;
    }

    *analysis = g_test_result.current_analysis[phase];
    return 1;
}



/**
 * @brief 检查是否为软故障
 */
uint8_t Power_Stage_Test_IsSoftFault(sys_status_t status)
{
    return (status >= SYS_STATUS_PST_SOFT_FAULT_U &&
            status <= SYS_STATUS_PST_ADC_SAMPLE_FAILED) ? 1 : 0;
}





/********** 私有函数实现 **********/

/**
 * @brief 准备测试环境（确保安全保护始终开启）
 */
static sys_status_t pst_prepare_test_environment(void)
{
    // 1. 检查母线电压（仅在完整系统模式下执行）
#if (POWER_STAGE_TEST_MODE == PST_MODE_FULL_SYSTEM)
    if (!Power_Stage_Test_CheckBusVoltage()) {
        return SYS_STATUS_PST_BUS_VOLTAGE_LOW;
    }
#else
    // 仅核心板模式：跳过母线电压检查，假设电压正常
    // 这允许在没有功率驱动板的情况下进行自检逻辑验证
#endif

    // 2. 停止定时器和PWM输出
    PST_TMR_COUNTER_DISABLE();
    PST_TMR_OUTPUT_DISABLE();

    // 3. 清除所有PWM占空比
    pst_clear_all_pwm();

    // 4. 清除刹车标志但保持刹车功能开启
    PST_TMR_CLEAR_BRK_FLAG();

    // 5. 验证硬件刹车功能是否正常工作
    // 注意：不禁用刹车功能，确保安全保护始终有效

    // 6. 重新启用定时器和PWM输出
    PST_TMR_OUTPUT_ENABLE();
    PST_TMR_COUNTER_ENABLE();

    // 7. 等待PWM周期完成，确保系统稳定
    if (!pst_wait_for_pwm_cycles(PST_DEVICE_TEST_CYCLES, PST_PWM_CYCLE_TIMEOUT_MS)) {
        pst_emergency_shutdown();
        return SYS_STATUS_PST_SYSTEM_ERROR;
    }

    // 8. 最终安全检查：确认刹车功能仍然开启
    if (!pst_verify_brake_function()) {
        pst_emergency_shutdown();
        return SYS_STATUS_PST_HARDWARE_ERROR;
    }

    return SYS_STATUS_OK;
}

/**
 * @brief 测试所有功率器件
 */
static sys_status_t pst_test_power_devices(void)
{
    sys_status_t result;
    
    // 遍历所有功率器件进行测试
    for (pst_device_t device = PST_DEVICE_U_HIGH; device < PST_DEVICE_COUNT; device++) {
        result = pst_test_single_device(device);
        
        // 记录单个器件的测试结果
        g_test_result.device_results[device] = result;
        
        // 如果发现故障，立即返回
        if (result != SYS_STATUS_PST_OK) {
            return result;
        }
    }
    
    return SYS_STATUS_PST_OK;
}

/**
 * @brief 测试电机绕组（双重验证）
 */
static sys_status_t pst_test_motor_windings(void)
{
    sys_status_t result;

    // 遍历所有相位进行双重验证测试
    for (pst_phase_t phase = PST_PHASE_U; phase < PST_PHASE_COUNT; phase++) {
        result = pst_test_single_phase(phase);

        // 记录单个相位的测试结果
        g_test_result.phase_results[phase] = result;

        // 如果发现硬件故障，立即返回
        if (result != SYS_STATUS_PST_OK) {
            return result;
        }

        // 进行ADC电流一致性分析
        if (pst_analyze_current_samples(phase)) {
            // 检测软故障
            if (pst_detect_soft_faults(&g_test_result.current_analysis[phase])) {
                // 根据相位设置软故障状态码
                switch (phase) {
                    case PST_PHASE_U:
                        g_test_result.soft_fault_results[phase] = SYS_STATUS_PST_SOFT_FAULT_U;
                        return SYS_STATUS_PST_SOFT_FAULT_U;
                    case PST_PHASE_V:
                        g_test_result.soft_fault_results[phase] = SYS_STATUS_PST_SOFT_FAULT_V;
                        return SYS_STATUS_PST_SOFT_FAULT_V;
                    case PST_PHASE_W:
                        g_test_result.soft_fault_results[phase] = SYS_STATUS_PST_SOFT_FAULT_W;
                        return SYS_STATUS_PST_SOFT_FAULT_W;
                    default:
                        break;
                }
            }

            // 检查电流一致性
            if (!g_test_result.current_analysis[phase].is_consistent) {
                g_test_result.soft_fault_results[phase] = SYS_STATUS_PST_CURRENT_INCONSISTENT;
                return SYS_STATUS_PST_CURRENT_INCONSISTENT;
            }
        } else {
            // ADC分析失败
            g_test_result.soft_fault_results[phase] = SYS_STATUS_PST_ADC_SAMPLE_FAILED;
            return SYS_STATUS_PST_ADC_SAMPLE_FAILED;
        }

        // 标记软故障检测通过
        g_test_result.soft_fault_results[phase] = SYS_STATUS_PST_OK;
    }

    // 标记ADC分析完成
    g_test_result.adc_analysis_completed = 1;

    return SYS_STATUS_PST_OK;
}

/**
 * @brief 测试单个功率器件
 */
static sys_status_t pst_test_single_device(pst_device_t device)
{
    // 清零-激励-检查 三步曲
    
    // 1. 清零：清除刹车标志
    PST_TMR_CLEAR_BRK_FLAG();
    
    // 2. 激励：对当前器件施加测试脉冲
    pst_set_device_pwm(device, PST_DEVICE_TEST_PULSE_WIDTH);

    // 等待PWM周期完成，让脉冲生效
    if (!pst_wait_for_pwm_cycles(PST_DEVICE_TEST_CYCLES, PST_PWM_CYCLE_TIMEOUT_MS)) {
        // 等待超时，清除PWM并返回错误
        pst_clear_all_pwm();
        return SYS_STATUS_PST_SYSTEM_ERROR;
    }
    
    // 3. 检查：读取刹车标志
    if (PST_TMR_GET_BRK_FLAG() == SET) {
        // 发现故障，清除PWM并返回对应的错误码
        pst_clear_all_pwm();
        return pst_device_to_status_code(device);
    }
    
    // 清除当前器件的PWM
    pst_set_device_pwm(device, 0);
    
    return SYS_STATUS_PST_OK;
}

/**
 * @brief 测试单个相位绕组（双重验证，优化时序）
 */
static sys_status_t pst_test_single_phase(pst_phase_t phase)
{
    sys_status_t result = SYS_STATUS_PST_OK;

    // 第一重验证：硬件保护检测
    // 1. 清零：清除刹车标志
    PST_TMR_CLEAR_BRK_FLAG();
    g_test_result.hardware_brake_triggered = 0;

    // 2. 启动ADC采样准备
    if (!pst_init_adc_sampling(phase)) {
        return SYS_STATUS_PST_ADC_SAMPLE_FAILED;
    }

    // 3. 激励：设置测试矢量
    pst_set_phase_test_vector(phase);

    // 4. PWM稳定等待
    if (!pst_wait_for_pwm_cycles(PST_PWM_STABILIZATION_CYCLES, PST_PWM_CYCLE_TIMEOUT_MS)) {
        pst_cleanup_adc_sampling();
        return SYS_STATUS_PST_SYSTEM_ERROR;
    }

    // 5. 开始同步采样循环 - 基于PWM周期计次
    uint8_t sample_success = 0;
    for (uint8_t i = 0; i < PST_ADC_SAMPLE_COUNT; i++) {
        // 等待PWM周期完成，确保ADC数据更新
        if (!pst_wait_for_pwm_cycles(1, PST_PWM_CYCLE_TIMEOUT_MS)) {
            result = SYS_STATUS_PST_SYSTEM_ERROR;
            break;
        }

        // 收集单次采样
        if (pst_collect_adc_sample()) {
            sample_success++;
        }

        // 立即检查硬件刹车（实时安全监控）
        if (PST_TMR_GET_BRK_FLAG() == SET) {
            g_test_result.hardware_brake_triggered = 1;
            result = pst_phase_to_status_code(phase);
            break;  // 立即停止测试
        }

        // 采样间隔等待 - 等待指定PWM周期数
        if (i < PST_ADC_SAMPLE_COUNT - 1) {
            if (!pst_wait_for_pwm_cycles(PST_SAMPLE_INTERVAL_CYCLES, PST_PWM_CYCLE_TIMEOUT_MS)) {
                result = SYS_STATUS_PST_SYSTEM_ERROR;
                break;
            }
        }
    }

    // 6. 检查采样成功率
    if (sample_success < PST_ADC_SAMPLE_COUNT && result == SYS_STATUS_PST_OK) {
        result = SYS_STATUS_PST_ADC_SAMPLE_FAILED;
    }

    // 7. 保存ADC采样数据
    g_test_result.adc_samples[phase] = g_adc_buffer;

    // 8. 清理
    pst_cleanup_adc_sampling();
    pst_clear_all_pwm();

    return result;
}

/**
 * @brief 设置单个功率器件的PWM占空比
 */
static void pst_set_device_pwm(pst_device_t device, uint16_t duty)
{
    // 首先清除所有PWM
    pst_clear_all_pwm();

    // 根据器件类型设置对应的PWM通道
    switch (device) {
        case PST_DEVICE_U_HIGH:
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_U_HIGH, duty);
            break;
        case PST_DEVICE_U_LOW:
            // 下管通过互补输出控制，这里设置上管为0，下管自动激活
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_U_HIGH, PST_TMR_PERIOD_VALUE - duty);
            break;
        case PST_DEVICE_V_HIGH:
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_V_HIGH, duty);
            break;
        case PST_DEVICE_V_LOW:
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_V_HIGH, PST_TMR_PERIOD_VALUE - duty);
            break;
        case PST_DEVICE_W_HIGH:
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_W_HIGH, duty);
            break;
        case PST_DEVICE_W_LOW:
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_W_HIGH, PST_TMR_PERIOD_VALUE - duty);
            break;
        default:
            break;
    }
}

/**
 * @brief 设置相位测试矢量
 */
static void pst_set_phase_test_vector(pst_phase_t test_phase)
{
    // 清除所有PWM
    pst_clear_all_pwm();

    switch (test_phase) {
        case PST_PHASE_U:
            // 测试U相：V相和W相下管导通，U相上管施加测试脉冲
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_V_HIGH, PST_TMR_PERIOD_VALUE - 10);  // V相下管导通
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_W_HIGH, PST_TMR_PERIOD_VALUE - 10);  // W相下管导通
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_U_HIGH, PST_PHASE_TEST_PULSE_WIDTH); // U相上管测试脉冲
            break;

        case PST_PHASE_V:
            // 测试V相：U相和W相下管导通，V相上管施加测试脉冲
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_U_HIGH, PST_TMR_PERIOD_VALUE - 10);  // U相下管导通
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_W_HIGH, PST_TMR_PERIOD_VALUE - 10);  // W相下管导通
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_V_HIGH, PST_PHASE_TEST_PULSE_WIDTH); // V相上管测试脉冲
            break;

        case PST_PHASE_W:
            // 测试W相：U相和V相下管导通，W相上管施加测试脉冲
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_U_HIGH, PST_TMR_PERIOD_VALUE - 10);  // U相下管导通
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_V_HIGH, PST_TMR_PERIOD_VALUE - 10);  // V相下管导通
            PST_SET_PWM_DUTY(PST_PWM_CHANNEL_W_HIGH, PST_PHASE_TEST_PULSE_WIDTH); // W相上管测试脉冲
            break;

        default:
            break;
    }
}

/**
 * @brief 清除所有PWM占空比
 */
static void pst_clear_all_pwm(void)
{
    PST_SET_PWM_DUTY(PST_PWM_CHANNEL_U_HIGH, 0);
    PST_SET_PWM_DUTY(PST_PWM_CHANNEL_V_HIGH, 0);
    PST_SET_PWM_DUTY(PST_PWM_CHANNEL_W_HIGH, 0);
}

/**
 * @brief 基于PWM周期完成的等待函数
 * @param count 等待PWM周期完成的次数
 * @param timeout_ms 超时时间(毫秒)
 * @return 1: 成功等待到指定次数, 0: 超时或失败
 * @note 此函数等待PWM周期完成指定次数，用于替代传统的时间延时
 *       移植说明：通过修改PST_GET_PWM_CYCLE_TICK()宏可适配不同平台
 */
static uint8_t pst_wait_for_pwm_cycles(uint8_t count, uint32_t timeout_ms)
{
    if (count == 0) {
        return 1;  // 无需等待
    }

    uint32_t start_tick = PST_GET_PWM_CYCLE_TICK();
    uint32_t target_tick = start_tick + count;
    uint32_t timeout_count = timeout_ms * 1000;  // 转换为微秒级计数
    uint32_t elapsed_time = 0;

    while (PST_GET_PWM_CYCLE_TICK() < target_tick) {
        // 检查超时
        if (elapsed_time >= timeout_count) {
            return 0;  // 超时
        }

        // 检查硬件刹车状态（安全检查）
        if (PST_TMR_GET_BRK_FLAG() == SET) {
            return 0;  // 硬件保护触发
        }

        // 短暂延时并增加计数
        pst_delay_us(1);
        elapsed_time++;
    }

    return 1;  // 成功等待到指定次数
}

/**
 * @brief 将功率器件枚举转换为对应的状态码
 */
static sys_status_t pst_device_to_status_code(pst_device_t device)
{
    switch (device) {
        case PST_DEVICE_U_HIGH: return SYS_STATUS_PST_U_HIGH_SHORT;
        case PST_DEVICE_U_LOW:  return SYS_STATUS_PST_U_LOW_SHORT;
        case PST_DEVICE_V_HIGH: return SYS_STATUS_PST_V_HIGH_SHORT;
        case PST_DEVICE_V_LOW:  return SYS_STATUS_PST_V_LOW_SHORT;
        case PST_DEVICE_W_HIGH: return SYS_STATUS_PST_W_HIGH_SHORT;
        case PST_DEVICE_W_LOW:  return SYS_STATUS_PST_W_LOW_SHORT;
        default: return SYS_STATUS_PST_SYSTEM_ERROR;
    }
}

/**
 * @brief 将相位枚举转换为对应的状态码
 */
static sys_status_t pst_phase_to_status_code(pst_phase_t phase)
{
    switch (phase) {
        case PST_PHASE_U: return SYS_STATUS_PST_PHASE_U_SHORT;
        case PST_PHASE_V: return SYS_STATUS_PST_PHASE_V_SHORT;
        case PST_PHASE_W: return SYS_STATUS_PST_PHASE_W_SHORT;
        default: return SYS_STATUS_PST_SYSTEM_ERROR;
    }
}

/********** ADC采样管理函数实现 **********/

/**
 * @brief 初始化ADC采样
 */
static uint8_t pst_init_adc_sampling(pst_phase_t phase)
{
    if (phase >= PST_PHASE_COUNT) {
        return 0;
    }

    // 重置采样状态
    g_adc_sampling_active = 1;
    g_adc_sample_count = 0;
    g_current_test_phase = phase;
    memset(&g_adc_buffer, 0, sizeof(pst_adc_samples_t));

    // 确保ADC模块已初始化
    if (!ADC_IS_INITIALIZED()) {
        g_adc_sampling_active = 0;
        return 0;
    }

    return 1;
}

/**
 * @brief 清理ADC采样
 */
static void pst_cleanup_adc_sampling(void)
{
    g_adc_sampling_active = 0;
    g_adc_sample_count = 0;
}

/**
 * @brief 收集单次ADC采样
 * @note 支持两种模式：完整系统模式（真实ADC数据）和仅核心板模式（模拟数据）
 */
static uint8_t pst_collect_adc_sample(void)
{
    if (!g_adc_sampling_active || g_adc_sample_count >= PST_ADC_SAMPLE_COUNT) {
        return 0;
    }

#if (POWER_STAGE_TEST_MODE == PST_MODE_FULL_SYSTEM)
    // 完整系统模式：使用真实的ADC硬件数据

    // 检查ADC计算是否完成
    if (!PST_IS_PWM_CYCLE_COMPLETED()) {
        return 0;  // ADC数据尚未更新
    }

    // 直接从adc_pmsm模块获取滤波后的三相电流数据
    float ia = ADC_GET_IA_FILTERED();
    float ib = ADC_GET_IB_FILTERED();
    float ic = ADC_GET_IC_FILTERED();
    uint32_t tick = PST_GET_PWM_CYCLE_TICK();

#else
    // 仅核心板模式：生成可预测的模拟电流数据
    // 这允许在没有真实硬件的情况下测试数据分析算法

    // 生成简单的模拟电流值：
    // - 基础值接近零，模拟无负载状态
    // - 添加微小的递增值，模拟采样变化
    // - 三相之间有轻微差异，用于测试一致性分析
    static float base_current = 0.01f;  // 基础电流值 (A)
    float increment = (float)g_adc_sample_count * 0.001f;  // 递增值

    float ia = base_current + increment;
    float ib = base_current + increment + 0.002f;  // 轻微偏差
    float ic = base_current + increment - 0.001f;  // 轻微偏差
    uint32_t tick = Get_Runtime_Ms() * 1000 + g_adc_sample_count * 100;  // 模拟时间戳

    // 模拟PWM周期完成的延时
    pst_delay_us(50);  // 模拟ADC采样时间
#endif

    // 存储采样数据（两种模式共用）
    g_adc_buffer.ia_samples[g_adc_sample_count] = ia;
    g_adc_buffer.ib_samples[g_adc_sample_count] = ib;
    g_adc_buffer.ic_samples[g_adc_sample_count] = ic;
    g_adc_buffer.sample_tick[g_adc_sample_count] = tick;
    g_adc_sample_count++;
    g_adc_buffer.sample_count = g_adc_sample_count;

    return 1;
}

/**
 * @brief ADC采样回调函数（可在ADC中断中调用）
 */
static void pst_adc_sample_callback(void)
{
    if (g_adc_sampling_active) {
        pst_collect_adc_sample();
    }
}

/********** 电流一致性分析算法实现 **********/

/**
 * @brief 分析电流采样数据
 */
static uint8_t pst_analyze_current_samples(pst_phase_t phase)
{
    if (phase >= PST_PHASE_COUNT) {
        return 0;
    }

    pst_adc_samples_t *samples = &g_test_result.adc_samples[phase];
    pst_current_analysis_t *analysis = &g_test_result.current_analysis[phase];

    if (samples->sample_count == 0) {
        return 0;
    }

    // 计算各相平均值
    analysis->ia_avg = pst_calculate_average(samples->ia_samples, samples->sample_count);
    analysis->ib_avg = pst_calculate_average(samples->ib_samples, samples->sample_count);
    analysis->ic_avg = pst_calculate_average(samples->ic_samples, samples->sample_count);

    // 计算各相最大偏差
    analysis->ia_max_dev = pst_calculate_max_deviation(samples->ia_samples, samples->sample_count, analysis->ia_avg);
    analysis->ib_max_dev = pst_calculate_max_deviation(samples->ib_samples, samples->sample_count, analysis->ib_avg);
    analysis->ic_max_dev = pst_calculate_max_deviation(samples->ic_samples, samples->sample_count, analysis->ic_avg);

    // 计算三相间最大偏差
    float phase_avgs[3] = {analysis->ia_avg, analysis->ib_avg, analysis->ic_avg};
    float max_avg = phase_avgs[0];
    float min_avg = phase_avgs[0];

    for (int i = 1; i < 3; i++) {
        if (phase_avgs[i] > max_avg) max_avg = phase_avgs[i];
        if (phase_avgs[i] < min_avg) min_avg = phase_avgs[i];
    }
    analysis->max_phase_deviation = max_avg - min_avg;

    // 计算一致性因子
    analysis->consistency_factor = pst_calculate_consistency_factor(analysis);

    // 判断是否一致
    analysis->is_consistent = (analysis->max_phase_deviation <= PST_CURRENT_CONSISTENCY_THRESHOLD) ? 1 : 0;

    return 1;
}

/**
 * @brief 计算平均值
 */
static float pst_calculate_average(const float *samples, uint8_t count)
{
    if (count == 0) return 0.0f;

    float sum = 0.0f;
    for (uint8_t i = 0; i < count; i++) {
        sum += samples[i];
    }

    return sum / count;
}

/**
 * @brief 计算最大偏差
 */
static float pst_calculate_max_deviation(const float *samples, uint8_t count, float average)
{
    if (count == 0) return 0.0f;

    float max_dev = 0.0f;
    for (uint8_t i = 0; i < count; i++) {
        float dev = fabsf(samples[i] - average);
        if (dev > max_dev) {
            max_dev = dev;
        }
    }

    return max_dev;
}

/**
 * @brief 计算一致性因子
 */
static float pst_calculate_consistency_factor(const pst_current_analysis_t *analysis)
{
    // 基于最大偏差计算一致性因子
    float max_individual_dev = analysis->ia_max_dev;
    if (analysis->ib_max_dev > max_individual_dev) max_individual_dev = analysis->ib_max_dev;
    if (analysis->ic_max_dev > max_individual_dev) max_individual_dev = analysis->ic_max_dev;

    // 综合考虑个体偏差和相间偏差
    float total_deviation = max_individual_dev + analysis->max_phase_deviation;

    // 将偏差转换为一致性因子 (0-1)
    if (total_deviation <= 0.001f) {
        return 1.0f;  // 完全一致
    }

    float consistency = 1.0f - (total_deviation / (PST_CURRENT_CONSISTENCY_THRESHOLD * 2.0f));

    // 限制在0-1范围内
    if (consistency < 0.0f) consistency = 0.0f;
    if (consistency > 1.0f) consistency = 1.0f;

    return consistency;
}

/**
 * @brief 检测软故障
 */
static uint8_t pst_detect_soft_faults(const pst_current_analysis_t *analysis)
{
    // 检查是否存在软故障
    if (analysis->ia_max_dev > PST_SOFT_FAULT_THRESHOLD ||
        analysis->ib_max_dev > PST_SOFT_FAULT_THRESHOLD ||
        analysis->ic_max_dev > PST_SOFT_FAULT_THRESHOLD ||
        analysis->max_phase_deviation > PST_SOFT_FAULT_THRESHOLD) {
        return 1;  // 检测到软故障
    }

    return 0;  // 无软故障
}

/********** 安全保护函数实现 **********/

/**
 * @brief 验证硬件刹车功能
 */
static uint8_t pst_verify_brake_function(void)
{
    // 具体的硬件刹车功能验证代码

    // 简单的验证：检查是否能清除和检测刹车标志
    PST_TMR_CLEAR_BRK_FLAG();

    // 如果刹车功能正常，标志应该被清除
    if (PST_TMR_GET_BRK_FLAG() == RESET) {
        return 1;  // 刹车功能正常
    }

    return 0;  // 刹车功能异常
}

/**
 * @brief 紧急关断
 */
static void pst_emergency_shutdown(void)
{
    // 立即关闭所有PWM输出
    pst_clear_all_pwm();

    // 禁用PWM输出
    PST_TMR_OUTPUT_DISABLE();

    // 停止定时器
    PST_TMR_COUNTER_DISABLE();

    // 停止ADC采样
    pst_cleanup_adc_sampling();
}

/**
 * @brief 测试PWM周期计次机制
 * @param test_cycles 测试的周期数
 * @return 1: 测试通过, 0: 测试失败
 * @note 此函数用于验证新的基于PWM周期计次的延时机制是否正常工作
 */
uint8_t Power_Stage_Test_PWMCycleTiming(uint8_t test_cycles)
{
    if (test_cycles == 0) {
        return 1;
    }

    // 记录开始时的计次值
    uint32_t start_tick = PST_GET_PWM_CYCLE_TICK();

    // 等待指定的PWM周期数
    if (!pst_wait_for_pwm_cycles(test_cycles, PST_PWM_CYCLE_TIMEOUT_MS)) {
        return 0;  // 等待超时
    }

    // 检查计次值是否正确增加
    uint32_t end_tick = PST_GET_PWM_CYCLE_TICK();
    uint32_t actual_cycles = end_tick - start_tick;

    // 允许±1的误差范围
    if (actual_cycles >= test_cycles && actual_cycles <= (test_cycles + 2)) {
        return 1;  // 测试通过
    }

    return 0;  // 计次不准确
}



/**
 * @brief 等待ADC采样完成
 * @param timeout_ms 超时时间（毫秒）
 * @return 1: 采样完成, 0: 超时
 */
static uint8_t pst_wait_for_adc_samples(uint32_t timeout_ms)
{
    uint32_t start_time = Get_Runtime_Ms();

    while (g_adc_sampling_active && g_adc_sample_count < PST_ADC_SAMPLE_COUNT) {
        // 检查超时
        if ((Get_Runtime_Ms() - start_time) >= timeout_ms) {
            return 0;  // 超时
        }

        // 短暂延时，避免占用过多CPU
        pst_delay_us(100);
    }

    return (g_adc_sample_count >= PST_ADC_SAMPLE_COUNT) ? 1 : 0;
}

/**
 * @brief 微秒级延时函数
 * @param us 延时时间（微秒）
 * @note 使用系统提供的微秒延时函数
 */
static void pst_delay_us(uint32_t us)
{
    wk_delay_us(us);
}

/**
 * @brief 获取当前测试模式
 * @return PST_MODE_FULL_SYSTEM 或 PST_MODE_CORE_BOARD_ONLY
 */
uint8_t Power_Stage_Test_GetMode(void)
{
    return POWER_STAGE_TEST_MODE;
}

/**
 * @brief 获取当前测试模式的描述字符串
 * @return 模式描述字符串
 */
const char* Power_Stage_Test_GetModeString(void)
{
#if (POWER_STAGE_TEST_MODE == PST_MODE_FULL_SYSTEM)
    return "完整系统模式 (Full System Mode)";
#else
    return "仅核心板模式 (Core Board Only Mode)";
#endif
}

