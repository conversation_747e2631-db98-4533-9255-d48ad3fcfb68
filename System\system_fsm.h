/**
 * @file system_fsm.h
 * @brief 高可靠性驱动器系统状态机头文件
 * @version 1.0
 * @date 2024-12-12
 * 
 * @details 
 * 本模块实现了一个高可靠性驱动器的完整生命周期状态机，包括：
 * - 上电初始化和自检
 * - 正常操作环路（空闲-运行-停止）
 * - 故障处理和恢复
 * - 基于轮询的状态切换机制
 * 
 * 状态机特点：
 * - 1ms周期轮询调用
 * - 全局故障监控和处理
 * - 可靠的状态切换逻辑
 * - 详细的状态信息记录
 */

#ifndef __SYSTEM_FSM_H
#define __SYSTEM_FSM_H

#include "at32a403a_wk_config.h"
#include "system_status.h"

/********** 状态机配置宏 **********/
#define SYS_FSM_VERSION_MAJOR       1
#define SYS_FSM_VERSION_MINOR       0

// 状态机时序配置（单位：ms）
#define SYS_FSM_INIT_TIMEOUT        5000    // 初始化超时时间
#define SYS_FSM_SELF_CHECK_TIMEOUT  3000    // 自检超时时间
#define SYS_FSM_STOPPING_TIMEOUT    2000    // 停止超时时间
#define SYS_FSM_FAULT_RESET_DELAY   1000    // 故障复位延时

// 状态机调用周期
#define SYS_FSM_CALL_PERIOD_MS      1       // 1ms调用周期

/********** 状态机状态枚举 **********/
/**
 * @brief 系统状态机状态枚举
 */
typedef enum {
    SYS_FSM_STATE_POWER_ON_INIT = 0,    // 上电初始化状态
    SYS_FSM_STATE_SELF_CHECK,           // 自检状态
    SYS_FSM_STATE_IDLE,                 // 空闲状态
    SYS_FSM_STATE_RUNNING,              // 运行状态
    SYS_FSM_STATE_STOPPING,             // 停止状态
    SYS_FSM_STATE_FAULT,                // 故障状态
    SYS_FSM_STATE_COUNT                 // 状态总数
} sys_fsm_state_t;

/********** 状态机命令枚举 **********/
/**
 * @brief 系统状态机命令枚举
 */
typedef enum {
    SYS_FSM_CMD_NONE = 0,               // 无命令
    SYS_FSM_CMD_START,                  // 启动命令
    SYS_FSM_CMD_STOP,                   // 停止命令
    SYS_FSM_CMD_FAULT_RESET,            // 故障复位命令
    SYS_FSM_CMD_EMERGENCY_STOP,         // 紧急停止命令
    SYS_FSM_CMD_COUNT                   // 命令总数
} sys_fsm_cmd_t;

/********** 状态机事件枚举 **********/
/**
 * @brief 系统状态机内部事件枚举
 */
typedef enum {
    SYS_FSM_EVENT_NONE = 0,             // 无事件
    SYS_FSM_EVENT_INIT_COMPLETE,        // 初始化完成事件
    SYS_FSM_EVENT_INIT_FAILED,          // 初始化失败事件
    SYS_FSM_EVENT_SELF_CHECK_PASS,      // 自检通过事件
    SYS_FSM_EVENT_SELF_CHECK_FAIL,      // 自检失败事件
    SYS_FSM_EVENT_MOTOR_STOPPED,        // 电机完全停止事件
    SYS_FSM_EVENT_FAULT_DETECTED,       // 故障检测事件
    SYS_FSM_EVENT_TIMEOUT,              // 超时事件
    SYS_FSM_EVENT_COUNT                 // 事件总数
} sys_fsm_event_t;

/********** 状态机数据结构 **********/
/**
 * @brief 状态机统计信息结构
 */
typedef struct {
    uint32_t state_enter_count[SYS_FSM_STATE_COUNT];   // 各状态进入次数
    uint32_t state_duration_ms[SYS_FSM_STATE_COUNT];   // 各状态持续时间
    uint32_t cmd_received_count[SYS_FSM_CMD_COUNT];    // 各命令接收次数
    uint32_t event_triggered_count[SYS_FSM_EVENT_COUNT]; // 各事件触发次数
    uint32_t fault_count;                               // 故障总次数
    uint32_t total_runtime_ms;                          // 系统总运行时间
} sys_fsm_statistics_t;

/**
 * @brief 状态机控制块结构
 */
typedef struct {
    // 状态信息
    sys_fsm_state_t current_state;          // 当前状态
    sys_fsm_state_t previous_state;         // 前一个状态
    uint32_t state_enter_time_ms;           // 当前状态进入时间
    uint32_t state_duration_ms;             // 当前状态持续时间
    
    // 命令和事件
    sys_fsm_cmd_t pending_cmd;              // 待处理命令
    sys_fsm_event_t pending_event;          // 待处理事件
    
    // 故障信息
    sys_status_t last_fault_code;           // 最后一次故障代码
    uint32_t fault_time_ms;                 // 故障发生时间
    
    // 系统时间
    uint32_t system_time_ms;                // 系统运行时间（1ms递增）
    
    // 统计信息
    sys_fsm_statistics_t statistics;        // 状态机统计信息
    
    // 初始化标志
    uint8_t initialized;                    // 状态机初始化标志
} sys_fsm_control_t;

/********** 全局变量声明 **********/
extern sys_fsm_control_t g_sys_fsm;

/********** 公开API函数声明 **********/

/**
 * @brief 初始化系统状态机
 * @return SYS_STATUS_OK: 成功, 其他: 失败
 */
sys_status_t SYS_FSM_Init(void);

/**
 * @brief 状态机主处理函数
 * @note 需要每1ms调用一次
 */
void SYS_FSM_Process(void);

/**
 * @brief 发送命令到状态机
 * @param cmd 命令类型
 * @return SYS_STATUS_OK: 成功, 其他: 失败
 */
sys_status_t SYS_FSM_SendCommand(sys_fsm_cmd_t cmd);

/**
 * @brief 触发内部事件
 * @param event 事件类型
 * @return SYS_STATUS_OK: 成功, 其他: 失败
 */
sys_status_t SYS_FSM_TriggerEvent(sys_fsm_event_t event);

/**
 * @brief 获取当前状态
 * @return 当前状态
 */
sys_fsm_state_t SYS_FSM_GetCurrentState(void);

/**
 * @brief 获取状态名称字符串
 * @param state 状态
 * @return 状态名称字符串
 */
const char* SYS_FSM_GetStateName(sys_fsm_state_t state);

/**
 * @brief 获取命令名称字符串
 * @param cmd 命令
 * @return 命令名称字符串
 */
const char* SYS_FSM_GetCommandName(sys_fsm_cmd_t cmd);

/**
 * @brief 获取事件名称字符串
 * @param event 事件
 * @return 事件名称字符串
 */
const char* SYS_FSM_GetEventName(sys_fsm_event_t event);

/**
 * @brief 检查状态机是否处于运行状态
 * @return 1: 运行中, 0: 未运行
 */
uint8_t SYS_FSM_IsRunning(void);

/**
 * @brief 检查状态机是否处于故障状态
 * @return 1: 故障状态, 0: 正常状态
 */
uint8_t SYS_FSM_IsFaulted(void);

/**
 * @brief 获取最后一次故障代码
 * @return 故障代码
 */
sys_status_t SYS_FSM_GetLastFaultCode(void);

/**
 * @brief 获取状态机统计信息
 * @return 统计信息结构体指针
 */
const sys_fsm_statistics_t* SYS_FSM_GetStatistics(void);

/**
 * @brief 复位状态机统计信息
 */
void SYS_FSM_ResetStatistics(void);

#endif /* __SYSTEM_FSM_H */
